# Sub-Task Context: Implement Image Generation Fix

**Parent Task:** `ROO#TASK_20250614201649_D4E5`
**Main Plan Overview:** `[Link to Main Plan](../../../plans/ROO#TASK_20250614201649_D4E5_plan_overview.md)`
**Depends On:** `ROO#SUB_D4E5-ANALYZE_S001_20250614201725_A1B2`

## Goal for <PERSON><PERSON> (rooroo-developer)
Implement the code changes required to fix the image editing feature, based on the findings from the analysis task.

## Input Artifact
The primary input for this task is the analysis report generated by the `rooroo-analyzer`. You will find it here:
*   **Analysis Report:** `../../ROO#SUB_D4E5-ANALYZE_S001_20250614201725_A1B2/analysis_report.md`

You must read and fully understand the report's conclusion and recommendations before starting work.

## Task
1.  Apply the specific fixes recommended in the `analysis_report.md`.
2.  Ensure the changes are clean, follow existing code patterns, and do not introduce new errors.

## Output Artifact
Your output is the set of modified code files. The `rooroo-navigator` will automatically track the files you modify. There is no need to create a separate report.