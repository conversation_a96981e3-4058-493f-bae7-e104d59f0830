# Task Context: Implement Pre-Prompt Pills

**Goal:** Implement 'Idea 1: Pills' for pre-prompts. This involves adding a row of clickable icons/tags below the main prompt input field. Clicking a pill should append its corresponding text to the prompt. The implementation should be primarily on the frontend, with prompts and icons initially hardcoded.

**Relevant Files:**
*   [`frontend/index.html`](frontend/index.html): The main HTML structure where the new UI elements will be added.
*   [`frontend/assets/css/style.css`](frontend/assets/css/style.css): The stylesheet for styling the new "pill" buttons.
*   [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js): The JavaScript file where the click handling logic for the pills should be implemented.
*   [`frontend/assets/js/formHandlers.js`](frontend/assets/js/formHandlers.js): This might be relevant for interacting with the prompt input field.

**Requirements:**
1.  Add a container for the "pills" below the "Edit Prompt" input field in `index.html`.
2.  Initially, hardcode 4-8 "pills" with icons and corresponding prompt text. The icons should be thematically related to the prompt text.
3.  Style the pills to be easily clickable and visually distinct.
4.  Implement the JavaScript logic in `ui.js` to handle clicks on the pills.
5.  When a pill is clicked, its associated text should be appended to the current value of the prompt input field.