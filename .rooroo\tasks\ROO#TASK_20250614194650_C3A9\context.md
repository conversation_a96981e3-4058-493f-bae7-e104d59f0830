# Task Context: Fix "Quick Prompts" Dropdown

**Goal:** Fix the non-opening 'Quick Prompts' dropdown in the Kontext Pro interface.

**Analysis:**
The dropdown appears to be implemented as a collapsible section. The controlling JavaScript logic is likely in `setupCollapsiblePrompts()` within `frontend/assets/js/ui.js`. This function is not being correctly initialized from `frontend/assets/js/main.js` because it is not exported on the global `window.ui` object.

**Relevant Files:**
*   [`frontend/index.html`](frontend/index.html) - Contains the HTML structure for the dropdown.
*   [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js) - Contains the `setupCollapsiblePrompts` function definition.
*   [`frontend/assets/js/main.js`](frontend/assets/js/main.js) - Where the initialization call is made.

**Action:**
Modify `frontend/assets/js/ui.js` to correctly export the `setupCollapsiblePrompts` function so it can be called from `main.js`.