# Task Context: Fix Frontend to Handle Inconsistent API Response Structures

**CRITICAL PROBLEM:** The backend API is returning successful responses with two different JSON structures. The frontend code is not robust enough to handle this inconsistency, causing the image display to fail intermittently.

**Goal:** Make the frontend image display logic resilient to both known successful response structures.

---

### Evidence: Two Inconsistent but Valid JSON Responses

**Structure 1 (deeper nesting):**
The path to the URL is `response.data.data.images[0].url`.
```json
{
    "success": true,
    "data": {
        "data": {
            "images": [
                { "url": "..." }
            ]
        }
    }
}
```

**Structure 2 (shallower nesting):**
The path to the URL is `response.data.images[0].url`.
```json
{
    "data": {
        "images": [
            { "url": "..." }
        ]
    }
}
```

---

**Required Fix:**

The developer must modify the JavaScript function responsible for displaying the result image (likely `displayGeneratedImages` in [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js)).

The logic must be updated to:
1.  First, try to access the image URL via the deeper path: `response.data.data.images[0].url`.
2.  If that path is undefined or throws an error, it must then try to access the URL via the shallower path: `response.data.images[0].url`.
3.  This will ensure that regardless of which structure the API returns, the image will be correctly displayed.