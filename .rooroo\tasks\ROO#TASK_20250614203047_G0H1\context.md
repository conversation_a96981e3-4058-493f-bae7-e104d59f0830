# Task Context: Definitive Fix for `displayKontextEditedImage`

**CRITICAL PROBLEM:** The function `displayKontextEditedImage` in [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js) is the source of the failure. It only checks for one of the two possible JSON structures in the API response.

**Evidence:**
1.  The user's console log screenshot clearly shows the API response.
2.  The code in `ui.js` at line 514 (`function displayKontextEditedImage(resultData)`) has flawed logic that does not account for both response structures.

**Required Action:**
You must **completely replace** the existing `displayKontextEditedImage` function in [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js) with the following robust version. This new version correctly checks for both nested and shallow response structures.

**REPLACEMENT CODE:**
```javascript
function displayKontextEditedImage(resultData) {
    console.log("Displaying Kontext edited image with robust logic:", resultData);
    const imageElement = document.getElementById('kontextGeneratedImage');
    const placeholder = document.getElementById('kontextImagePlaceholder');
    const downloadBtnContainer = document.getElementById('kontextDownloadBtn');

    if (!imageElement || !placeholder) {
        console.error("Required UI elements for Kontext image display are missing.");
        return;
    }

    let imageUrl;

    // Structure 1: Deeper nesting (response.data.data.images)
    if (resultData?.data?.data?.images?.[0]?.url) {
        imageUrl = resultData.data.data.images[0].url;
        console.log("Image URL found via Structure 1 (deep nesting).");
    } 
    // Structure 2: Shallower nesting (response.data.images)
    else if (resultData?.data?.images?.[0]?.url) {
        imageUrl = resultData.data.images[0].url;
        console.log("Image URL found via Structure 2 (shallow nesting).");
    }

    if (imageUrl) {
        imageElement.src = imageUrl;
        placeholder.classList.add('hidden');
        imageElement.classList.remove('hidden');

        // Setup lightbox and download
        imageElement.onclick = () => window.lightbox?.openLightbox(imageUrl, "Kontext Edited Image");
        if (downloadBtnContainer) {
            downloadBtnContainer.classList.remove('hidden');
            downloadBtnContainer.onclick = () => window.imageHandlers?.handleDownloadSpecificImage(imageUrl, `kontext_edited_${Date.now()}.jpg`);
        }
    } else {
        addLogMessage("Could not find a valid image URL in the Kontext API response after checking all known structures.", "error");
        console.error("Failed to extract image URL from response:", resultData);
    }
}
```

**Goal:** Apply this exact code replacement to finally fix the image display issue.