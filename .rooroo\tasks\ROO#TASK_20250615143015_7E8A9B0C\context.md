# Task: Implement Tabbed Quick Prompts for Kontext Pro Interface

## User Request:

Reorganize the existing 8 "Quick Prompt" buttons in the `frontend/index.html` Kontext Pro interface (currently in a single row within a collapsible section around line 332) into a tabbed structure with three categories: "Body", "Style", and "Effects".

**Key Specifications:**

1.  **HTML Changes (in `frontend/index.html` around line 331-347):**
    *   Replace `div#kontext-prompt-pills` with a new structure containing:
        *   Tab headers: Three buttons for "Body", "Style", "Effects" with `data-tab-target` attributes.
        *   Tab content panels: Three divs (one for each category), initially one active and two hidden, containing the respective `prompt-pill` buttons.
    *   (Refer to the detailed HTML structure provided previously by the UI/UX Designer mode for exact markup).

2.  **CSS Styling (Tailwind CSS):**
    *   Style active/inactive tab buttons (e.g., `active text-blue-400 border-b-2 border-blue-500` for active).
    *   Manage visibility of content panels using `hidden` class.

3.  **JavaScript Functionality (in `frontend/assets/js/ui.js`):**
    *   Create `initializeKontextQuickPromptTabs()` function.
    *   Logic: On tab button click, update active styles on tab buttons and toggle `hidden` class on corresponding content panels based on `data-tab-target`.
    *   Initialization: Call this function from `frontend/assets/js/main.js` (e.g., within `DOMContentLoaded`). Expose the function via `window.ui`.

4.  **Accessibility:**
    *   Ensure proper ARIA roles (`tab`, `tablist`, `tabpanel`) and attributes (`aria-selected`, `aria-controls`, `aria-labelledby`) are used.
    *   Implement keyboard navigation for tabs.

**Detailed specifications (including exact HTML and JS logic concepts) were previously prepared by the UI/UX Designer mode and should be used as the primary reference for implementation.**

## Relevant Files:
*   Target HTML: [`frontend/index.html`](frontend/index.html)
*   Target JS (for new function): [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js)
*   Target JS (for initialization): [`frontend/assets/js/main.js`](frontend/assets/js/main.js)
*   Existing CSS (Tailwind is used): [`frontend/assets/css/style.css`](frontend/assets/css/style.css)

## Potentially Relevant Previous UI/UX Designer Task Contexts:
*   [Task Context 1](.rooroo/tasks/ROO#TASK_20250614192616_A4B1/context.md)
*   [Task Context 2](.rooroo/tasks/ROO#TASK_20250614194650_C3A9/context.md)
*   [Task Context 3](.rooroo/tasks/ROO#TASK_20250614195143_F4D3/context.md)

Please locate the detailed HTML and JS logic from the UI/UX Designer's output within these contexts or their associated artifacts.