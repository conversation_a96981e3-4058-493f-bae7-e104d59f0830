# Task: Implement JSON-based Storage for Quick Prompts

## User Request:
Based on previous brainstorming (ref: <PERSON><PERSON> Sparker session), the user has decided to implement a solution where Quick Prompts are stored in an external JSON file and dynamically loaded into the interface.

**Key Specifications:**

1.  **Create JSON Data File:**
    *   Create a new file at [`frontend/data/prompts.json`](frontend/data/prompts.json).
    *   The JSON structure should be an array of categories, where each category has an `id`, `category` (display name), and an array of `prompts` (each with `label` and `value`).
    *   Example structure:
        ```json
        [
          {
            "category": "Body",
            "id": "body-prompts",
            "prompts": [
              { "label": "Full Body", "value": "full body shot" },
              { "label": "Portrait", "value": "portrait" }
            ]
          },
          {
            "category": "Style",
            "id": "style-prompts",
            "prompts": [
              { "label": "Cinematic", "value": "cinematic" }
            ]
          },
          {
            "category": "Effects",
            "id": "effects-prompts",
            "prompts": [
              { "label": "God Rays", "value": "god rays" }
            ]
          }
        ]
        ```
    *   Populate this file with the existing 8 quick prompts, distributed appropriately across the "Body", "Style", and "Effects" categories.

2.  **JavaScript Logic (in [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js)):**
    *   Create a new function, e.g., `loadQuickPromptsFromJSON()`.
    *   This function should:
        *   Fetch the [`frontend/data/prompts.json`](frontend/data/prompts.json) file.
        *   Parse the JSON data.
        *   Dynamically generate the HTML for the tab headers (e.g., "Body", "Style", "Effects") and tab content panels. Each panel should contain the respective `prompt-pill` buttons based on the JSON data.
        *   The generated HTML structure should be compatible with the tab styling and functionality implemented in task `ROO#TASK_20250615143015_7E8A9B0C`.
        *   Insert the generated HTML into a designated container in [`frontend/index.html`](frontend/index.html) (e.g., a new `<div id="dynamic-quick-prompts-container"></div>`).
    *   Ensure that `initializeKontextQuickPromptTabs()` (from task `ROO#TASK_20250615143015_7E8A9B0C`) is called *after* the dynamic HTML content has been loaded and inserted into the DOM. This might involve calling it at the end of `loadQuickPromptsFromJSON()` or adapting its initialization.

3.  **HTML Changes (in [`frontend/index.html`](frontend/index.html)):**
    *   Remove the static HTML for the tabbed quick prompts (implemented in task `ROO#TASK_20250615143015_7E8A9B0C`).
    *   Add the new empty container element (e.g., `<div id="dynamic-quick-prompts-container"></div>`) where the JavaScript will inject the dynamically generated quick prompts. This container should be placed within the collapsible section where the prompts were previously.

4.  **Initialization (in [`frontend/assets/js/main.js`](frontend/assets/js/main.js)):**
    *   Call the new `loadQuickPromptsFromJSON()` function on `DOMContentLoaded` (or similar appropriate timing) to load and display the prompts.

**References:**
*   The tabbed UI structure and initial JavaScript (`initializeKontextQuickPromptTabs`) were implemented in task `ROO#TASK_20250615143015_7E8A9B0C`. The new dynamic generation should be compatible with this. Context: [`./.rooroo/tasks/ROO#TASK_20250615143015_7E8A9B0C/context.md`](./.rooroo/tasks/ROO#TASK_20250615143015_7E8A9B0C/context.md)