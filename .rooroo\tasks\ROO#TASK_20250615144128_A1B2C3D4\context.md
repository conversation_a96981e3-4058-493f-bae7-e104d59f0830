# Task: Debug and Fix Quick Prompt Tab Switching

## User Feedback:
The user reports that after the implementation of JSON-based storage for Quick Prompts (task `ROO#TASK_20250615143626_D4E5F6A1`), the tab switching functionality for these quick prompts is broken. Specifically, "switching to the second tab doesn't work."

## Goal:
Identify the cause of the bug in the Quick Prompt tab switching mechanism and implement a fix to ensure all tabs ("Body", "Style", "Effects") function correctly.

**Key Areas to Investigate:**

1.  **Event Listener Attachment:**
    *   Verify that event listeners for tab clicks are being correctly attached to the dynamically generated tab buttons in [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js) (likely within or related to `initializeQuickPromptTabs` or `loadQuickPrompts` functions).
    *   Ensure that the timing of event listener attachment is correct, i.e., after the dynamic HTML for tabs has been fully loaded and inserted into the DOM.

2.  **CSS Class Toggling:**
    *   Check the logic responsible for adding/removing `active` styles on tab buttons and toggling the `hidden` class on tab content panels.
    *   Ensure `data-tab-target` attributes are correctly used to link tab buttons to their respective content panels.

3.  **Selectors and DOM Structure:**
    *   Confirm that JavaScript selectors used to target tab buttons and content panels are accurate for the dynamically generated HTML structure.
    *   Inspect the generated HTML in the browser's developer tools to ensure it matches expectations and that IDs/classes are correct.

4.  **JavaScript Console Errors:**
    *   Check the browser's JavaScript console for any errors that might indicate the source of the problem.

**User Provided Console Errors (Added 2025-06-15):**
```
(index):64 cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation
ui.js:747 Uncaught ReferenceError: initializeKontextQuickPromptTabs is not defined
    at ui.js:747:5
main.js:8 Uncaught TypeError: Cannot read properties of undefined (reading 'setupSliderSync')
    at HTMLDocument.<anonymous> (main.js:8:15)
```
*The `initializeKontextQuickPromptTabs is not defined` error seems highly relevant to the reported tab switching issue.*
*The `setupSliderSync` error might be unrelated but should be noted.*

**Relevant Files (from previous task `ROO#TASK_20250615143626_D4E5F6A1`):**
*   Data: [`frontend/data/prompts.json`](frontend/data/prompts.json)
*   JavaScript (primary focus for bug): [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js)
*   HTML (container for dynamic content): [`frontend/index.html`](frontend/index.html)
*   JavaScript (initialization): [`frontend/assets/js/main.js`](frontend/assets/js/main.js)

**Reference Previous Tasks:**
*   Initial tabbed UI implementation: `ROO#TASK_20250615143015_7E8A9B0C`
*   JSON-based dynamic loading (where bug likely introduced): `ROO#TASK_20250615143626_D4E5F6A1` (Context: [`./.rooroo/tasks/ROO#TASK_20250615143626_D4E5F6A1/context.md`](./.rooroo/tasks/ROO#TASK_20250615143626_D4E5F6A1/context.md))

The developer should ensure that after the fix, clicking on any quick prompt tab correctly displays its content and hides the content of other tabs, and that the active tab styling is applied correctly.