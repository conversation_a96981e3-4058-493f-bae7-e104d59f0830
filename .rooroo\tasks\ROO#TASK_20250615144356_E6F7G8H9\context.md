# Task: Debug and Fix "window.ui.loadQuickPrompts is not a function" Error

## User Feedback & Console Errors:
After the previous fix (task `ROO#TASK_20250615144128_A1B2C3D4`), a new error has emerged:
```
(index):64 cdn.tailwindcss.com should not be used in production. To use Tailwind CSS in production, install it as a PostCSS plugin or use the Tailwind CLI: https://tailwindcss.com/docs/installation
ui.js:377 Switched to interface: fluxLorasInterface
main.js:12 Uncaught TypeError: window.ui.loadQuickPrompts is not a function
    at HTMLDocument.<anonymous> (main.js:12:15)
ui.js:377 Switched to interface: kontextProInterface
```
The critical error is `Uncaught TypeError: window.ui.loadQuickPrompts is not a function` originating from [`frontend/assets/js/main.js`](frontend/assets/js/main.js) at line 12.

## Goal:
Identify why `window.ui.loadQuickPrompts` is not recognized as a function and implement a fix. This function is expected to be defined and exposed in [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js) as per task `ROO#TASK_20250615143626_D4E5F6A1`.

**Key Areas to Investigate:**

1.  **Function Definition and Exposure in `ui.js`:**
    *   Verify that the `loadQuickPrompts` function is correctly defined in [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js).
    *   Ensure it's correctly added to the `window.ui` object for global access. Check for typos in the function name or the object key (e.g., `loadQuickPrompts` vs `loadquickprompts`).
    *   Confirm the structure of `window.ui = { ... }` in [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js) correctly includes `loadQuickPrompts`.

2.  **Function Call in `main.js`:**
    *   Confirm the call in [`frontend/assets/js/main.js`](frontend/assets/js/main.js) (around line 12) matches the exposed function name exactly: `window.ui.loadQuickPrompts()`.

3.  **Script Loading Order:**
    *   While less likely if other `window.ui` functions work, ensure [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js) is loaded and parsed before [`frontend/assets/js/main.js`](frontend/assets/js/main.js) attempts to call `window.ui.loadQuickPrompts()`.

**Relevant Files:**
*   JavaScript (where `loadQuickPrompts` should be defined/exposed): [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js)
*   JavaScript (where the error occurs): [`frontend/assets/js/main.js`](frontend/assets/js/main.js)
*   Data file for prompts: [`frontend/data/prompts.json`](frontend/data/prompts.json)

**Reference Previous Tasks:**
*   JSON-based dynamic loading (where `loadQuickPrompts` was introduced): `ROO#TASK_20250615143626_D4E5F6A1` (Context: [`./.rooroo/tasks/ROO#TASK_20250615143626_D4E5F6A1/context.md`](./.rooroo/tasks/ROO#TASK_20250615143626_D4E5F6A1/context.md))
*   Previous debugging task: `ROO#TASK_20250615144128_A1B2C3D4` (Context: [`./.rooroo/tasks/ROO#TASK_20250615144128_A1B2C3D4/context.md`](./.rooroo/tasks/ROO#TASK_20250615144128_A1B2C3D4/context.md))

The developer should ensure that `loadQuickPrompts` is correctly exposed and callable from `main.js`, resolving the TypeError.