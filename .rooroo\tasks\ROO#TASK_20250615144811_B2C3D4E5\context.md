# Task: Make Kontext Pro Panel Open by Default

## User Request:
The "Kontext Pro" panel (the collapsible section containing the Quick Prompts) should be open by default when the interface loads.

## Goal:
Modify the interface so that the Kontext Pro panel is expanded/visible upon initial page load.

**Key Actions:**

1.  **Identify Collapsible Panel Elements:**
    *   Locate the HTML elements in [`frontend/index.html`](frontend/index.html) that constitute the "Kontext Pro" collapsible panel. This includes the trigger button/element and the content area.
    *   The relevant section is likely around where the Quick Prompts container (`<div id="dynamic-quick-prompts-container"></div>`) is located, within a collapsible structure.

2.  **Modify Default State:**
    *   **Option A (HTML Change):** If the panel's collapsed state is controlled by a CSS class (e.g., `hidden`) on the content area and/or ARIA attributes (e.g., `aria-expanded="false"`) on the trigger, modify these in the static HTML of [`frontend/index.html`](frontend/index.html) to reflect an open state by default.
        *   For example, remove the `hidden` class from the content panel.
        *   Set `aria-expanded="true"` on the trigger.
        *   Adjust any other classes or attributes that control the visual "open" state (e.g., an icon on the trigger).
    *   **Option B (JavaScript Change):** If the panel's state is primarily managed by JavaScript (e.g., in [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js)), add or modify JavaScript logic to programmatically open/expand this panel when the page loads (e.g., within a `DOMContentLoaded` listener or as part of existing UI initialization). This might involve simulating a click on the trigger or directly calling a function that expands the panel.

3.  **Ensure Functionality:**
    *   Verify that the panel is indeed open when the page first loads.
    *   Confirm that the user can still manually collapse and expand the panel as before.

**Relevant Files:**
*   HTML Structure: [`frontend/index.html`](frontend/index.html)
*   JavaScript for UI interactions (if needed): [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js)
*   Main JavaScript for initialization (if JS approach taken): [`frontend/assets/js/main.js`](frontend/assets/js/main.js)

The developer should choose the simplest and most robust method (HTML or JS modification) to achieve the default open state while preserving existing toggle functionality.