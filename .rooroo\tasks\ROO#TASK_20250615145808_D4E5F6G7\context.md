# Task: Update `prompts.json` with Approved English Preprompts

## User Request:
The user has approved a new set of preprompts for the Quick Prompts feature. The existing content of [`frontend/data/prompts.json`](frontend/data/prompts.json) should be completely replaced with the new, fully English content.

## Goal:
Overwrite the content of the file [`frontend/data/prompts.json`](frontend/data/prompts.json) with the following JSON structure and data:

```json
[
  {
    "category": "Body",
    "id": "body-prompts",
    "prompts": [
      { "label": "Enlarge Breasts", "value": "Make breasts larger, maintaining natural look" },
      { "label": "Narrow Waist", "value": "Make waist narrower, keeping proportions realistic" },
      { "label": "Blonde Hair", "value": "Change hair color to blonde" }
    ]
  },
  {
    "category": "Style",
    "id": "style-prompts",
    "prompts": [
      { "label": "Black & White", "value": "Convert to black and white" },
      { "label": "Pencil Sketch", "value": "Transform to pencil sketch with cross-hatching" },
      { "label": "Cinematic", "value": "Change to a cinematic style with dramatic lighting and rich colors" }
    ]
  },
  {
    "category": "Effects",
    "id": "effects-prompts",
    "prompts": [
      { "label": "Sunset Sky", "value": "Change the sky to a dramatic sunset" },
      { "label": "God Rays", "value": "Add god rays emanating from a light source" },
      { "label": "Lens Flare", "value": "Add a subtle lens flare effect" }
    ]
  }
]
```

**Key Action:**
*   Use the `write_to_file` tool to replace the entire content of [`frontend/data/prompts.json`](frontend/data/prompts.json) with the exact JSON provided above. Ensure correct formatting and no extra characters.

**Relevant File:**
*   Target file to overwrite: [`frontend/data/prompts.json`](frontend/data/prompts.json)

This task ensures the quick prompts displayed in the UI reflect the user's latest approved set.