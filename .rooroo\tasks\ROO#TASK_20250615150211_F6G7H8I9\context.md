# Task: Force Overwrite `prompts.json` with EXACT Approved Content

## User Request & Issue:
Despite previous attempts, the file [`frontend/data/prompts.json`](frontend/data/prompts.json) still does not contain the specifically approved content. The current content is incorrect.

## Goal:
**Overwrite the entire content** of the file [`frontend/data/prompts.json`](frontend/data/prompts.json) with the following EXACT JSON structure and data. No other content should remain.

```json
[
  {
    "category": "Body",
    "id": "body-prompts",
    "prompts": [
      { "label": "Enlarge Breasts", "value": "Make breasts larger, maintaining natural look" },
      { "label": "Narrow Waist", "value": "Make waist narrower, keeping proportions realistic" },
      { "label": "Blonde Hair", "value": "Change hair color to blonde" }
    ]
  },
  {
    "category": "Style",
    "id": "style-prompts",
    "prompts": [
      { "label": "Black & White", "value": "Convert to black and white" },
      { "label": "Pencil Sketch", "value": "Transform to pencil sketch with cross-hatching" },
      { "label": "Cinematic", "value": "Change to a cinematic style with dramatic lighting and rich colors" }
    ]
  },
  {
    "category": "Effects",
    "id": "effects-prompts",
    "prompts": [
      { "label": "Sunset Sky", "value": "Change the sky to a dramatic sunset" },
      { "label": "God Rays", "value": "Add god rays emanating from a light source" },
      { "label": "Lens Flare", "value": "Add a subtle lens flare effect" }
    ]
  }
]
```

**CRITICAL Action:**
*   Use the `write_to_file` tool.
*   The `content` parameter for the tool MUST be the exact JSON string provided above.
*   The `path` parameter MUST be [`frontend/data/prompts.json`](frontend/data/prompts.json).
*   Ensure the file is completely overwritten, not appended to or merged.

**Relevant File:**
*   Target file to overwrite: [`frontend/data/prompts.json`](frontend/data/prompts.json)

This task is to correct a previous incorrect update and ensure the user's explicitly approved prompt set is implemented.