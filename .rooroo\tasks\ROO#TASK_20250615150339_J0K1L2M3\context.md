# Task: CRITICAL - Debug "undefined" Quick Prompt Buttons & Verify `prompts.json` Structure

## User Request & Issue:
The user reports that the quick prompt buttons in the UI are displaying "undefined" as their text.
**User Clarification:** While the `value` fields in [`frontend/data/prompts.json`](frontend/data/prompts.json) are correct, the user is **not sure** if other fields like `category`, `id`, and especially `label` are correctly named or present for all prompts.

This means the "undefined" button issue could stem from:
1.  Incorrect or missing `label` (or `category`/`id`) fields in the [`frontend/data/prompts.json`](frontend/data/prompts.json) file.
2.  JavaScript code in [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js) incorrectly parsing or accessing these fields.

## Goal:
1.  **First, verify and, if necessary, correct the structure of [`frontend/data/prompts.json`](frontend/data/prompts.json).** Ensure it precisely matches the agreed-upon structure below, particularly that each prompt object has a `label` field, and each category object has `category` and `id` fields.
2.  **Then, if the `prompts.json` file is confirmed or made correct, investigate the JavaScript code** in [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js) (likely `loadQuickPrompts` or similar) to ensure it correctly uses `promptObject.label` for button text and `categoryObject.category` / `categoryObject.id` for tab generation.
3.  Implement fixes in either `prompts.json` or `ui.js` (or both) to ensure buttons display correct labels.
4.  Verify that the UI buttons show the correct labels after the fix.

**Agreed-upon Target JSON structure for `frontend/data/prompts.json`:**
```json
[
  {
    "category": "Body",
    "id": "body-prompts",
    "prompts": [
      { "label": "Enlarge Breasts", "value": "Make breasts larger, maintaining natural look" },
      { "label": "Narrow Waist", "value": "Make waist narrower, keeping proportions realistic" },
      { "label": "Blonde Hair", "value": "Change hair color to blonde" }
    ]
  },
  {
    "category": "Style",
    "id": "style-prompts",
    "prompts": [
      { "label": "Black & White", "value": "Convert to black and white" },
      { "label": "Pencil Sketch", "value": "Transform to pencil sketch with cross-hatching" },
      { "label": "Cinematic", "value": "Change to a cinematic style with dramatic lighting and rich colors" }
    ]
  },
  {
    "category": "Effects",
    "id": "effects-prompts",
    "prompts": [
      { "label": "Sunset Sky", "value": "Change the sky to a dramatic sunset" },
      { "label": "God Rays", "value": "Add god rays emanating from a light source" },
      { "label": "Lens Flare", "value": "Add a subtle lens flare effect" }
    ]
  }
]
```

**CRITICAL Actions for Developer:**
1.  **PRIORITY 1: Inspect [`frontend/data/prompts.json`](frontend/data/prompts.json).**
    *   Compare its current content against the target JSON structure above.
    *   If it does not match (e.g., missing `label` fields, different property names for labels/categories), **use `write_to_file` to overwrite it with the correct target JSON provided above.**
2.  **PRIORITY 2: If `prompts.json` was correct or has been corrected, debug JavaScript in [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js).**
    *   Ensure the code correctly accesses `promptObject.label` for button text.
    *   Ensure category tabs are correctly generated using `categoryObject.category` and `categoryObject.id`.
3.  **Your report MUST explicitly state what was found in `prompts.json`, what changes (if any) were made to it, what (if any) issue was in the JavaScript, how it was fixed, AND that the UI buttons now show the correct labels.**

**Relevant Files:**
*   JSON Data File: [`frontend/data/prompts.json`](frontend/data/prompts.json)
*   JavaScript Rendering Prompts: [`frontend/assets/js/ui.js`](frontend/assets/js/ui.js)

This task is to URGENTLY correct the UI bug causing "undefined" buttons by first ensuring data integrity and then JavaScript logic.