/**
 * Configuration module for the application
 * Loads environment variables from .env file
 */
require('dotenv').config();

module.exports = {
    // Server configuration
    port: process.env.PORT || 3001,

    // Fal.ai API configuration
    falApiKey: process.env.FAL_KEY,

    // Default model configuration
    defaultModelId: "fal-ai/flux-lora",

    // Image size mapping for enum values (official API values)
    imageSizeMap: {
        "square_hd": "square_hd",
        "square": "square",
        "portrait_4_3": "portrait_4_3",
        "portrait_16_9": "portrait_16_9",
        "landscape_4_3": "landscape_4_3",
        "landscape_16_9": "landscape_16_9"
    },

    // UI display to API enum mapping
    uiToApiEnum: {
        "square_hd": "square_hd",
        "square": "square",
        "portrait_3_4": "portrait_4_3",  // UI shows 3:4 but API needs portrait_4_3
        "portrait_16_9": "portrait_16_9",
        "landscape_4_3": "landscape_4_3",
        "landscape_16_9": "landscape_16_9"
    },

    // Legacy aspect ratio to enum mapping (for backward compatibility)
    aspectRatioToEnum: {
        "3:4": "portrait_4_3",
        "1:1": "square",
        "4:3": "landscape_4_3",
        "16:9": "landscape_16_9"
    },

    // Pixel dimensions for each image size (for UI display)
    imageSizeDimensions: {
        "square_hd": { width: 1024, height: 1024 },
        "square": { width: 1024, height: 1024 },
        "portrait_4_3": { width: 768, height: 1024 },
        "portrait_16_9": { width: 576, height: 1024 },
        "landscape_4_3": { width: 1024, height: 768 },
        "landscape_16_9": { width: 1024, height: 576 }
    }
};
