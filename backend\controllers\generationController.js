/**
 * Controller for image generation endpoints
 */
const falService = require('../services/falService');
const { formatErrorResponse } = require('../utils/errorHandler');
const geminiService = require('../services/geminiService'); // Added for prompt improvement
const { validateImageGenerationRequest, validateImageEditingRequest, validateImprovePromptRequest } = require('../utils/validators'); // Added validateImageEditingRequest and validateImprovePromptRequest

/**
 * Handles image generation requests
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.handleImageGeneration = async (req, res) => {
    try {
        const inputData = req.body; 
        console.log("Received input data (for simple JSON response):", JSON.stringify(inputData, null, 2));
        
        // Validate input data
        const validation = validateImageGenerationRequest(inputData);
        if (!validation.isValid) {
            return res.status(400).json({
                success: false,
                message: 'Validation error',
                errors: validation.errors
            });
        }

        // Generate image
        const { result, duration } = await falService.generateImage(inputData);
        
        // Process result
        const processedResult = falService.processResult(result, duration);
        
        // Send response
        res.json(processedResult);

    } catch (error) {
        const errorResponse = formatErrorResponse(error);
        res.status(errorResponse.status).json(errorResponse);
    }
};

/**
 * Handles image editing requests using the Kontext model
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.processImageEditingRequest = async (req, res) => {
    try {
        const inputData = req.body;
        // console.log("Received input data for image editing:", JSON.stringify(inputData, null, 2)); // Optional: for debugging

        // Validate input data
        const validation = validateImageEditingRequest(inputData);
        if (!validation.isValid) {
            return res.status(400).json({
                success: false,
                message: 'Validation error for image editing',
                errors: validation.errors
            });
        }

        // Call Fal AI service for image editing
        // Assuming falService will have a method like callFalKontextEditAPI
        const result = await falService.callFalKontextEditAPI(inputData);
        
        // Send response (the result from Fal AI API is expected to be directly usable or require minimal processing)
        // The Fal AI SDK's subscribe method usually returns the direct output.
        res.json({ success: true, data: result });

    } catch (error) {
        const errorResponse = formatErrorResponse(error);
        res.status(errorResponse.status).json(errorResponse);
    }
};

/**
 * Handles prompt improvement requests using Gemini LLM.
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
exports.handleImprovePrompt = async (req, res) => {
    try {
        const { prompt, image, strictnessLevel, isAnatomical } = req.body; // Added isAnatomical

        // Validate input
        // Note: The validator might need an update if image presence/format, strictnessLevel, or isAnatomical needs validation.
        // For now, assuming client-side handles image validation primarily.
        // Consider adding strictnessLevel and isAnatomical to validation if they have specific allowed values/types.
        const validation = validateImprovePromptRequest({ prompt }); // Current validator only checks prompt
        if (!validation.isValid) {
            return res.status(400).json({
                success: false,
                message: 'Validation error for prompt improvement',
                errors: validation.errors
            });
        }

        // Call Gemini service to improve prompt, passing all relevant parameters
        const improvedPrompt = await geminiService.improvePrompt(prompt, image, strictnessLevel, isAnatomical);

        res.json({ success: true, improvedPrompt });

    } catch (error) {
        const errorResponse = formatErrorResponse(error);
        res.status(errorResponse.status).json(errorResponse);
    }
};
