/**
 * Routes for image generation API
 */
const express = require('express');
const router = express.Router();
const generationController = require('../controllers/generationController');

/**
 * POST /api/generate
 * Generates an image based on the provided parameters
 */
router.post('/generate', generationController.handleImageGeneration);

/**
 * POST /api/edit-image
 * Edits an image based on the provided image and prompt using the Kontext model.
 */
router.post('/edit-image', generationController.processImageEditingRequest);

/**
 * POST /api/improve-prompt
 * Improves a given prompt using Gemini LLM.
 */
router.post('/improve-prompt', generationController.handleImprovePrompt);

module.exports = router;
