/**
 * Main server file for the FalApiFlux application
 */
const express = require('express');
const cors = require('cors');
const path = require('path');
const config = require('./config/config');
const generationRoutes = require('./routes/generationRoutes');

// Initialize Express app
const app = express();

// Middleware
app.use(cors()); // Enable CORS for development
app.use(express.json({ limit: '50mb' })); // Parse JSON request bodies, increased limit for large image payloads
app.use(express.urlencoded({ limit: '50mb', extended: true })); // Also increase limit for URL-encoded payloads, just in case

// Serve static files from the frontend directory
app.use(express.static(path.join(__dirname, '../frontend')));

// API Routes
app.use('/api', generationRoutes); // Mount generation routes

// Serve the main HTML file for the root route
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '../frontend/index.html'));
});

// Start server
app.listen(config.port, () => {
    console.log(`Server running on http://localhost:${config.port}`);
});
