/**
 * Service for interacting with the Fal.ai API
 */
const { fal } = require('@fal-ai/client');
const config = require('../config/config');

/**
 * Prepares input data for Fal.ai API
 * @param {Object} inputData - User input data from frontend
 * @returns {Object} Formatted input for Fal.ai API
 */
function prepareFalInput(inputData) {
    const falInput = {
        prompt: inputData.prompt,
    };

    // Add image size
    if (inputData.imageSize) {
        // Case 1: imageSize is an object with width and height
        if (typeof inputData.imageSize === 'object' && inputData.imageSize.width && inputData.imageSize.height) {
            falInput.image_size = {
                width: parseInt(inputData.imageSize.width, 10),
                height: parseInt(inputData.imageSize.height, 10)
            };
        }
        // Case 2: imageSize is a string that matches one of the official API enum values
        else if (typeof inputData.imageSize === 'string' && config.imageSizeMap[inputData.imageSize]) {
            falInput.image_size = inputData.imageSize;
        }
        // Case 3: imageSize is a UI value that needs to be mapped to API enum
        else if (typeof inputData.imageSize === 'string' && config.uiToApiEnum[inputData.imageSize]) {
            const uiValue = inputData.imageSize;
            const apiValue = config.uiToApiEnum[inputData.imageSize];
            console.log(`Converting UI image size "${uiValue}" to API enum "${apiValue}"`);
            falInput.image_size = apiValue;
        }
        // Case 4: Legacy format - imageSize is a string in format "width:height"
        else if (typeof inputData.imageSize === 'string' && config.aspectRatioToEnum[inputData.imageSize]) {
            falInput.image_size = config.aspectRatioToEnum[inputData.imageSize];
        }
    }

    // Add other parameters
    if (inputData.numInferenceSteps) falInput.num_inference_steps = parseInt(inputData.numInferenceSteps, 10);
    if (inputData.seed && String(inputData.seed).toLowerCase() !== "random") {
        falInput.seed = parseInt(inputData.seed, 10);
    }
    if (inputData.loras && inputData.loras.length > 0) {
        falInput.loras = inputData.loras
            .filter(lora => lora.path && lora.path.trim() !== "")
            .map(lora => ({ path: lora.path, scale: parseFloat(lora.scale) }));
        if (falInput.loras.length === 0) delete falInput.loras;
    }
    if (inputData.guidanceScale) falInput.guidance_scale = parseFloat(inputData.guidanceScale);
    if (inputData.numImages) falInput.num_images = parseInt(inputData.numImages, 10);
    if (typeof inputData.safetyChecker === 'boolean') {
        falInput.enable_safety_checker = inputData.safetyChecker;
    }
    if (inputData.outputFormat) falInput.output_format = inputData.outputFormat;

    return falInput;
}

/**
 * Generates an image using Fal.ai API
 * @param {Object} inputData - User input data from frontend
 * @returns {Promise<Object>} Generated image data
 */
async function generateImage(inputData) {
    const modelId = inputData.form && inputData.form.trim() !== ""
        ? inputData.form
        : config.defaultModelId;

    const falInput = prepareFalInput(inputData);

    console.log("Sending to Fal.ai (Model ID: " + modelId + "):", JSON.stringify(falInput, null, 2));
    const startTime = Date.now();

    // Call Fal.ai API
    const result = await fal.run(modelId, {
        input: falInput,
    });

    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    console.log("Final result from Fal.ai (using fal.run):", JSON.stringify(result, null, 2));

    return {
        result,
        duration
    };
}

/**
 * Processes the result from Fal.ai API
 * @param {Object} result - Raw result from Fal.ai API
 * @param {number} duration - Duration of the API call in seconds
 * @returns {Object} Processed result for frontend
 */
function processResult(result, duration) {
    // The structure of `result` from `fal.run` can sometimes be nested under `data`
    // or be the data itself. Based on server logs, it IS nested under `data`.
    const falData = result && result.data ? result.data : (result || {});

    let isNsfwDetected = false;
    if (falData.has_nsfw_concepts && Array.isArray(falData.has_nsfw_concepts)) {
        isNsfwDetected = falData.has_nsfw_concepts.some(concept => concept === true);
    }

    const imageUrl = falData.images && falData.images.length > 0 ? falData.images[0].url : null;
    const contentType = falData.images && falData.images.length > 0 ? falData.images[0].content_type : null;
    const seedValue = falData.seed;

    return {
        success: true,
        imageUrl: imageUrl,
        contentType: contentType,
        seed: seedValue,
        isNsfwDetected: isNsfwDetected,
        requestInfo: `Your request took ${duration} seconds.`,
        falResult: result // Keep sending the full falResult for debugging/future use
    };
}

/**
 * Calls the Fal.ai API for image editing using the Kontext model.
 * @param {Object} inputData - User input data from frontend, including prompt and image_url.
 * @returns {Promise<Object>} The result from the Fal.ai API.
 */
async function callFalKontextEditAPI(inputData) {
    const modelId = "fal-ai/flux-pro/kontext"; // Specific model ID for Kontext

    // Prepare the input for the Kontext model
    // Based on the schema: prompt, image_url are primary.
    // Others like guidance_scale, num_images, safety_tolerance, output_format, aspect_ratio can be added.
    const falInput = {
        prompt: inputData.prompt,
        image_url: inputData.image_url, // This should be a base64 data URI or a publicly accessible URL
    };

    if (inputData.guidance_scale) falInput.guidance_scale = parseFloat(inputData.guidance_scale);
    if (inputData.num_images) falInput.num_images = parseInt(inputData.num_images, 10);
    if (inputData.safety_tolerance) falInput.safety_tolerance = String(inputData.safety_tolerance); // Enum, passed as string
    if (inputData.output_format) falInput.output_format = inputData.output_format; // Enum 'jpeg' or 'png'
    if (inputData.aspect_ratio) falInput.aspect_ratio = inputData.aspect_ratio; // Enum e.g., '1:1'
    if (inputData.seed && String(inputData.seed).toLowerCase() !== "random") {
        falInput.seed = parseInt(inputData.seed, 10);
    }
    // sync_mode is true by default for fal.subscribe if not specified, which is good for direct response.
    // If you want to use webhooks for very long edits, you'd use fal.queue.submit and set sync_mode: false

    console.log(`Sending to Fal.ai (Model ID: ${modelId}):`, JSON.stringify(falInput, null, 2));
    const startTime = Date.now();

    try {
        const result = await fal.subscribe(modelId, { // Using fal.subscribe as per Kontext docs
            input: falInput,
            logs: true, // Enable logs if needed for debugging during the process
            onQueueUpdate: (update) => { // Optional: for real-time logging
                if (update.status === "IN_PROGRESS") {
                    update.logs.map((log) => console.log(`[Kontext Update] ${log.message}`));
                } else if (update.status === "COMPLETED") {
                    console.log("[Kontext Update] Processing completed.");
                }
            },
        });

        const endTime = Date.now();
        const duration = ((endTime - startTime) / 1000).toFixed(2);
        console.log(`Result from Fal.ai (${modelId}) (took ${duration}s):`, JSON.stringify(result, null, 2));
        
        // The `result` from fal.subscribe is typically the direct output of the model.
        // It might be nested under `result.data` or just `result` itself.
        // The Kontext output schema shows "images", "timings", "seed", "has_nsfw_concepts", "prompt".
        // We assume `result` directly contains these. If it's nested under `result.data`, adjust accordingly.
        return result;

    } catch (error) {
        console.error(`Error calling Fal.ai Kontext API (${modelId}):`, error);
        throw error; // Re-throw to be caught by the controller
    }
}

module.exports = {
    generateImage,
    processResult,
    callFalKontextEditAPI // Export the new function
};
