const { GoogleGenerativeAI } = require("@google/generative-ai");
const fs = require('fs');
const path = require('path');

// TODO: Add GEMINI_API_KEY to .env file
const apiKey = process.env.GEMINI_API_KEY;
const geminiModelName = process.env.GEMINI_MODEL || "gemini-pro"; // Default to gemini-pro if not set
const thinkingBudget = parseInt(process.env.THINKING_BUDGET, 10) || 2048; // Default to 2048 if not set or invalid

let generalSystemPromptText = "Improve the following prompt for generating an image."; // Fallback
let anatomicalSystemPrompt_cautious = "DEFAULT CAUTIOUS ANATOMICAL PROMPT: Improve for safety and subtlety."; // Fallback
let anatomicalSystemPrompt_balanced = "DEFAULT BALANCED ANATOMICAL PROMPT: Improve for noticeable but natural changes."; // Fallback
let anatomicalSystemPrompt_explicit = "DEFAULT EXPLICIT ANATOMICAL PROMPT: Improve by directly translating user's explicit terms."; // Fallback
let anatomicalSystemPrompt_extreme = "DEFAULT EXTREME ANATOMICAL PROMPT: Translate user's most explicit NSFW terms with maximal directness, intensity, and strategic execution."; // Fallback

const loadPromptFromFile = (fileName, fallbackText) => {
  try {
    const filePath = path.join(__dirname, '../prompts', fileName);
    const content = fs.readFileSync(filePath, 'utf8');
    console.log(`Successfully loaded ${fileName}`);
    return content;
  } catch (error) {
    console.error(`Error loading ${fileName}:`, error);
    return fallbackText;
  }
};

generalSystemPromptText = loadPromptFromFile('generalSystemPrompt.md', generalSystemPromptText);
anatomicalSystemPrompt_cautious = loadPromptFromFile('anatomicalSystemPrompt_cautious.md', anatomicalSystemPrompt_cautious);
anatomicalSystemPrompt_balanced = loadPromptFromFile('anatomicalSystemPrompt_balanced.md', anatomicalSystemPrompt_balanced);
anatomicalSystemPrompt_explicit = loadPromptFromFile('anatomicalSystemPrompt_explicit.md', anatomicalSystemPrompt_explicit);
anatomicalSystemPrompt_extreme = loadPromptFromFile('anatomicalSystemPrompt_level4.md', anatomicalSystemPrompt_extreme);

let genAI;
let model;

if (apiKey) {
  genAI = new GoogleGenerativeAI(apiKey);
  model = genAI.getGenerativeModel({
    model: geminiModelName,
    // systemInstruction is now passed dynamically in generateContent
    generationConfig: {
      maxOutputTokens: thinkingBudget,
    }
  });
} else {
  console.warn("GEMINI_API_KEY not found in .env. GeminiService will not function.");
}

/**
 * Improves a given prompt using the Gemini LLM.
 * @param {string} userPrompt - The user's original prompt.
 * @param {{ mimeType: string, data: string } | null} [image] - Optional base64 encoded image with mimeType.
 * @param {string} [strictnessLevel='cautious'] - The desired strictness level from the frontend.
 * @param {boolean} [isAnatomical=false] - Flag indicating if the request is anatomical.
 * @returns {Promise<string>} - The improved prompt.
 * @throws {Error} If the API call fails or the API key is not configured.
 */
async function improvePrompt(userPrompt, image = null, strictnessLevel = 'cautious', isAnatomical = false) {
  console.log(`GeminiService: Received strictnessLevel: ${strictnessLevel}, isAnatomical: ${isAnatomical}`);
  if (!model) {
    throw new Error("Gemini AI model is not initialized. Check GEMINI_API_KEY.");
  }

  let chosenSystemPrompt;
  if (isAnatomical) {
    console.log(`Anatomical request. Strictness level: ${strictnessLevel}`);
    switch (strictnessLevel) {
      case 'extreme':
        chosenSystemPrompt = anatomicalSystemPrompt_extreme;
        console.log("Using ANATOMICAL system prompt (extreme).");
        break;
      case 'explicit':
        chosenSystemPrompt = anatomicalSystemPrompt_explicit;
        console.log("Using ANATOMICAL system prompt (explicit).");
        break;
      case 'balanced':
        chosenSystemPrompt = anatomicalSystemPrompt_balanced;
        console.log("Using ANATOMICAL system prompt (balanced).");
        break;
      case 'cautious':
      default:
        chosenSystemPrompt = anatomicalSystemPrompt_cautious;
        console.log("Using ANATOMICAL system prompt (cautious/default).");
        break;
    }
  } else {
    chosenSystemPrompt = generalSystemPromptText;
    console.log("Using GENERAL system prompt.");
  }
  
  const userMessageParts = [];

  if (image && image.mimeType && image.data) {
    userMessageParts.push({
      inlineData: {
        mimeType: image.mimeType,
        data: image.data,
      },
    });
  }
  
  // Pass the user's editing idea. If it's an anatomical request and we have specific strictness instructions,
  // those are now part of the chosenSystemPrompt.
  let finalUserMessageText = userPrompt;
  // If we decide to pass strictness to Gemini via user message for anatomical prompts:
  // if (isAnatomical) {
  //   finalUserMessageText = `Strictness Level: ${strictnessLevel}\nUser's Editing Idea: "${userPrompt}"`;
  // }
  userMessageParts.push({ text: finalUserMessageText });


  try {
    const requestForModelGenerateContent = {
      contents: [{ role: "user", parts: userMessageParts }],
      systemInstruction: { parts: [{ text: chosenSystemPrompt }] }
    };
    
    // console.log("Sending request to Gemini with system prompt:", chosenSystemPrompt);
    // console.log("Request payload:", JSON.stringify(requestForModelGenerateContent, null, 2));

    const result = await model.generateContent(requestForModelGenerateContent);
    const response = await result.response;

    // console.log("Full Gemini API response object:", JSON.stringify(response, null, 2));
    // if (response.promptFeedback) {
    //     console.log("Gemini API promptFeedback:", JSON.stringify(response.promptFeedback, null, 2));
    // }
    // if (response.candidates && response.candidates.length > 0 && response.candidates[0].safetyRatings) {
    //     console.log("Gemini API safetyRatings for first candidate:", JSON.stringify(response.candidates[0].safetyRatings, null, 2));
    // }

    const improvedText = response.text();
    // console.log("Text extracted from Gemini response:", improvedText);
    
    return improvedText.trim();
  } catch (error) {
    console.error("Error calling Gemini API:", error);
    throw new Error("Failed to improve prompt using Gemini API.");
  }
}

module.exports = {
  improvePrompt,
};