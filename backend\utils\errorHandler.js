/**
 * Error handling utilities
 */

/**
 * Formats error response for API
 * @param {Error} error - The error object
 * @returns {Object} Formatted error response
 */
function formatErrorResponse(error) {
    console.error("Error during image generation:", error);
    
    let errorMessage = "An error occurred during image generation.";
    if (error.message) errorMessage = error.message;
    
    // Check for Fal specific error structure
    if (error.status && error.title && error.detail) { // Fal error object structure
        errorMessage = `${error.title}: ${JSON.stringify(error.detail)}`;
    } else if (error.response && error.response.data && error.response.data.detail) { // Axios-like error
        errorMessage = typeof error.response.data.detail === 'string' 
            ? error.response.data.detail 
            : JSON.stringify(error.response.data.detail);
    }
    
    return {
        success: false,
        message: errorMessage,
        errorDetails: error.detail || error.response?.data || error.toString(),
        status: error.status || 500
    };
}

module.exports = {
    formatErrorResponse
};
