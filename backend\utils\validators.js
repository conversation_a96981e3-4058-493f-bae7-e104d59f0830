/**
 * Validation utilities for API requests
 */

/**
 * Validates image generation request data
 * @param {Object} data - Request data to validate
 * @returns {Object} Validation result with isValid flag and errors array
 */
function validateImageGenerationRequest(data) {
    const errors = [];
    
    // Check for required prompt
    if (!data.prompt || data.prompt.trim() === '') {
        errors.push('Prompt is required');
    }
    
    // Validate numeric values
    if (data.numInferenceSteps && (isNaN(data.numInferenceSteps) || data.numInferenceSteps < 1)) {
        errors.push('Number of inference steps must be a positive number');
    }
    
    if (data.guidanceScale && (isNaN(data.guidanceScale) || data.guidanceScale < 1)) {
        errors.push('Guidance scale must be a positive number');
    }
    
    if (data.numImages && (isNaN(data.numImages) || data.numImages < 1)) {
        errors.push('Number of images must be a positive number');
    }
    
    // Validate loras if present
    if (data.loras && Array.isArray(data.loras)) {
        data.loras.forEach((lora, index) => {
            if (!lora.path || lora.path.trim() === '') {
                errors.push(`LoRA at index ${index} is missing a path`);
            }
            if (isNaN(lora.scale) || lora.scale < 0 || lora.scale > 1) {
                errors.push(`LoRA at index ${index} has an invalid scale (must be between 0 and 1)`);
            }
        });
    }
    
    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Validates image editing request data for the Kontext model
 * @param {Object} data - Request data to validate
 * @returns {Object} Validation result with isValid flag and errors array
 */
function validateImageEditingRequest(data) {
    const errors = [];

    // Check for required prompt
    if (!data.prompt || String(data.prompt).trim() === '') {
        errors.push('Prompt is required for image editing.');
    }

    // Check for required image_url
    if (!data.image_url || String(data.image_url).trim() === '') {
        errors.push('Image URL (image_url) is required for image editing.');
    } else {
        // Basic check for data URI or https URL
        if (!String(data.image_url).startsWith('data:image/') && !String(data.image_url).startsWith('https://')) {
            errors.push('Image URL must be a valid data URI (e.g., data:image/png;base64,...) or an https URL.');
        }
    }

    // Validate optional numeric values
    if (data.guidance_scale && (isNaN(parseFloat(data.guidance_scale)) || parseFloat(data.guidance_scale) < 0)) {
        errors.push('Guidance scale must be a non-negative number.');
    }
    if (data.num_images && (isNaN(parseInt(data.num_images, 10)) || parseInt(data.num_images, 10) < 1)) {
        errors.push('Number of images must be a positive integer.');
    }
    if (data.seed && String(data.seed).toLowerCase() !== "random" && isNaN(parseInt(data.seed, 10))) {
        errors.push('Seed must be an integer or "random".');
    }

    // Validate enums if present
    const validOutputFormats = ['jpeg', 'png'];
    if (data.output_format && !validOutputFormats.includes(String(data.output_format).toLowerCase())) {
        errors.push(`Invalid output format. Must be one of: ${validOutputFormats.join(', ')}.`);
    }

    const validSafetyTolerances = ['1', '2', '3', '4', '5', '6'];
    if (data.safety_tolerance && !validSafetyTolerances.includes(String(data.safety_tolerance))) {
        errors.push(`Invalid safety tolerance. Must be one of: ${validSafetyTolerances.join(', ')}.`);
    }
    
    // Aspect ratio can be complex (e.g., "16:9", "1:1"). For now, we'll assume it's a string if provided.
    // More specific validation can be added if needed based on exact allowed values by Fal.ai.
    if (data.aspect_ratio && typeof data.aspect_ratio !== 'string') {
        errors.push('Aspect ratio must be a string (e.g., "16:9").');
    }


    return {
        isValid: errors.length === 0,
        errors
    };
}

/**
 * Validates prompt improvement request data
 * @param {Object} data - Request data to validate (should contain 'prompt')
 * @returns {Object} Validation result with isValid flag and errors array
 */
function validateImprovePromptRequest(data) {
    const errors = [];

    if (!data.prompt || typeof data.prompt !== 'string' || data.prompt.trim() === '') {
        errors.push('Prompt is required and must be a non-empty string.');
    }

    // Add any other specific validations for the prompt if needed, e.g., length limits.
    // For now, just checking for presence and type.

    return {
        isValid: errors.length === 0,
        errors
    };
}

module.exports = {
    validateImageGenerationRequest,
    validateImageEditingRequest, // Export the new function
    validateImprovePromptRequest
};
