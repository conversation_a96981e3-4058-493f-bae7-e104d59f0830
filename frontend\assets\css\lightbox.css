/* Lightbox styles */
#image-lightbox-modal {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.75);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    backdrop-filter: blur(5px);
    transition: opacity 0.3s ease;
    padding: 20px;
}

#image-lightbox-modal.hidden {
    opacity: 0;
    pointer-events: none;
}

#image-lightbox-modal:not(.hidden) {
    opacity: 1;
}

#image-lightbox-modal .relative {
    position: relative;
    background-color: var(--bg-secondary);
    padding: 0.5rem;
    border-radius: 0.75rem;
    box-shadow: var(--shadow);
    max-width: 95vw;
    max-height: 95vh;
    display: flex;
    flex-direction: column;
    border: 1px solid var(--border);
    overflow: visible;
    transition: transform 0.3s ease, opacity 0.3s ease;
    transform: scale(1);
    opacity: 1;
}

/* Для квадратних зображень оптимізуємо контейнер */
#image-lightbox-modal.square-mode .relative {
    max-width: min(90vw, 90vh);
    max-height: min(90vw, 90vh);
    aspect-ratio: 1/1;
    padding: 0.75rem;
}

#image-lightbox-modal.hidden .relative {
    transform: scale(0.95);
    opacity: 0;
}

#lightbox-image {
    object-fit: contain;
    width: auto;
    height: auto;
    max-width: 100%;
    max-height: calc(90vh - 16px);
    flex-grow: 1;
    background-color: var(--bg-secondary);
    border-radius: 6px;
    box-shadow: 0 3px 15px rgba(0, 0, 0, 0.3);
    margin: 0 auto;
}

/* Для квадратних зображень оптимізуємо розмір */
#lightbox-image.square-image {
    max-width: min(85vw, 85vh);
    max-height: min(85vw, 85vh);
    object-fit: contain;
}

#lightbox-download-btn,
#lightbox-close-btn {
    position: absolute;
    top: 0.75rem;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 9999px;
    width: 2rem;
    height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    color: white;
    z-index: 60;
    font-size: 0.875rem;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

#lightbox-download-btn {
    right: 3rem;
}

#lightbox-close-btn {
    right: 0.75rem;
}

#lightbox-download-btn:hover,
#lightbox-close-btn:hover {
    background-color: var(--accent);
    transform: scale(1.05);
    box-shadow: 0 0 8px rgba(142, 68, 173, 0.5);
    border-color: rgba(255, 255, 255, 0.2);
}

@media (min-width: 768px) {
    #image-lightbox-modal .relative {
        padding: 1rem;
        max-width: 90vw;
        max-height: 90vh;
    }

    #image-lightbox-modal.square-mode .relative {
        max-width: min(85vw, 85vh);
        max-height: min(85vw, 85vh);
        padding: 1rem;
    }

    #lightbox-image.square-image {
        max-width: min(80vw, 80vh);
        max-height: min(80vw, 80vh);
        object-fit: contain;
    }

    #lightbox-download-btn,
    #lightbox-close-btn {
        width: 2.5rem;
        height: 2.5rem;
        font-size: 1rem;
    }
}
