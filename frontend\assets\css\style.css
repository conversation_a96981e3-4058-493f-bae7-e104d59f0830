/* Custom CSS for elements that need more specific styling */
.dark-theme {
    --bg-primary: #0f0f0f;
    --bg-secondary: #1e1e1e;
    --bg-tertiary: #2a2a2a;
    --text-primary: #ffffff;
    --text-secondary: #a0a0a0;
    --accent: #8e44ad;
    --accent-hover: #9b59b6;
    --border: #3a3a3a;
    --shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

body {
    background-color: var(--bg-primary);
    color: var(--text-primary);
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    line-height: 1.5;
}

.panel {
    background-color: var(--bg-secondary);
    border-radius: 12px;
    box-shadow: var(--shadow);
    border: 1px solid var(--border);
    transition: box-shadow 0.3s ease-out; /* Додано для hover ефекту на панелі */
}

.panel:hover {
    box-shadow: 0 12px 35px rgba(0,0,0,0.15); /* Посилена тінь при наведенні */
}

.input-field {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border);
    color: var(--text-primary);
    transition: all 0.3s ease, transform 0.2s ease-out, box-shadow 0.2s ease-out; /* Додано transform та box-shadow */
    border-radius: 6px;
    font-size: 0.875rem;
}

.input-field:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.input-field:focus {
    border-color: var(--accent);
    outline: none;
    box-shadow: 0 0 0 2px rgba(142, 68, 173, 0.2), 0 2px 6px rgba(0,0,0,0.15); /* Покращено тінь при фокусі */
}

.slider {
    -webkit-appearance: none;
    appearance: none;
    width: 100%;
    height: 12px; /* Збільшуємо висоту треку для кращої помітності */
    border-radius: 6px; /* Оновлюємо радіус */
    background: linear-gradient(to right, var(--bg-tertiary) 0%, rgba(255, 255, 255, 0.15) 100%); /* Більш контрастний градієнт */
    outline: none;
    margin: 8px 0;
    accent-color: var(--accent); /* Акцентний колір для активної частини */
    border: 1px solid rgba(255,255,255,0.05); /* Тонка рамка для кращої видимості */
}

.slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 20px; /* Збільшуємо розмір ручки */
    height: 20px; /* Збільшуємо розмір ручки */
    border-radius: 50%;
    background: white !important; /* Біла ручка, !important для перевизначення accent-color */
    cursor: pointer;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4), 0 0 0 2px rgba(0,0,0,0.2); /* Більш виразна об'ємна тінь */
    transition: all 0.2s ease;
    margin-top: -4px; /* Центрування ручки відносно збільшеного треку */
}

.slider::-webkit-slider-thumb:hover {
    transform: scale(1.2); /* Збільшуємо ефект при наведенні */
    background: #f5f5f5 !important; /* Світліший колір при наведенні */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5), 0 0 0 2px rgba(0,0,0,0.25);
}

.slider::-moz-range-thumb {
    width: 20px; /* Збільшуємо розмір ручки */
    height: 20px; /* Збільшуємо розмір ручки */
    border-radius: 50%;
    background: white !important; /* Біла ручка, !important для перевизначення accent-color */
    cursor: pointer;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.4), 0 0 0 2px rgba(0,0,0,0.2); /* Більш виразна об'ємна тінь */
    transition: all 0.2s ease;
    border: none;
}

.slider::-moz-range-thumb:hover {
    transform: scale(1.2); /* Збільшуємо ефект при наведенні */
    background: #f5f5f5 !important; /* Світліший колір при наведенні */
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.5), 0 0 0 2px rgba(0,0,0,0.25);
}

.toggle-switch {
    position: relative;
    display: inline-block;
    width: 36px;
    height: 18px;
}

.toggle-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: var(--bg-tertiary);
    transition: .3s;
    border-radius: 18px;
    box-shadow: inset 0 0 3px rgba(0, 0, 0, 0.2);
}

.toggle-slider:before {
    position: absolute;
    content: "";
    height: 14px;
    width: 14px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .3s;
    border-radius: 50%;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
    background-color: var(--accent);
}

input:checked + .toggle-slider:before {
    transform: translateX(18px);
}

.image-container {
    position: relative;
    background-color: var(--bg-tertiary);
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid var(--border);
    box-shadow: inset 0 0 8px rgba(0, 0, 0, 0.1);
    max-width: 550px;
    margin: 0 auto;
}

.download-btn {
    position: absolute;
    top: 8px;
    right: 8px;
    background-color: rgba(0, 0, 0, 0.5);
    border-radius: 50%;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
    color: white;
    z-index: 10;
    font-size: 0.875rem;
}

.download-btn:hover {
    background-color: var(--accent);
    transform: scale(1.1);
    box-shadow: 0 0 8px rgba(142, 68, 173, 0.5);
}

.lora-item {
    position: relative;
    padding-right: 40px;
    transition: all 0.3s ease;
}

.lora-item:hover {
    transform: translateY(-2px);
}

.remove-lora {
    position: absolute;
    right: 8px;
    top: 8px;
    color: #ff453a;
    cursor: pointer;
    width: 28px;
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.remove-lora:hover {
    background-color: rgba(255, 69, 58, 0.2);
    transform: scale(1.1);
}

.btn-primary {
    background-color: var(--accent);
    color: white;
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 500;
    letter-spacing: 0.3px;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.btn-primary:hover {
    background-color: var(--accent-hover);
    transform: translateY(-2px);
    box-shadow: 0 6px 10px rgba(0, 0, 0, 0.15);
}

.btn-primary:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Specific styles for save preset button */
#save-preset-btn {
    background-color: var(--accent);
    color: white;
    font-weight: 500;
    min-width: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

#save-preset-btn:hover {
    background-color: var(--accent-hover);
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#save-preset-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.btn-secondary {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    transition: all 0.3s ease;
    border-radius: 8px;
    font-weight: 500;
    border: 1px solid var(--border);
}

.btn-secondary:hover {
    background-color: rgba(255, 255, 255, 0.05);
    transform: translateY(-2px);
}

.logs-container {
    max-height: 120px;
    overflow-y: auto;
    font-family: 'Courier New', Courier, monospace;
    font-size: 11px;
    background-color: var(--bg-tertiary);
    border-radius: 6px;
    padding: 8px;
    border: 1px solid var(--border);
    box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.1);
}

.info-log {
    color: var(--text-primary);
    margin-bottom: 4px;
}

.error-log {
    color: #ff453a;
    margin-bottom: 4px;
    font-weight: 500;
}

.success-log {
    color: #30d158;
    margin-bottom: 4px;
    font-weight: 500;
}

.status-indicator {
    padding: 4px 10px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    display: inline-block;
    transition: all 0.3s ease;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-default {
    background-color: var(--bg-tertiary);
    color: var(--text-primary);
    border: 1px solid var(--border);
}

.status-progress {
    background-color: #8e44ad;
    color: white;
    animation: pulse 2s infinite;
}

.status-completed {
    background-color: #30d158;
    color: white;
    animation: pulse 2s infinite;
}

.status-error {
    background-color: #ff453a;
    color: white;
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(142, 68, 173, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(142, 68, 173, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(142, 68, 173, 0);
    }
}

@keyframes indeterminate-progress {
    0% { background-position: 200% 0; }
    100% { background-position: -200% 0; }
}

#progress-bar-indicator.indeterminate {
    width: 100%;
    background-image: linear-gradient(
        to right,
        var(--bg-tertiary) 0%,
        var(--accent) 50%,
        var(--bg-tertiary) 100%
    );
    background-size: 200% 100%;
    animation: indeterminate-progress 2s linear infinite;
    border-radius: 4px;
    height: 4px;
}

/* Improved focus styles for accessibility */
:focus {
    outline: 2px solid var(--accent);
    outline-offset: 2px;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: var(--bg-secondary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--bg-tertiary);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--accent);
}

/* Additional styling for headings */
h2, h3 {
    font-weight: 600;
    letter-spacing: 0.3px;
}

/* Specific styling for the comparison slider title */
#kontextComparisonContainer h3 {
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.7);
    font-weight: 400; /* Lighter font weight */
    font-size: 1rem; /* Reduced font size (approx 25% reduction from text-lg which is 1.125rem) */
    margin-bottom: 1.5rem; /* Increased whitespace (mb-3 is 0.75rem, mb-6 is 1.5rem) */
    color: var(--text-secondary); /* Lighter color for less emphasis */
}

/* Custom styles for the comparison slider handle and line */
#comparison-slider-handle {
    background-color: rgba(255, 255, 255, 0.7) !important;
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.5), 0 0 20px rgba(255, 255, 255, 0.3);
    transition: left 0.1s ease-out, background-color 0.2s ease-out, box-shadow 0.2s ease-out; /* Додано transition для hover */
}

#comparison-slider-handle:hover {
    background-color: rgba(255, 255, 255, 0.9) !important; /* Яскравіше при наведенні */
    box-shadow: 0 0 12px rgba(255, 255, 255, 0.7), 0 0 25px rgba(255, 255, 255, 0.4);
}

#comparison-slider-handle > div {
    width: 36px !important; /* Зменшено на 25% від 48px */
    height: 36px !important; /* Зменшено на 25% від 48px */
    background-color: transparent !important; /* Прозорий фон */
    box-shadow: 0 0 8px rgba(255, 255, 255, 0.6), 0 1px 3px rgba(0,0,0,0.2); /* Тонка тінь та світіння */
    transition: transform 0.2s ease-out, box-shadow 0.2s ease-out; /* Додано transition для hover */
}

#comparison-slider-handle:hover > div {
    /* Removed transform: scale(1.1) to prevent handle "jumping" on hover */
    box-shadow: 0 0 12px rgba(255, 255, 255, 0.8), 0 2px 5px rgba(0,0,0,0.3); /* Посилена тінь при наведенні */
}

#comparison-slider-handle > div > i {
    color: rgba(255, 255, 255, 0.9) !important; /* Світла іконка для контрасту з темним фоном зображення */
    font-size: 1.125rem !important; /* Зменшено пропорційно ручці (було 1.5rem) */
    font-style: normal; /* To render the character correctly */
    line-height: 1;
    text-shadow: 0 1px 2px rgba(0,0,0,0.3); /* Тінь для іконки */
}

/* Плавне з'явлення панелі порівняння */
#kontextComparisonContainer {
    opacity: 0;
    transform: translateY(20px);
    transition: opacity 0.5s ease-out, transform 0.5s ease-out;
}

#kontextComparisonContainer.active {
    opacity: 1;
    transform: translateY(0);
}

/* Тонка анімація при русі слайдера */
#comparison-after-image {
    transition: clip-path 0.1s ease-out;
}


@keyframes rotateIcon {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes swapIcon {
    0% { transform: scale(1) rotate(0deg); }
    50% { transform: scale(1.2) rotate(180deg); }
    100% { transform: scale(1) rotate(360deg); }
}

@keyframes blinkIcon {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}

#comparison-reset-btn:hover i {
    animation: rotateIcon 1s linear infinite;
}

#comparison-swap-btn:hover i {
    animation: swapIcon 0.6s ease-in-out;
}

#comparison-toggle-view-btn:hover i {
    animation: blinkIcon 1s step-end infinite;
}

#comparison-save-btn[disabled] {
    border: 1px dashed var(--text-secondary) !important;
    opacity: 0.6;
}

#comparison-metadata .meta-item {
    display: flex; /* Default, will be overridden by JS if hidden */
    align-items: center;
}
#comparison-metadata .meta-item i {
    width: 1em; /* Ensure icons take up consistent space */
    text-align: center;
}


/* Smooth transitions при переключенні між режимами */
.interface-panel {
    transition: opacity 0.3s ease-in-out, visibility 0.3s ease-in-out;
    /* position: absolute; /* Для правильного накладання */
    /* width: 100%; */
}

.interface-panel.hidden {
    opacity: 0;
    visibility: hidden;
    display: none !important; /* Щоб уникнути впливу на розмітку */
}

.interface-panel.active-panel {
    opacity: 1;
    visibility: visible;
    display: flex !important; /* Або block, залежно від потреби */
}


/* Utility classes */
.overflow-hidden {
    overflow: hidden !important;
}

/* Preset panel styles */
.preset-item {
    position: relative;
    transition: all 0.3s ease;
    border: 1px solid rgba(255, 255, 255, 0.05);
    background-color: rgba(30, 30, 30, 0.5);
    margin-bottom: 0.5rem;
    border-radius: 0.5rem;
    overflow: hidden;
}

.preset-item:hover {
    transform: translateY(-2px);
    border-color: var(--accent);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.preset-name {
    padding: 0.5rem 0.75rem;
    font-weight: 500;
    color: var(--text-primary);
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: calc(100% - 70px);
}

.preset-actions {
    position: absolute;
    top: 0.5rem;
    right: 0.5rem;
    display: flex;
    gap: 0.5rem;
    opacity: 0.7;
    transition: opacity 0.2s ease;
}

.preset-item:hover .preset-actions {
    opacity: 1;
}

.preset-preview {
    background-color: var(--bg-secondary);
    border: 1px solid var(--border);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    z-index: 100;
    max-width: 280px;
    border-radius: 0.5rem;
}

.load-preset-btn, .delete-preset-btn {
    cursor: pointer;
    transition: all 0.2s ease;
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    background-color: rgba(0, 0, 0, 0.2);
}

.load-preset-btn {
    color: #3b82f6;
}

.delete-preset-btn {
    color: #ef4444;
}

.load-preset-btn:hover, .delete-preset-btn:hover {
    transform: scale(1.1);
    background-color: rgba(0, 0, 0, 0.4);
}

/* Specific styles for preset sections */
#presets-container {
    scrollbar-width: thin;
}

/* Hover ефекти для prompt-pill */
.prompt-pill {
    transition: all 0.2s ease-out;
}
.prompt-pill:hover {
    transform: translateY(-2px) scale(1.03);
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    background-color: var(--accent-hover);
    color: white;
}

#presets-container::-webkit-scrollbar {
    width: 4px;
}

#presets-container::-webkit-scrollbar-thumb {
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

#presets-container::-webkit-scrollbar-thumb:hover {
    background-color: var(--accent);
}

/* Gallery styles */
.gallery-nav-btn {
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(4px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    opacity: 0.7;
}

.gallery-nav-btn:hover {
    background-color: var(--accent);
    transform: scale(1.1);
    opacity: 1;
.gallery-nav-btn:hover {
    background-color: var(--accent);
    transform: scale(1.1);
}

.gallery-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.gallery-dot.active {
    background-color: var(--accent);
}

/* Prompt Pills Styling */
.prompt-pills-container {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem; /* 8px */
}

.prompt-pill {
    display: inline-flex;
    align-items: center;
    padding: 0.375rem 0.75rem; /* 6px 12px */
    font-size: 0.75rem; /* 12px */
    font-weight: 500;
    line-height: 1;
    color: var(--text-secondary);
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border);
    border-radius: 9999px; /* Fully rounded */
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.prompt-pill:hover {
    color: var(--text-primary);
    background-color: #3a3a3a; /* Slightly lighter tertiary */
    border-color: var(--accent);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.prompt-pill i {
    margin-right: 0.375rem; /* 6px */
    color: var(--accent);
    transition: color 0.2s ease;
}

.prompt-pill:hover i {
    color: var(--accent-hover);
}
    box-shadow: 0 0 8px rgba(142, 68, 173, 0.5);
}

.gallery-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.5);
    transition: all 0.3s ease;
    cursor: pointer;
}

.gallery-dot.active {
    background-color: var(--accent);
    transform: scale(1.2);
}

.image-counter {
    position: absolute;
    bottom: 8px;
    right: 8px;
    background-color: rgba(0, 0, 0, 0.5);
    color: white;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.75rem;
    backdrop-filter: blur(4px);
    z-index: 10;
}

/* Interface Panel Management */
.interface-panel {
    display: none; /* Hidden by default */
    /* flex-grow: 1; /* Allow panels to take available space if their container is flex */
    /* The 'active-panel' class will set display to flex or block as needed */
}

.interface-panel.active-panel {
    display: flex; /* Or 'block' if you prefer, 'flex' matches the new HTML structure */
}

/* Ensure the main container for interfaces can manage flex children correctly */
/* The #interfacesContainer is now set to display: contents, so its children 
   (#fluxLorasInterface and #kontextProInterface) become direct flex items 
   of the parent div ("Right Column for Switchable Interfaces").
   The parent div "Right Column for Switchable Interfaces" is already flex-col.
   The individual interface panels (#fluxLorasInterface, #kontextProInterface) 
   are set to flex-col lg:flex-row.
*/

/* Styling for file input to make it look better */
input[type="file"].input-field {
    padding: 0.375rem 0.75rem; /* Adjust padding for file input */
}

input[type="file"].input-field::file-selector-button {
    margin-right: 0.75rem;
    padding: 0.5rem 1rem;
    border-radius: 9999px; /* Full-rounded */
    border: 0px;
    font-size: 0.875rem; /* text-sm */
    font-weight: 600; /* font-semibold */
    background-color: var(--bg-tertiary); /* Tailwind gray-700 equivalent */
    color: var(--text-secondary); /* Tailwind gray-200 equivalent */
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
}

input[type="file"].input-field::file-selector-button:hover {
    background-color: #4b5563; /* Tailwind gray-600 equivalent */
}

/* Interface Toggle Styles */
#interfaceToggle {
    background: rgba(0,0,0,0.5);
    backdrop-filter: blur(5px);
    border-radius: 20px;
    padding: 4px;
    position: absolute;
    top: 8px;
    right: 8px;
}

.toggle-button {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    background: #4b5563;
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
}

.toggle-button:hover {
    background: #6366f1;
    transform: scale(1.1);
}

.toggle-button.active {
    background: #6366f1;
    box-shadow: 0 0 0 2px white;
}

.toggle-tab-button {
    background-color: transparent;
    border: none;
    border-bottom: 2px solid transparent; /* Default non-active state */
    transition: all 0.3s ease;
}

.toggle-tab-button.active {
    /* Styles for the active tab are mostly handled by Tailwind classes in the HTML:
       border-blue-500 text-blue-400
       The -mb-px is to make the bottom border overlap the container's bottom border.
    */
}

.toggle-tab-button:not(.active):hover {
    border-bottom-color: #4a5568; /* A slightly darker gray for hover on non-active tabs */
    color: #ffffff; /* White text on hover for non-active tabs */
}

/* Prompt Pills */
.prompt-pill {
    background-color: var(--bg-tertiary);
    border: 1px solid var(--border);
    color: var(--text-secondary);
    padding: 0.3rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.8rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease-in-out;
    display: inline-flex;
    align-items: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.prompt-pill:hover {
    background-color: var(--accent);
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(142, 68, 173, 0.3);
}

.prompt-pill:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.prompt-pill i {
    transition: color 0.2s ease-in-out;
}

.prompt-pill:hover i {
    color: white;
}

/* Collapsible Prompt Section */
.collapsible-trigger .trigger-icon {
    transition: transform 0.3s ease;
}

.collapsible-trigger[aria-expanded="true"] .trigger-icon {
    transform: rotate(180deg);
}

.collapsible-content {
    overflow: hidden;
    transition: max-height 0.4s ease-out, opacity 0.3s ease-in-out;
    max-height: 0;
    opacity: 0;
}

.collapsible-content.open {
    max-height: 500px; /* Adjust as needed */
    opacity: 1;
}

.prompt-pill {
    background-color: var(--bg-tertiary);
    color: var(--text-secondary);
    border: 1px solid var(--border);
    padding: 0.3rem 0.6rem;
    border-radius: 1rem;
    font-size: 0.75rem;
    transition: all 0.2s ease;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    gap: 0.3rem;
}

.prompt-pill:hover {
    background-color: var(--accent);
    color: white;
    border-color: var(--accent-hover);
    transform: translateY(-1px);
}

/* Before/After Comparison Component Styles */
#kontextComparisonContainer {
    display: none; /* Initially hidden, shown by JS */
}

#comparison-image-area img {
    max-width: 100%;
    max-height: 100%;
    user-select: none;
    -webkit-user-drag: none;
}

#comparison-after-image {
    clip-path: inset(0 0 0 50%); /* Default: show right half */
}

#comparison-slider-handle {
    touch-action: none; /* Prevent scrolling on touch devices when dragging handle */
}

.disabled-btn {
    opacity: 0.5;
    cursor: not-allowed;
    background-color: var(--bg-tertiary) !important; /* Ensure consistent disabled look */
    color: var(--text-secondary) !important;
    border-color: var(--border) !important;
}

.disabled-btn:hover {
    transform: none;
    box-shadow: none;
}

/* Custom slider thumb for comparison slider for better visibility */
#comparison-slider-input::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px; /* Larger thumb */
    height: 18px;
    background: var(--accent); /* Tailwind blue-500 */
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid var(--bg-secondary); /* Border to stand out */
    box-shadow: 0 0 5px rgba(0,0,0,0.3);
}

#comparison-slider-input::-moz-range-thumb {
    width: 18px;
    height: 18px;
    background: var(--accent);
    border-radius: 50%;
    cursor: pointer;
    border: 2px solid var(--bg-secondary);
    box-shadow: 0 0 5px rgba(0,0,0,0.3);
}

/* Styling for the vertical drag handle's inner icon container */
#comparison-slider-handle > div {
    pointer-events: none; /* Ensure the icon container doesn't interfere with dragging the handle itself */
}

/* Styles for the clear prompt button */
#clearKontextPromptBtn {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    padding: 0.25rem;
    line-height: 1;
    transition: color 0.2s ease;
    z-index: 10; /* Ensure it's above the textarea */
}

#clearKontextPromptBtn:hover {
    color: var(--accent); /* Use accent color on hover */
}

/* Ensure the comparison container is visible when it's supposed to be active */
#kontextProInterface.active-panel #kontextComparisonContainer.active {
    display: block !important;
}

/* Hide the old result container when comparison is active */
#kontextProInterface.active-panel #kontextComparisonContainer.active ~ #kontextEditedImageResultContainer,
#kontextProInterface.active-panel #kontextComparisonContainer.active ~ #kontextGalleryNavigation,
#kontextProInterface.active-panel #kontextComparisonContainer.active ~ #kontextGalleryIndicator,
#kontextProInterface.active-panel #kontextComparisonContainer.active ~ #kontextDownloadBtn {
    display: none !important;
}
