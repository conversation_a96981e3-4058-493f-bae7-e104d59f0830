/**
 * API interaction module
 * Handles all communication with the backend API
 */

const API_URL = 'http://localhost:3001/api';

/**
 * Sends a request to generate an image
 * @param {Object} inputData - Form data for image generation
 * @returns {Promise<Object>} API response
 */
async function generateImage(inputData) {
    try {
        const response = await fetch(`${API_URL}/generate`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(inputData),
        });

        return await response.json();
    } catch (error) {
        console.error("Error calling backend:", error);
        throw error;
    }
}

/**
 * Sends a request to edit an image using the Kontext model.
 * @param {Object} payload - Data for image editing, including prompt and image_url (base64).
 * @returns {Promise<Object>} API response.
 */
async function requestImageEdit(payload) {
    try {
        const response = await fetch(`${API_URL}/edit-image`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
        });

        if (!response.ok) {
            // Attempt to parse error response from backend
            const errorData = await response.json().catch(() => ({ message: "Unknown server error" }));
            console.error("Error from /edit-image endpoint:", response.status, errorData);
            // Construct a more informative error to be caught by the caller
            const error = new Error(errorData.message || `HTTP error! status: ${response.status}`);
            error.response = errorData; // Attach full response if needed
            error.status = response.status;
            throw error;
        }
        return await response.json();
    } catch (error) {
        console.error("Error calling /edit-image backend:", error);
        // Ensure the error propagated has a meaningful message for UI display
        throw error; // Re-throw to be handled by the calling function (e.g., in formHandlers)
    }
}

/**
 * Sends a request to improve a prompt using the Gemini LLM.
 * @param {string} promptText - The user's original prompt.
 * @param {{ mimeType: string, data: string } | null} [image] - Optional image object for multimodal prompt improvement.
 * @param {string} [strictnessLevel='cautious'] - The desired strictness level for prompt generation.
 * @param {boolean} [isAnatomical=false] - Flag indicating if the request is anatomical.
 * @returns {Promise<Object>} API response.
 */
async function improvePrompt(promptText, image = null, strictnessLevel = 'cautious', isAnatomical = false) {
    try {
        const payload = {
            prompt: promptText,
            strictnessLevel: strictnessLevel,
            isAnatomical: isAnatomical // Add isAnatomical to the payload
        };
        if (image) {
            payload.image = image;
        }

        const response = await fetch(`${API_URL}/improve-prompt`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(payload),
        });

        if (!response.ok) {
            const errorData = await response.json().catch(() => ({ message: "Unknown server error during prompt improvement" }));
            console.error("Error from /improve-prompt endpoint:", response.status, errorData);
            const error = new Error(errorData.message || `HTTP error! status: ${response.status}`);
            error.response = errorData;
            error.status = response.status;
            throw error;
        }
        return await response.json();
    } catch (error) {
        console.error("Error calling /improve-prompt backend:", error);
        throw error;
    }
}

// Export API functions
window.api = {
    generateImage,
    requestImageEdit, // Add the new function
    improvePrompt
};
