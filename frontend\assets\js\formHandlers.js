/**
 * Form handlers module
 * Handles form interactions and data collection
 */

let currentOriginalKontextPrompt = null; // Added for revert functionality

/**
 * Collects input data from the form
 * @returns {Object} Collected form data
 */
function collectInputData() {
    console.log("Collecting input data...");
    const lorasArray = [];
    document.querySelectorAll('#loras-container .lora-item').forEach(item => {
        const pathInput = item.querySelector('input[type="text"]');
        const scaleInput = item.querySelector('input[type="number"]');
        if (pathInput && pathInput.value.trim() !== "" && scaleInput) {
            lorasArray.push({ path: pathInput.value.trim(), scale: parseFloat(scaleInput.value) });
        }
    });

    const additionalSettingsDiv = document.getElementById('additional-settings');

    if (!additionalSettingsDiv) {
        console.error("Additional settings div not found in collectInputData!");
        return null;
    }

    const rangeInputs = additionalSettingsDiv.querySelectorAll('input[type="range"]');
    const toggleInputs = additionalSettingsDiv.querySelectorAll('.toggle-switch input[type="checkbox"]');

    console.log("collectInputData - Found range inputs:", rangeInputs.length);
    console.log("collectInputData - Found toggle inputs:", toggleInputs.length);

    // Handle image size
    let imageSize;
    const selectedSize = document.getElementById('image-size').value;

    if (selectedSize === 'custom') {
        // For custom size, create an object with width and height
        const width = parseInt(document.getElementById('custom-width').value, 10);
        const height = parseInt(document.getElementById('custom-height').value, 10);

        // Ensure values are multiples of 8 and within valid range
        const validWidth = Math.min(Math.max(Math.round(width / 8) * 8, 256), 1024);
        const validHeight = Math.min(Math.max(Math.round(height / 8) * 8, 256), 1024);

        imageSize = {
            width: validWidth,
            height: validHeight
        };
    } else {
        // For predefined sizes, use the enum value directly
        imageSize = selectedSize;
    }

    return {
        form: document.getElementById('form-selector').value,
        prompt: document.getElementById('prompt').value,
        loras: lorasArray,
        imageSize: imageSize,
        numInferenceSteps: rangeInputs[0] ? rangeInputs[0].value : "28",
        seed: document.getElementById('seed').value,
        guidanceScale: rangeInputs[1] ? rangeInputs[1].value : "3.5",
        syncMode: toggleInputs[0] ? toggleInputs[0].checked : false,
        numImages: rangeInputs[2] ? rangeInputs[2].value : "1",
        safetyChecker: document.getElementById('safety-checker') ? document.getElementById('safety-checker').checked :
                    ((toggleInputs && toggleInputs.length > 1) ? toggleInputs[1].checked : false),
        outputFormat: document.getElementById('output-format').value
    };
}

/**
 * Handles the reset button click
 */
function handleResetButtonClick() {
    console.log("Reset button clicked");
    document.getElementById('prompt').value = "";
    const lorasContainer = document.getElementById('loras-container');
    while (lorasContainer.children.length > 1) lorasContainer.removeChild(lorasContainer.lastChild);
    const firstLora = lorasContainer.firstElementChild;
    if (firstLora) {
        firstLora.querySelector('input[type="text"]').value = "";
        firstLora.querySelector('input[type="range"]').value = "0.7";
        firstLora.querySelector('input[type="number"]').value = "0.7";
    }
    document.getElementById('image-size').value = "square_hd";
    window.ui.updateImageSizeDisplay("square_hd");

    // Reset custom size inputs if they exist
    const customWidth = document.getElementById('custom-width');
    const customHeight = document.getElementById('custom-height');
    if (customWidth) customWidth.value = "1024";
    if (customHeight) customHeight.value = "1024";

    // Hide custom size container
    const customSizeContainer = document.getElementById('custom-size-container');
    if (customSizeContainer) customSizeContainer.classList.add('hidden');

    const additionalSettingsDiv = document.getElementById('additional-settings');
    const rangeInputs = additionalSettingsDiv.querySelectorAll('input[type="range"]');
    const toggleInputs = additionalSettingsDiv.querySelectorAll('.toggle-switch input[type="checkbox"]');

    if(rangeInputs[0]) rangeInputs[0].value = "28";
    // More robust way to get the number input associated with the first range slider for numInferenceSteps
    const numInfStepsNumberInput = rangeInputs[0] ? rangeInputs[0].closest('.flex.items-center.gap-3').querySelector('input[type="number"]') : null;
    if(numInfStepsNumberInput) numInfStepsNumberInput.value = "28";

    document.getElementById('seed').value = "random";

    if(rangeInputs[1]) rangeInputs[1].value = "3.5";
    const guidanceScaleNumberInput = rangeInputs[1] ? rangeInputs[1].closest('.flex.items-center.gap-3').querySelector('input[type="number"]') : null;
    if(guidanceScaleNumberInput) guidanceScaleNumberInput.value = "3.5";

    if(toggleInputs[0]) toggleInputs[0].checked = false; // Sync Mode

    if(rangeInputs[2]) rangeInputs[2].value = "1";
    const numImagesNumberInput = rangeInputs[2] ? rangeInputs[2].closest('.flex.items-center.gap-3').querySelector('input[type="number"]') : null;
    if(numImagesNumberInput) numImagesNumberInput.value = "1";

    if(toggleInputs[1]) toggleInputs[1].checked = false; // Safety Checker

    // Also set by ID if available
    const safetyCheckerElement = document.getElementById('safety-checker');
    if(safetyCheckerElement) safetyCheckerElement.checked = false;

    document.getElementById('output-format').value = "jpeg";

    window.ui.updateStatusIndicator("Ready", false);
    document.getElementById('image-placeholder').classList.remove('hidden');
    const genImg = document.getElementById('generated-image');
    genImg.classList.add('hidden');
    genImg.src = '';
    document.getElementById('download-btn').classList.add('hidden');
    document.getElementById('request-info').textContent =
        "Your request took 0.00 seconds and will cost $0.000 per megapixel.";
    const logsContent = document.getElementById('logs-content');
    while (logsContent.children.length > 2) logsContent.removeChild(logsContent.lastChild);
    window.ui.addLogMessage("Form reset to default values", "info");
    window.ui.hideIndeterminateProgress();
}

/**
 * Handles the randomize seed button click
 */
function handleRandomizeSeed() {
    const randomSeed = Math.floor(Math.random() * 1000000);
    document.getElementById('seed').value = randomSeed;
    window.ui.addLogMessage(`Seed randomized to: ${randomSeed}`, "info");
}

/**
 * Applies a preset to the form
 * @param {Object} preset - The preset to apply
 */
function applyPreset(preset) {
    console.log("Applying preset:", preset);

    // Set form selector
    if (preset.form) {
        document.getElementById('form-selector').value = preset.form;
    }

    // Set image size
    if (preset.imageSize) {
        if (typeof preset.imageSize === 'object') {
            // Custom size
            document.getElementById('image-size').value = 'custom';
            const customWidth = document.getElementById('custom-width');
            const customHeight = document.getElementById('custom-height');
            const customSizeContainer = document.getElementById('custom-size-container');

            if (customWidth && customHeight) {
                customWidth.value = preset.imageSize.width;
                customHeight.value = preset.imageSize.height;
                customSizeContainer.classList.remove('hidden');
            }
        } else {
            // Predefined size
            document.getElementById('image-size').value = preset.imageSize;
            const customSizeContainer = document.getElementById('custom-size-container');
            if (customSizeContainer) {
                customSizeContainer.classList.add('hidden');
            }
        }
        window.ui.updateImageSizeDisplay(preset.imageSize);
    }

    // Set LoRAs
    if (preset.loras && preset.loras.length > 0) {
        const lorasContainer = document.getElementById('loras-container');

        // Clear existing LoRAs
        while (lorasContainer.children.length > 0) {
            lorasContainer.removeChild(lorasContainer.lastChild);
        }

        // Add LoRAs from preset
        preset.loras.forEach((lora) => {
            const loraItem = document.createElement('div');
            loraItem.className = 'lora-item mb-2 p-3 bg-gray-800 rounded-lg';
            loraItem.innerHTML = `
                <div class="mb-2">
                    <label class="block text-xs font-medium mb-1">Path</label>
                    <input type="text" class="input-field w-full p-1.5 rounded-lg text-sm" placeholder="Enter LoRA URL" value="${lora.path}">
                </div>
                <div>
                    <label class="block text-xs font-medium mb-1">Scale</label>
                    <div class="flex items-center gap-2">
                        <input type="range" min="0" max="1" step="0.01" value="${lora.scale}" class="slider flex-1">
                        <input type="number" min="0" max="1" step="0.01" value="${lora.scale}" class="input-field w-16 p-1.5 rounded-lg text-sm text-center">
                    </div>
                </div>
                <div class="remove-lora p-1.5 cursor-pointer">
                    <i class="fas fa-trash text-sm"></i>
                </div>
            `;

            // Add event listener for remove button
            const removeBtn = loraItem.querySelector('.remove-lora');
            removeBtn.addEventListener('click', function() {
                // @ts-ignore: Property 'loraManager' does not exist on type 'Window & typeof globalThis'
                if (window.loraManager && typeof window.loraManager.handleRemoveLoraItem === 'function') {
                    window.loraManager.handleRemoveLoraItem(loraItem);
                } else {
                    console.error("loraManager or handleRemoveLoraItem function not found");
                }
            });

            // Add event listeners for slider sync
            const slider = loraItem.querySelector('input[type="range"]');
            const numberInput = loraItem.querySelector('input[type="number"]');

            slider.addEventListener('input', function() {
                numberInput.value = this.value;
            });

            numberInput.addEventListener('input', function() {
                let value = parseFloat(this.value);
                const min = parseFloat(slider.min);
                const max = parseFloat(slider.max);
                if (isNaN(value)) value = parseFloat(slider.defaultValue) || min;
                if (value < min) value = min;
                if (value > max) value = max;
                this.value = value;
                slider.value = value;
            });

            lorasContainer.appendChild(loraItem);
        });
    }

    // Set additional settings
    const additionalSettingsDiv = document.getElementById('additional-settings');

    if (!additionalSettingsDiv) {
        console.error("Additional settings div not found!");
        return;
    }

    const rangeInputs = additionalSettingsDiv.querySelectorAll('input[type="range"]');
    const toggleInputs = additionalSettingsDiv.querySelectorAll('.toggle-switch input[type="checkbox"]');

    console.log("Found range inputs:", rangeInputs.length);
    console.log("Found toggle inputs:", toggleInputs.length);

    // Set number of inference steps
    if (preset.numInferenceSteps && rangeInputs[0]) {
        rangeInputs[0].value = preset.numInferenceSteps;
        const numInfStepsNumberInput = rangeInputs[0].closest('.flex.items-center.gap-3').querySelector('input[type="number"]');
        if (numInfStepsNumberInput) {
            numInfStepsNumberInput.value = preset.numInferenceSteps;
        }
    }

    // Set seed
    if (preset.seed) {
        document.getElementById('seed').value = preset.seed;
    }

    // Set guidance scale
    if (preset.guidanceScale && rangeInputs[1]) {
        rangeInputs[1].value = preset.guidanceScale;
        const guidanceScaleNumberInput = rangeInputs[1].closest('.flex.items-center.gap-3').querySelector('input[type="number"]');
        if (guidanceScaleNumberInput) {
            guidanceScaleNumberInput.value = preset.guidanceScale;
        }
    }

    // Set sync mode
    if (preset.syncMode !== undefined && toggleInputs[0]) {
        toggleInputs[0].checked = preset.syncMode;
    }

    // Set number of images
    if (preset.numImages && rangeInputs[2]) {
        rangeInputs[2].value = preset.numImages;
        const numImagesNumberInput = rangeInputs[2].closest('.flex.items-center.gap-3').querySelector('input[type="number"]');
        if (numImagesNumberInput) {
            numImagesNumberInput.value = preset.numImages;
        }
    }

    // Set safety checker to always be false
    try {
        // Always set to false regardless of preset value
        console.log("Setting Safety Checker to false (forced)");

        // Try multiple approaches to ensure it's set

        // Approach 1: Use direct ID (most reliable)
        const safetyCheckerElement = document.getElementById('safety-checker');
        if (safetyCheckerElement) {
            console.log("Setting safety checker using direct ID");
            safetyCheckerElement.checked = false;
        }

        // Approach 2: Using toggleInputs array
        if (toggleInputs && toggleInputs.length > 1) {
            console.log("Setting safety checker using toggleInputs[1]");
            toggleInputs[1].checked = false;
        }

        // Approach 3: Direct querySelector
        const safetyCheckerToggles = document.querySelectorAll('#additional-settings .toggle-switch input[type="checkbox"]');
        if (safetyCheckerToggles && safetyCheckerToggles.length > 1) {
            console.log("Setting safety checker using direct querySelectorAll");
            safetyCheckerToggles[1].checked = false;
        }

        // Approach 4: Find by label text
        const safetyCheckerLabels = Array.from(document.querySelectorAll('label.block.text-xs'));
        const safetyLabel = safetyCheckerLabels.find(label =>
            label.textContent && label.textContent.includes('Safety Checker'));

        if (safetyLabel) {
            const toggleSwitch = safetyLabel.nextElementSibling;
            if (toggleSwitch && toggleSwitch.classList.contains('toggle-switch')) {
                const checkbox = toggleSwitch.querySelector('input[type="checkbox"]');
                if (checkbox) {
                    console.log("Setting safety checker using label search");
                    checkbox.checked = false;
                }
            }
        }

        // Override preset value to ensure it's false
        if (preset.safetyChecker !== false) {
            preset.safetyChecker = false;
            console.log("Preset safetyChecker value overridden to false");
        }
    } catch (error) {
        console.error("Error setting safety checker:", error);
    }

    // Set output format
    if (preset.outputFormat) {
        document.getElementById('output-format').value = preset.outputFormat;
    }
}


/**
 * Collects input data from the FLUX.1 Kontext [pro] form.
 * @returns {Object | null} Collected form data for Kontext, or null if essential elements are missing.
 */
function collectKontextInputData() {
    console.log("Collecting Kontext Pro input data...");
    const imageUploadElement = document.getElementById('kontextImageUpload');
    const promptElement = document.getElementById('kontextPrompt');
    const guidanceScaleElement = document.getElementById('kontextGuidanceScaleInput');
    const numImagesElement = document.getElementById('kontextNumImagesInput');
    const outputFormatElement = document.getElementById('kontextOutputFormat');
    const seedElement = document.getElementById('kontextSeed');
    const safetyToleranceElement = document.getElementById('kontextSafetyTolerance'); // New
    const aspectRatioElement = document.getElementById('kontextAspectRatio'); // New


    if (!imageUploadElement || !promptElement) {
        console.error("Kontext form elements (image upload or prompt) not found.");
        window.ui.addLogMessageToContainer("Kontext form elements missing.", "error", "kontext-logs-content");
        return null;
    }

    const imageData = imageUploadElement.files[0]; // Get the File object
    if (!imageData) {
        window.ui.addLogMessageToContainer("Please select an image for Kontext editing.", "error", "kontext-logs-content");
        window.ui.updateKontextStatusIndicator("Error: No image selected", true);
        return null;
    }

    const promptValue = promptElement.value.trim();
    if (promptValue === "") {
        window.ui.addLogMessageToContainer("Prompt cannot be empty for Kontext editing.", "error", "kontext-logs-content");
        window.ui.updateKontextStatusIndicator("Error: Prompt is required", true);
        return null; // Prevent further processing
    }
    
    const data = {
        prompt: promptValue, // Use the trimmed prompt value
        // image_url will be handled by the API call logic (converted to base64 or uploaded)
        raw_image_file: imageData, // Keep the raw file for now
        guidance_scale: guidanceScaleElement ? parseFloat(guidanceScaleElement.value) : 3.5,
        num_images: numImagesElement ? parseInt(numImagesElement.value, 10) : 1,
        output_format: outputFormatElement ? outputFormatElement.value : 'jpeg',
        seed: seedElement ? (seedElement.value.toLowerCase() === 'random' ? undefined : parseInt(seedElement.value, 10)) : undefined,
        safety_tolerance: safetyToleranceElement ? safetyToleranceElement.value : "5", // New, default "5"
        aspect_ratio: aspectRatioElement ? aspectRatioElement.value : "1:1" // New, default "1:1"
    };
    
    // Remove undefined seed to let API handle random if not specified
    if (data.seed === undefined) delete data.seed;

    console.log("Kontext Input Data Collected:", data);
    return data;
}

/**
 * Handles the submit button click for the Kontext Pro interface.
 */
async function handleSubmitKontextEdit() {
    console.log("Submit Kontext Edit button clicked");
    window.ui.updateKontextStatusIndicator("Processing...", false);
    window.ui.showKontextProgress("Preparing your edit...");
    window.ui.addLogMessageToContainer("Kontext edit request initiated.", "info", "kontext-logs-content");

    const kontextData = collectKontextInputData();

    if (!kontextData || !kontextData.raw_image_file) {
        // Error message already shown by collectKontextInputData or image check
        window.ui.hideKontextProgress();
        return;
    }

    const MAX_KONTEXT_FILE_SIZE_MB = 3; // Set to 3MB as per user request
    const MAX_KONTEXT_FILE_SIZE_BYTES = MAX_KONTEXT_FILE_SIZE_MB * 1024 * 1024;

    if (kontextData.raw_image_file.size > MAX_KONTEXT_FILE_SIZE_BYTES) { // Restored block
        const errorMsg = `Image too large for Kontext editing (max ${MAX_KONTEXT_FILE_SIZE_MB}MB). Please use a smaller image.`;
        console.warn(errorMsg, "Selected file size:", (kontextData.raw_image_file.size / (1024*1024)).toFixed(2) + "MB");
        window.ui.updateKontextStatusIndicator(errorMsg, true);
        window.ui.addLogMessageToContainer(errorMsg, "error", "kontext-logs-content");
        window.ui.hideKontextProgress();
        return;
    }

    // No outer try...catch here, error handling will be inside onloadend or onerror
    // Convert image to base64 data URI
    const reader = new FileReader();

    reader.onloadend = async function() {
        const startTime = performance.now(); // Record start time
        try {
            const base64Image = reader.result;
            console.log("Base64 Image Data URI length:", base64Image.length);
            // Estimate payload size: A data URI looks like "data:[<mediatype>][;base64],<data>"
            // The actual base64 part is after the comma.
            const base64DataOnly = base64Image.split(',')[1] || "";
            console.log("Base64 Data Part length:", base64DataOnly.length);
            // Approximate original file size from base64: (length * 3/4) bytes
            const approxOriginalSizeBytes = (base64DataOnly.length * 0.75);
            console.log(`Approximate original image size: ${(approxOriginalSizeBytes / (1024*1024)).toFixed(2)} MB`);

            const apiPayload = {
                prompt: kontextData.prompt,
                image_url: base64Image, // Send base64 data URI
                guidance_scale: kontextData.guidance_scale,
                num_images: kontextData.num_images,
                output_format: kontextData.output_format,
                safety_tolerance: kontextData.safety_tolerance, // Add safety_tolerance
                aspect_ratio: kontextData.aspect_ratio // Add aspect_ratio
            };
            if (kontextData.seed !== undefined) {
                apiPayload.seed = kontextData.seed;
            }

            window.ui.addLogMessageToContainer("Sending request to Kontext API...", "info", "kontext-logs-content");
            window.ui.showKontextProgress("Editing image with Kontext Pro..."); // This will be hidden in the finally block below

            const result = await window.api.requestImageEdit(apiPayload);
            const endTime = performance.now(); // Record end time
            const duration = ((endTime - startTime) / 1000).toFixed(2); // Calculate duration in seconds
            console.log("Kontext API Result:", result);

            if (result && result.success && result.data) {
                window.ui.updateKontextStatusIndicator("Editing complete!", false);
                window.ui.addLogMessageToContainer("Kontext editing successful.", "success", "kontext-logs-content");

                const originalImageElement = document.getElementById('kontextImagePreview');
                const originalImageUrl = originalImageElement ? originalImageElement.src : null;
                
                let editedImageDetails = null;
                const apiResultData = result.data;

                // Robustly find the image details from the API response
                if (apiResultData?.data?.images?.[0]) {
                    editedImageDetails = apiResultData.data.images[0];
                    console.log("Edited image details found via deep nesting (result.data.data.images).");
                } else if (apiResultData?.images?.[0]) {
                    editedImageDetails = apiResultData.images[0];
                    console.log("Edited image details found via shallow nesting (result.data.images).");
                } else if (apiResultData?.url) {
                    editedImageDetails = apiResultData; // The entire object is the details
                    console.log("Edited image details found via direct URL (result.data.url).");
                }

                const kontextEditedImageContainer = document.getElementById('kontextEditedImageResultContainer');
                const comparisonContainer = document.getElementById('kontextComparisonContainer');

                if (originalImageUrl && originalImageUrl !== '' && originalImageUrl !== '#' && editedImageDetails && editedImageDetails.url) {
                    if (kontextEditedImageContainer) kontextEditedImageContainer.style.display = 'none';
                    if (comparisonContainer) comparisonContainer.style.display = 'block'; // Show comparison
                    
                    const originalFilename = kontextData.raw_image_file.name;
                    const originalWidth = originalImageElement.naturalWidth;
                    const originalHeight = originalImageElement.naturalHeight;

                    // Use calculated duration if backend timing is not available
                    const processingTime = result.data?.timings?.inference_s ? `${result.data.timings.inference_s.toFixed(2)}s` : `${duration}s`;

                    const sliderData = {
                        originalImageUrl: originalImageUrl,
                        editedImageUrl: editedImageDetails.url,
                        originalDetails: `${originalFilename} (${originalWidth}x${originalHeight})`,
                        editedDetails: `${editedImageDetails.width}x${editedImageDetails.height}`,
                        processingTime: processingTime,
                        seed: editedImageDetails.seed || ''
                    };

                    window.comparisonSlider.init(sliderData);
                } else {
                    console.warn("Could not initialize comparison view. Missing original/edited image URL or details. Falling back to old gallery.", {originalImageUrl, editedImageDetails});
                    window.ui.addLogMessageToContainer("Warning: Could not show comparison view. Displaying standard result gallery.", "error", "kontext-logs-content");
                    if (comparisonContainer) comparisonContainer.style.display = 'none'; // Hide comparison on fallback
                    if (kontextEditedImageContainer) kontextEditedImageContainer.style.display = 'block'; // Show old container for fallback
                    window.ui.displayKontextEditedImage(apiResultData);
                }

            } else {
                const errorMessage = result && result.errors ? result.errors.join(', ') : (result ? result.message : 'Unknown error from Kontext API');
                window.ui.updateKontextStatusIndicator(`Error: ${errorMessage}`, true);
                window.ui.addLogMessageToContainer(`Kontext API error: ${errorMessage}`, "error", "kontext-logs-content");
                const kontextEditedImageContainer = document.getElementById('kontextEditedImageResultContainer');
                if (kontextEditedImageContainer) kontextEditedImageContainer.style.display = 'block';
                const comparisonContainer = document.getElementById('kontextComparisonContainer');
                if (comparisonContainer) comparisonContainer.style.display = 'none';
            }
        } catch (error) {
            console.error("Error during Kontext API call or processing:", error);
            window.ui.updateKontextStatusIndicator(`API Error: ${error.message || 'Unknown API error'}`, true);
            window.ui.addLogMessageToContainer(`Kontext API processing error: ${error.message || error}`, "error", "kontext-logs-content");
            const kontextEditedImageContainer = document.getElementById('kontextEditedImageResultContainer');
            if (kontextEditedImageContainer) kontextEditedImageContainer.style.display = 'block';
            const comparisonContainer = document.getElementById('kontextComparisonContainer');
            if (comparisonContainer) comparisonContainer.style.display = 'none';
        } finally {
            window.ui.hideKontextProgress();
        }
    };

    reader.onerror = function() {
        console.error("Error reading image file for Kontext.");
        window.ui.updateKontextStatusIndicator("Error reading image file.", true);
        window.ui.addLogMessageToContainer("Error reading image file.", "error", "kontext-logs-content");
        window.ui.hideKontextProgress(); // Hide progress on file read error
    };

    // Start reading the file. If this itself throws an error (unlikely for readAsDataURL),
    // it won't be caught by the FileReader's onerror.
    try {
        reader.readAsDataURL(kontextData.raw_image_file);
    } catch (fileReadError) {
        console.error("Immediate error calling reader.readAsDataURL:", fileReadError);
        window.ui.updateKontextStatusIndicator("Client-side error preparing image.", true);
        window.ui.addLogMessageToContainer(`Client error preparing image: ${fileReadError.message || fileReadError}`, "error", "kontext-logs-content");
        window.ui.hideKontextProgress(); // Hide progress if readAsDataURL itself fails
    }
}

/**
 * Sets up event listeners for the Kontext Pro interface.
 */
function setupKontextEventListeners() {
    const imageUpload = document.getElementById('kontextImageUpload');
    const submitButton = document.getElementById('submitKontextEditButton');
    const guidanceSlider = document.getElementById('kontextGuidanceScaleSlider');
    const guidanceInput = document.getElementById('kontextGuidanceScaleInput');
    const numImagesSlider = document.getElementById('kontextNumImagesSlider');
    const numImagesInput = document.getElementById('kontextNumImagesInput');
    const improveKontextPromptBtn = document.getElementById('improve-kontext-prompt-btn');
    const revertKontextPromptBtn = document.getElementById('revert-kontext-prompt-btn');

    if (imageUpload) {
        imageUpload.addEventListener('change', window.ui.displayKontextImagePreview);
    }
    if (submitButton) {
        submitButton.addEventListener('click', handleSubmitKontextEdit);
    }
    if (improveKontextPromptBtn) {
        improveKontextPromptBtn.addEventListener('click', handleImproveKontextPromptClick);
    }
    if (revertKontextPromptBtn) {
        revertKontextPromptBtn.addEventListener('click', handleRevertKontextPromptClick);
    }

    // Setup listener for the anatomical checkbox
    if (window.ui && window.ui.setupAnatomicalCheckboxListener) {
        window.ui.setupAnatomicalCheckboxListener();
    } else {
        console.error("setupAnatomicalCheckboxListener not found on window.ui");
    }
    
    // Sync Kontext sliders and inputs
    if (guidanceSlider && guidanceInput) {
        guidanceSlider.addEventListener('input', () => guidanceInput.value = guidanceSlider.value);
        guidanceInput.addEventListener('input', () => guidanceSlider.value = guidanceInput.value);
    }
    if (numImagesSlider && numImagesInput) {
        numImagesSlider.addEventListener('input', () => numImagesInput.value = numImagesSlider.value);
        numImagesInput.addEventListener('input', () => numImagesSlider.value = numImagesInput.value);
    }
}

/**
 * Handles the "Improve Prompt" button click for the Kontext Pro interface.
 */
async function handleImproveKontextPromptClick() {
    const kontextPromptTextarea = document.getElementById('kontextPrompt');
    const statusElement = document.getElementById('kontextImprovePromptStatus');
    const improveButton = document.getElementById('improve-kontext-prompt-btn');
    const revertButton = document.getElementById('revert-kontext-prompt-btn'); // Added

    if (!kontextPromptTextarea) {
        console.error("Kontext prompt textarea not found.");
        window.ui.addLogMessageToContainer("Kontext prompt textarea not found.", "error", "kontext-logs-content");
        if (statusElement) {
            statusElement.className = 'text-xs text-red-400';
            statusElement.innerHTML = '<i class="fas fa-times-circle text-red-400 text-xs mr-1.5"></i> Error: Textarea not found.';
            setTimeout(() => {
                if (statusElement.innerHTML.includes("Error: Textarea not found.")) statusElement.innerHTML = '';
            }, 6000);
        }
        return;
    }

    const currentPrompt = kontextPromptTextarea.value;
    if (!currentPrompt.trim()) {
        window.ui.addLogMessageToContainer("Kontext prompt is empty. Nothing to improve.", "info", "kontext-logs-content");
        if (statusElement) {
            statusElement.className = 'text-xs text-red-400'; // Using red for consistency with other errors
            statusElement.innerHTML = '<i class="fas fa-exclamation-circle text-red-400 text-xs mr-1.5"></i> Prompt is empty.';
            setTimeout(() => {
                if (statusElement.innerHTML.includes("Prompt is empty.")) {
                    statusElement.innerHTML = "";
                }
            }, 3000);
        }
        return;
    }

    window.ui.addLogMessageToContainer("Improving Kontext prompt...", "info", "kontext-logs-content");
    if (statusElement) {
        statusElement.className = 'text-xs text-gray-300';
        statusElement.innerHTML = '<i class="fas fa-spinner fa-spin text-xs mr-1.5"></i> Improving prompt...';
    }
    if (improveButton) {
        improveButton.disabled = true;
        improveButton.classList.add('opacity-50', 'cursor-not-allowed');
    }

    // Store original prompt and hide revert button before attempting improvement
    currentOriginalKontextPrompt = kontextPromptTextarea.value;
    if (revertButton) {
        revertButton.classList.add('hidden');
    }

    // Store original prompt and hide revert button before attempting improvement
    currentOriginalKontextPrompt = kontextPromptTextarea.value;
    if (revertButton) {
        revertButton.classList.add('hidden');
    }

    try {
        let imagePayload = null;
        const imageUploadElement = document.getElementById('kontextImageUpload');
        const strictnessLevelElement = document.getElementById('kontextPromptStrictness');
        const strictnessLevel = strictnessLevelElement ? strictnessLevelElement.value : 'cautious';
        const isAnatomicalCheckbox = document.getElementById('kontextIsAnatomical'); // Get the new checkbox element
        const isAnatomical = isAnatomicalCheckbox ? isAnatomicalCheckbox.checked : false; // Default to false if not found
        const imagePreviewElement = document.getElementById('kontextImagePreview'); // Assuming this is where the preview is shown

        if (imageUploadElement && imageUploadElement.files && imageUploadElement.files[0]) {
            const imageFile = imageUploadElement.files[0];
            const MAX_FILE_SIZE_MB = 10; // Changed from 3 to 10 as per user request
            const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024;

            if (imageFile.size > MAX_FILE_SIZE_BYTES) {
                window.ui.addLogMessageToContainer(`Image is too large (max ${MAX_FILE_SIZE_MB}MB). Skipping image for prompt improvement.`, "warning", "kontext-logs-content");
                if (statusElement) {
                    // Optionally show a temporary warning in the status
                    const originalStatus = statusElement.textContent;
                    statusElement.textContent = "Image too large, improving text only.";
                    setTimeout(() => {
                        if (statusElement.textContent === "Image too large, improving text only.") {
                             statusElement.textContent = originalStatus; // Revert or clear
                        }
                    }, 3000);
                }
            } else {
                // Convert file to base64
                const base64String = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = () => resolve(reader.result.split(',')[1]); // Get base64 part
                    reader.onerror = error => reject(error);
                    reader.readAsDataURL(imageFile);
                });
                imagePayload = {
                    mimeType: imageFile.type || 'image/jpeg', // Default to jpeg if type is missing
                    data: base64String
                };
            }
        } else if (imagePreviewElement && imagePreviewElement.src && imagePreviewElement.src.startsWith('data:image')) {
            // Fallback: if no file but preview has a data URL (e.g., from a previous upload or state)
            // This part is more complex as we need to estimate size from base64.
            // For simplicity, we'll assume if it's in preview, it was likely validated before,
            // or we can add a size check for base64 string length if critical.
            // Let's assume for now if it's a data URL in preview, we try to send it.
            // A more robust solution would involve storing the File object or its details.
            const base64Parts = imagePreviewElement.src.split(',');
            if (base64Parts.length === 2) {
                const mimeTypeMatch = base64Parts[0].match(/:(.*?);/);
                imagePayload = {
                    mimeType: mimeTypeMatch ? mimeTypeMatch[1] : 'image/jpeg',
                    data: base64Parts[1]
                };
                // Note: Size check for base64 is (length * 3/4) - padding. Approx.
                // const approxSize = imagePayload.data.length * 0.75;
                // if (approxSize > MAX_FILE_SIZE_BYTES) { imagePayload = null; /* log warning */ }
            }
        }


        // The API call now needs to accept an image object, strictness level, and isAnatomical flag
        const result = await window.api.improvePrompt(currentPrompt, imagePayload, strictnessLevel, isAnatomical);
        if (result && result.success && result.improvedPrompt) {
            kontextPromptTextarea.value = result.improvedPrompt;
            if (revertButton) { // Show revert button on successful improvement
                revertButton.classList.remove('hidden');
            }
            if (revertButton) { // Show revert button on successful improvement
                revertButton.classList.remove('hidden');
            }
            window.ui.addLogMessageToContainer("Kontext prompt improved successfully.", "success", "kontext-logs-content");
            if (statusElement) {
                statusElement.className = 'text-xs';
                statusElement.innerHTML = '<i class="fas fa-check text-green-400 text-xs mr-1.5"></i> Prompt improved!';
                setTimeout(() => {
                    if (statusElement.innerHTML.includes('fa-check')) {
                        statusElement.innerHTML = "";
                    }
                }, 4000); // 3-4 seconds for success
            }
        } else {
            const errorMessage = result && result.error ? result.error : (result && result.message ? result.message : "Failed to improve prompt.");
            window.ui.addLogMessageToContainer(`Error improving Kontext prompt: ${errorMessage}`, "error", "kontext-logs-content");
            if (statusElement) {
                statusElement.className = 'text-xs';
                statusElement.innerHTML = '<i class="fas fa-times-circle text-red-400 text-xs mr-1.5"></i> Error improving prompt.';
                setTimeout(() => {
                    if (statusElement.innerHTML.includes('fa-times-circle')) {
                        statusElement.innerHTML = "";
                    }
                }, 6000); // 5-6 seconds for error
            }
        }
    } catch (error) {
        console.error("Error calling improvePrompt API or processing image:", error);
        window.ui.addLogMessageToContainer(`API Error improving Kontext prompt: ${error.message || 'Unknown error'}`, "error", "kontext-logs-content");
        if (statusElement) {
            statusElement.className = 'text-xs';
            statusElement.innerHTML = '<i class="fas fa-times-circle text-red-400 text-xs mr-1.5"></i> Error improving prompt.';
            setTimeout(() => {
                if (statusElement.innerHTML.includes('fa-times-circle')) {
                    statusElement.innerHTML = "";
                }
            }, 6000); // 5-6 seconds for error
        }
    } finally {
        if (improveButton) {
            improveButton.disabled = false;
            improveButton.classList.remove('opacity-50', 'cursor-not-allowed');
        }
    }
}

/**
 * Handles the "Improve Prompt" button click.
 */
async function handleImprovePromptClick() {
    const promptTextarea = document.getElementById('prompt');
    const improveButton = document.getElementById('improve-prompt-btn');
    const originalButtonText = improveButton.innerHTML;

    if (!promptTextarea || !promptTextarea.value.trim()) {
        // Optionally, show a small message or just do nothing
        console.log("Prompt is empty, not improving.");
        window.ui.addLogMessage("Cannot improve an empty prompt.", "warning");
        return;
    }

    improveButton.disabled = true;
    improveButton.innerHTML = '<i class="fas fa-spinner fa-spin text-xs"></i> Improving...';
    window.ui.addLogMessage("Improving prompt with AI...", "info");

    try {
        // Ensure window.api and window.api.improvePrompt are available
        if (window.api && typeof window.api.improvePrompt === 'function') {
            const result = await window.api.improvePrompt(promptTextarea.value);
            if (result && result.success && result.improvedPrompt) {
                promptTextarea.value = result.improvedPrompt;
                window.ui.addLogMessage("Prompt improved successfully.", "success");
            } else {
                const errorMessage = result && result.errors ? result.errors.join(', ') : (result ? result.message : 'Failed to improve prompt.');
                window.ui.addLogMessage(`Error improving prompt: ${errorMessage}`, "error");
            }
        } else {
            console.error("window.api.improvePrompt is not defined.");
            window.ui.addLogMessage("Error: Improve prompt function not available in API module.", "error");
        }
    } catch (error) {
        console.error("Error calling improvePrompt API:", error);
        window.ui.addLogMessage(`API Error: ${error.message || 'Could not connect to improve prompt service.'}`, "error");
    } finally {
        improveButton.disabled = false;
        improveButton.innerHTML = originalButtonText;
    }
}

/**
 * Handles the "Revert" button click for the Kontext Pro interface.
 */
function handleRevertKontextPromptClick() { // Defined once
    const kontextPromptTextarea = document.getElementById('kontextPrompt');
    const revertButton = document.getElementById('revert-kontext-prompt-btn');

    if (kontextPromptTextarea && currentOriginalKontextPrompt !== null) {
        kontextPromptTextarea.value = currentOriginalKontextPrompt;
        window.ui.addLogMessageToContainer("Kontext prompt reverted to original.", "info", "kontext-logs-content");
        const statusElement = document.getElementById('kontextImprovePromptStatus');
        if (statusElement && statusElement.innerHTML.includes('fa-check')) {
            statusElement.innerHTML = "";
        }
    }
    if (revertButton) {
        revertButton.classList.add('hidden');
    }
}

/**
 * Sets up event listeners for the main generation form.
 */
function setupMainFormEventListeners() {
    const improvePromptButton = document.getElementById('improve-prompt-btn');
    if (improvePromptButton) {
        improvePromptButton.addEventListener('click', handleImprovePromptClick);
    }

    // Add other main form listeners here if any in the future
    // e.g. const runButton = document.getElementById('run-btn');
    // if (runButton) { runButton.addEventListener('click', handleRunButtonClick); } // Assuming handleRunButtonClick exists
}


// Export form handler functions
window.formHandlers = {
    collectInputData,
    handleResetButtonClick,
    handleRandomizeSeed,
    applyPreset,
    collectKontextInputData,
    handleSubmitKontextEdit,
    setupKontextEventListeners,
    handleImprovePromptClick,        // For the main prompt (Flux Loras)
    handleRevertKontextPromptClick,  // For Kontext Pro prompt
    setupMainFormEventListeners
    // handleImproveKontextPromptClick (for Kontext Pro) is attached via setupKontextEventListeners
    // and generally not called directly via window.formHandlers.
};

/**
 * Sets up the clear button for the Kontext Prompt textarea.
 */
function setupKontextPromptClearButton() {
    const kontextPromptTextarea = document.getElementById('kontextPrompt');
    const clearButton = document.getElementById('clearKontextPromptBtn');
    const textareaWrapper = kontextPromptTextarea ? kontextPromptTextarea.parentElement : null;

    if (!kontextPromptTextarea || !clearButton || !textareaWrapper) {
        console.warn("Kontext Prompt textarea, clear button, or wrapper not found. Clear button functionality not initialized.");
        return;
    }

    // Function to update button visibility
    const updateClearButtonVisibility = () => {
        if (kontextPromptTextarea.value.length > 0) {
            clearButton.classList.remove('hidden');
        } else {
            clearButton.classList.add('hidden');
        }
    };

    // Initial check on load
    updateClearButtonVisibility();

    // Event listener for input changes
    kontextPromptTextarea.addEventListener('input', updateClearButtonVisibility);

    // Event listener for clear button click
    clearButton.addEventListener('click', () => {
        kontextPromptTextarea.value = '';
        updateClearButtonVisibility(); // Hide button after clearing
        kontextPromptTextarea.focus(); // Keep focus on textarea
    });

    // Show/hide button on wrapper hover (as per user clarification)
    textareaWrapper.addEventListener('mouseenter', () => {
        if (kontextPromptTextarea.value.length > 0) {
            clearButton.classList.remove('hidden');
        }
    });

    textareaWrapper.addEventListener('mouseleave', () => {
        if (kontextPromptTextarea.value.length > 0) { // Only hide if textarea is empty
            clearButton.classList.add('hidden');
        }
    });
}

// Initialize the clear button setup when the DOM is ready
document.addEventListener('DOMContentLoaded', setupKontextPromptClearButton);
