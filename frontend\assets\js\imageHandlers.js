/**
 * Image handlers module
 * Handles image-related operations
 */

// Store all generated images
let generatedImages = [];
let currentImageIndex = 0;

/**
 * Cleans up the gallery
 */
function cleanupGallery() {
    // Remove keyboard event listener
    document.removeEventListener('keydown', handleGalleryKeyNavigation);

    // Remove image counter if it exists
    const imageCounter = document.getElementById('image-counter');
    if (imageCounter) {
        imageCounter.remove();
    }

    // Clear stored images
    generatedImages = [];
    currentImageIndex = 0;
}

/**
 * Initializes the gallery with images
 * @param {Array} images - Array of image objects with url and content_type properties
 */
function initGallery(images) {
    console.log("Initializing gallery with images:", images);

    // Clean up previous gallery
    cleanupGallery();

    if (!images || !Array.isArray(images) || images.length === 0) {
        console.warn("No images provided to gallery");
        return;
    }

    // Store images
    generatedImages = images;
    currentImageIndex = 0;

    // Show first image
    showImageAtIndex(0);

    // Setup gallery navigation
    setupGalleryNavigation();
}

/**
 * Sets up gallery navigation
 */
function setupGalleryNavigation() {
    const galleryNavigation = document.getElementById('gallery-navigation');
    const galleryIndicator = document.getElementById('gallery-indicator');

    // Only show navigation if we have multiple images
    if (generatedImages.length > 1) {
        galleryNavigation.classList.remove('hidden');
        galleryIndicator.classList.remove('hidden');

        // Setup navigation buttons
        const prevButton = document.getElementById('prev-image-btn');
        const nextButton = document.getElementById('next-image-btn');

        prevButton.onclick = showPreviousImage;
        nextButton.onclick = showNextImage;

        // Create indicator dots
        galleryIndicator.innerHTML = '';
        for (let i = 0; i < generatedImages.length; i++) {
            const dot = document.createElement('div');
            dot.className = 'gallery-dot' + (i === 0 ? ' active' : '');
            dot.onclick = () => showImageAtIndex(i);
            galleryIndicator.appendChild(dot);
        }

        // Add image counter
        const imageCounter = document.createElement('div');
        imageCounter.id = 'image-counter';
        imageCounter.className = 'image-counter';
        imageCounter.textContent = `1/${generatedImages.length}`;
        document.getElementById('image-gallery').appendChild(imageCounter);

        // Add keyboard navigation
        document.addEventListener('keydown', handleGalleryKeyNavigation);
    } else {
        galleryNavigation.classList.add('hidden');
        galleryIndicator.classList.add('hidden');
    }
}

/**
 * Handles keyboard navigation for the gallery
 * @param {KeyboardEvent} event - Keyboard event
 */
function handleGalleryKeyNavigation(event) {
    // Only handle keyboard navigation if gallery is visible
    const imageGallery = document.getElementById('image-gallery');
    if (!imageGallery || imageGallery.classList.contains('hidden')) {
        return;
    }

    // Check if lightbox is open
    const lightboxModal = document.getElementById('image-lightbox-modal');
    if (lightboxModal && !lightboxModal.classList.contains('hidden')) {
        return; // Don't handle gallery navigation when lightbox is open
    }

    if (event.key === 'ArrowLeft') {
        showPreviousImage();
        event.preventDefault();
    } else if (event.key === 'ArrowRight') {
        showNextImage();
        event.preventDefault();
    }
}

/**
 * Shows the image at the specified index
 * @param {number} index - Index of the image to show
 */
function showImageAtIndex(index) {
    if (index < 0 || index >= generatedImages.length) {
        console.warn("Invalid image index:", index);
        return;
    }

    currentImageIndex = index;

    // Update image
    const imageElement = document.getElementById('generated-image');
    const imageGallery = document.getElementById('image-gallery');
    const placeholder = document.getElementById('image-placeholder');
    const downloadBtn = document.getElementById('download-btn');

    // Show image
    placeholder.classList.add('hidden');
    imageGallery.classList.remove('hidden');
    downloadBtn.classList.remove('hidden');

    const image = generatedImages[index];
    imageElement.src = image.url;
    imageElement.dataset.contentType = image.content_type || 'image/jpeg';

    // Update indicator dots
    const dots = document.querySelectorAll('#gallery-indicator .gallery-dot');
    dots.forEach((dot, i) => {
        if (i === index) {
            dot.classList.add('active');
        } else {
            dot.classList.remove('active');
        }
    });

    // Update image counter if it exists
    const imageCounter = document.getElementById('image-counter');
    if (imageCounter) {
        imageCounter.textContent = `${index + 1}/${generatedImages.length}`;
    }

    // Update lightbox image if it exists
    const lbImage = document.getElementById('lightbox-image');
    if (lbImage) {
        lbImage.dataset.contentType = image.content_type || 'image/jpeg';
    }
}

/**
 * Shows the next image in the gallery
 */
function showNextImage() {
    let nextIndex = currentImageIndex + 1;
    if (nextIndex >= generatedImages.length) {
        nextIndex = 0; // Loop back to the first image
    }
    showImageAtIndex(nextIndex);
}

/**
 * Shows the previous image in the gallery
 */
function showPreviousImage() {
    let prevIndex = currentImageIndex - 1;
    if (prevIndex < 0) {
        prevIndex = generatedImages.length - 1; // Loop to the last image
    }
    showImageAtIndex(prevIndex);
}

/**
 * Handles downloading the current image
 */
function handleDownloadImage() {
    console.log("Downloading image from main button");

    if (generatedImages.length === 0 || currentImageIndex < 0 || currentImageIndex >= generatedImages.length) {
        window.ui.addLogMessage("No image available to download.", "error");
        return;
    }

    const currentImage = generatedImages[currentImageIndex];
    const imageUrl = currentImage.url;
    let fileName = 'generated-image.jpg';

    try {
        const url = new URL(imageUrl);
        const pathParts = url.pathname.split('/');
        const lastPart = pathParts[pathParts.length - 1];
        if (lastPart) {
            fileName = lastPart.replace(/[^a-zA-Z0-9.-]/g, '_');
            if (!/\.(jpe?g|png|gif|webp)$/i.test(fileName)) {
                const contentType = currentImage.content_type || 'image/jpeg';
                const ext = contentType.split('/')[1] || 'jpg';
                fileName += '.' + ext;
            }
        }
    } catch (e) {
        console.warn("Could not parse URL for filename from main image, using default.", e);
    }

    const link = document.createElement('a');
    link.href = imageUrl;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.ui.addLogMessage("Download initiated for: " + fileName, "info");
}

/**
 * Gets the current image URL
 * @returns {string} URL of the current image
 */
function getCurrentImageUrl() {
    if (generatedImages.length === 0 || currentImageIndex < 0 || currentImageIndex >= generatedImages.length) {
        return null;
    }
    return generatedImages[currentImageIndex].url;
}

/**
 * Gets the current image content type
 * @returns {string} Content type of the current image
 */
function getCurrentImageContentType() {
    if (generatedImages.length === 0 || currentImageIndex < 0 || currentImageIndex >= generatedImages.length) {
        return 'image/jpeg';
    }
    return generatedImages[currentImageIndex].content_type || 'image/jpeg';
}

// Export image handler functions
window.imageHandlers = {
    initGallery,
    cleanupGallery,
    handleDownloadImage,
    showNextImage,
    showPreviousImage,
    showImageAtIndex,
    getCurrentImageUrl,
    getCurrentImageContentType
};
