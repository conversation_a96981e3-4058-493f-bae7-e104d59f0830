/**
 * Main application script
 * Initializes the application and sets up event listeners
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize UI Elements
    window.ui.setupSliderSync(); // Sets up sliders for the default (Flux Loras) panel initially
    window.ui.initializeInterfaceToggle(); // Initialize the icon-based interface toggle
    window.ui.setupPromptPills(); // Initialize prompt pills functionality
    window.ui.setupCollapsiblePrompts(); // Initialize collapsible prompt sections
    window.ui.loadQuickPrompts(); // Load and initialize tabbed quick prompts from JSON

    // Setup event listeners for the Kontext Pro panel (sliders, buttons etc.)
    if (window.formHandlers && window.formHandlers.setupKontextEventListeners) {
        window.formHandlers.setupKontextEventListeners();
    }
    // Setup Kontext Gallery Navigation
    if (window.ui && window.ui.setupKontextGalleryNav) {
        window.ui.setupKontextGalleryNav();
    }

    // Setup event listeners for the main "Standard" form (e.g., Improve Prompt button)
    if (window.formHandlers && window.formHandlers.setupMainFormEventListeners) {
        window.formHandlers.setupMainFormEventListeners();
    }

    // Initialize preset manager
    if (window.presetManager) {
        // Fix any existing presets in localStorage
        if (window.presetManager.fixAllPresets) {
            window.presetManager.fixAllPresets();
        }

        window.presetManager.initPresetManager();

        // Add a global function to fix presets (can be called from console)
        window.fixSafetyCheckerInPresets = function() {
            console.log("Manual fix for Safety Checker in presets initiated");
            if (window.presetManager && window.presetManager.fixAllPresets) {
                window.presetManager.fixAllPresets();
                console.log("Fix completed. Reloading presets...");
                window.presetManager.loadPresetsFromStorage();
                return "Fix completed and presets reloaded";
            } else {
                console.error("Preset manager or fixAllPresets function not available");
                return "Error: Preset manager not available";
            }
        };
    }

    // Image size change handler
    document.getElementById('image-size').addEventListener('change', function() {
        window.ui.updateImageSizeDisplay(this.value);
    });

    // Custom size input handlers
    const customWidth = document.getElementById('custom-width');
    const customHeight = document.getElementById('custom-height');

    if (customWidth && customHeight) {
        // Update display when custom width changes
        customWidth.addEventListener('input', function() {
            // Ensure value is a multiple of 8
            const value = parseInt(this.value, 10);
            if (!isNaN(value)) {
                const adjustedValue = Math.min(Math.max(Math.round(value / 8) * 8, 256), 1024);
                if (this.value !== adjustedValue.toString()) {
                    this.value = adjustedValue;
                }
                window.ui.updateImageSizeDisplay('custom');
            }
        });

        // Update display when custom height changes
        customHeight.addEventListener('input', function() {
            // Ensure value is a multiple of 8
            const value = parseInt(this.value, 10);
            if (!isNaN(value)) {
                const adjustedValue = Math.min(Math.max(Math.round(value / 8) * 8, 256), 1024);
                if (this.value !== adjustedValue.toString()) {
                    this.value = adjustedValue;
                }
                window.ui.updateImageSizeDisplay('custom');
            }
        });
    }

    // Initialize lightbox
    window.lightbox.initLightbox();

    // --- Main Action: Run Button ---
    const runButton = document.getElementById('run-btn');
    if (runButton) {
        runButton.addEventListener('click', async function handleRunButtonClick() {
            console.log("Run button clicked");
            const inputData = window.formHandlers.collectInputData();

            window.ui.updateStatusIndicator("In Progress...", false);
            window.ui.addLogMessage("Requesting image generation...", "info");
            window.ui.showIndeterminateProgress("Generating image...");

            // Reset gallery and hide image
            const imageGallery = document.getElementById('image-gallery');
            const placeholder = document.getElementById('image-placeholder');
            const downloadBtn = document.getElementById('download-btn');

            if (imageGallery) imageGallery.classList.add('hidden');
            if (placeholder) placeholder.classList.remove('hidden');
            if (downloadBtn) downloadBtn.classList.add('hidden');

            // Clean up gallery
            if (window.imageHandlers && window.imageHandlers.cleanupGallery) {
                window.imageHandlers.cleanupGallery();
            }

            try {
                const resultData = await window.api.generateImage(inputData);

                if (resultData.success) {
                    if (resultData.isNsfwDetected) {
                        window.ui.addLogMessage("Warning: Generated image flagged as NSFW by the API.", "error");
                        window.ui.updateStatusIndicator("Completed with Warning", false);
                        const placeholder = document.getElementById('image-placeholder');
                        const imageElement = document.getElementById('generated-image');
                        const downloadBtn = document.getElementById('download-btn');
                        imageElement.classList.add('hidden');
                        imageElement.src = '';
                        placeholder.classList.remove('hidden');
                        downloadBtn.classList.add('hidden');
                        placeholder.innerHTML = `
                            <div class="text-center">
                                <i class="fas fa-exclamation-triangle fa-3x mb-2 text-yellow-500"></i>
                                <p>Зображення позначено як потенційно неприйнятне (NSFW) і не може бути відображене.</p>
                            </div>`;
                        window.ui.addLogMessage("Displaying NSFW warning instead of image.", "info");
                    } else {
                        window.ui.updateStatusIndicator("Completed", false);
                        window.ui.addLogMessage("Image generation completed successfully!", "success");
                        window.ui.displayGeneratedImages(resultData);
                    }
                    document.getElementById('request-info').textContent = resultData.requestInfo || `Seed used: ${resultData.seed || 'N/A'}.`;
                } else {
                    window.ui.addLogMessage(`Error from server: ${resultData.message || 'Unknown server error.'}`, "error");
                    window.ui.updateStatusIndicator("Error", true);
                }
            } catch (error) {
                console.error("Error calling backend:", error);
                window.ui.updateStatusIndicator("Error", true);
                window.ui.addLogMessage(`Network/Fetch Error: ${error.message}`, "error");
            } finally {
                window.ui.hideIndeterminateProgress();
            }
        });
    } else {
        console.error("Run button #run-btn not found");
    }

    // Attach event listeners for other buttons
    document.getElementById('reset-btn').addEventListener('click', window.formHandlers.handleResetButtonClick);

    // Initial remove buttons for pre-existing Lora items
    document.querySelectorAll('#loras-container .lora-item .remove-lora').forEach(btn => {
        btn.addEventListener('click', function() {
            window.loraManager.handleRemoveLoraItem(btn.closest('.lora-item'));
        });
    });

    // Keyboard shortcut for run button (Ctrl+Enter)
    document.addEventListener('keydown', function(event) {
        if ((event.ctrlKey || event.metaKey) && event.key === 'Enter') {
            if (runButton) runButton.click();
        }
    });
});
