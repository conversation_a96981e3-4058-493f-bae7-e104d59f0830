/**
 * Sets up the event listeners for the prompt pills.
 */
function setupPromptPills() {
    const pillContainers = document.querySelectorAll('.prompt-pills-container');
    pillContainers.forEach(container => {
        const promptTargetId = container.id.includes('flux') ? 'prompt' : 'kontextPrompt';
        const promptTextarea = document.getElementById(promptTargetId);

        if (promptTextarea) {
            container.addEventListener('click', function(event) {
                const pill = event.target.closest('.prompt-pill');
                if (pill) {
                    const textToAppend = pill.dataset.promptText;
                    // Append text and a space
                    promptTextarea.value += textToAppend;
                    promptTextarea.focus();
                    // Move cursor to the end
                    promptTextarea.selectionStart = promptTextarea.selectionEnd = promptTextarea.value.length;
                }
            });
        }
    });
}

/**
 * Dynamically loads quick prompts from a JSON file and generates the tabbed interface.
 */
async function loadQuickPrompts() {
    const quickPromptsContainer = document.getElementById('quick-prompts-container');
    if (!quickPromptsContainer) {
        console.error("Quick prompts container not found.");
        return;
    }

    try {
        const response = await fetch('/data/prompts.json');
        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }
        const categories = await response.json();

        let tabButtonsHtml = '';
        let tabContentsHtml = '';

        categories.forEach((category, index) => {
            const categoryId = `tab-panel-${category.category.toLowerCase().replace(/\s/g, '-')}`;
            const isActive = index === 0 ? 'active-tab text-blue-400 border-blue-500' : 'text-gray-400 border-transparent';
            const isHidden = index === 0 ? '' : 'hidden';
            const ariaSelected = index === 0 ? 'true' : 'false';
            const tabIndex = index === 0 ? '0' : '-1';

            tabButtonsHtml += `
                <button class="tab-button py-2 px-4 border-b-2 font-medium text-sm focus:outline-none ${isActive}"
                        data-tab-target="#${categoryId}" role="tab" aria-controls="${categoryId}" aria-selected="${ariaSelected}" tabindex="${tabIndex}">
                    ${category.category}
                </button>
            `;

            let promptPillsHtml = '';
            category.prompts.forEach(prompt => {
                promptPillsHtml += `
                    <span class="prompt-pill bg-gray-700 text-gray-200 px-3 py-1 rounded-full text-sm cursor-pointer hover:bg-gray-600 transition-colors duration-200"
                          data-prompt-text="${prompt.value}">
                        ${prompt.label}
                    </span>
                `;
            });

            tabContentsHtml += `
                <div id="${categoryId}" class="tab-content p-4 ${isHidden}" role="tabpanel" aria-labelledby="${categoryId}-tab">
                    <div class="prompt-pills-container flex flex-wrap gap-2" id="kontext-prompt-pills-${category.category.toLowerCase().replace(/\s/g, '-')}" data-prompt-target="kontextPrompt">
                        ${promptPillsHtml}
                    </div>
                </div>
            `;
        });

        quickPromptsContainer.innerHTML = `
            <div class="flex border-b border-gray-700" role="tablist" aria-label="Quick Prompt Categories">
                ${tabButtonsHtml}
            </div>
            <div class="mt-4">
                ${tabContentsHtml}
            </div>
        `;

        // After content is loaded, initialize tab functionality and prompt pills
        initializeQuickPromptTabs();
        setupPromptPills(); // Re-run setup for dynamically added pills

    } catch (error) {
        console.error("Failed to load quick prompts:", error);
        addLogMessage("Failed to load quick prompts. Please check console for details.", "error");
    }
}

/**
 * Initializes the tabbed quick prompt functionality for dynamically loaded content.
 */
function initializeQuickPromptTabs() {
    const tabButtons = document.querySelectorAll('.tab-button');
    const tabContents = document.querySelectorAll('.tab-content');

    tabButtons.forEach(button => {
        button.addEventListener('click', () => {
            const targetPanelId = button.dataset.tabTarget;
            const targetPanel = document.querySelector(targetPanelId);

            // Deactivate all tabs and hide all content
            tabButtons.forEach(btn => {
                btn.classList.remove('active-tab', 'text-blue-400', 'border-blue-500');
                btn.classList.add('text-gray-400', 'border-transparent');
                btn.setAttribute('aria-selected', 'false');
                btn.setAttribute('tabindex', '-1');
            });
            tabContents.forEach(content => {
                content.classList.add('hidden');
                content.setAttribute('aria-hidden', 'true');
            });

            // Activate clicked tab and show its content
            button.classList.add('active-tab', 'text-blue-400', 'border-blue-500');
            button.classList.remove('text-gray-400', 'border-transparent');
            button.setAttribute('aria-selected', 'true');
            button.setAttribute('tabindex', '0');
            targetPanel.classList.remove('hidden');
            targetPanel.setAttribute('aria-hidden', 'false');
        });

        // Keyboard navigation for tabs
        button.addEventListener('keydown', (e) => {
            let index = Array.from(tabButtons).indexOf(e.target);
            let nextButton;

            switch (e.key) {
                case 'ArrowLeft':
                    nextButton = tabButtons[index - 1] || tabButtons[tabButtons.length - 1];
                    break;
                case 'ArrowRight':
                    nextButton = tabButtons[index + 1] || tabButtons[0];
                    break;
                case 'Home':
                    nextButton = tabButtons[0];
                    break;
                case 'End':
                    nextButton = tabButtons[tabButtons.length - 1];
                    break;
                default:
                    return; // Do nothing for other keys
            }

            e.preventDefault();
            nextButton.focus();
            nextButton.click(); // Activate the tab on focus
        });
    });
}

/**
 * UI module
 * Handles UI updates and interactions
 */

/**
 * Updates the status indicator
 * @param {string} statusText - Text to display in the status indicator
 * @param {boolean} isError - Whether this is an error status
 */
function updateStatusIndicator(statusText, isError = false) {
    const indicator = document.getElementById('status-indicator');
    indicator.className = 'status-indicator ';
    if (statusText.toLowerCase().includes('progress')) indicator.classList.add('status-progress');
    else if (statusText.toLowerCase().includes('complete with warning')) indicator.classList.add('status-progress');
    else if (statusText.toLowerCase().includes('complete')) indicator.classList.add('status-completed');
    else if (isError) indicator.classList.add('status-error');
    else indicator.classList.add('status-default');
    indicator.textContent = statusText;
}

/**
 * Adds a log message to the logs container
 * @param {string} message - Message to log
 * @param {string} type - Type of log (info, error, success)
 */
function addLogMessage(message, type = 'info') {
    const logEntry = document.createElement('div');
    logEntry.className = type + '-log';
    logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
    const logsContent = document.getElementById('logs-content');
    logsContent.appendChild(logEntry);
    logsContent.scrollTop = logsContent.scrollHeight;
}

/**
 * Displays generated images
 * @param {Object} resultData - Result data from the API
 */
function displayGeneratedImages(resultData) {
    console.log("Displaying generated images from result data:", resultData);
    let imagesArray = null;

    // Try Structure 1 (deeper nesting: response.data.data.images)
    if (resultData.data && resultData.data.data &&
        resultData.data.data.images &&
        Array.isArray(resultData.data.data.images) &&
        resultData.data.data.images.length > 0) {
        imagesArray = resultData.data.data.images;
        console.log(`Found ${imagesArray.length} images via Structure 1 (response.data.data.images)`);
    }
    // Try Structure 2 (shallower nesting: response.data.images)
    else if (resultData.data && resultData.data.images &&
             Array.isArray(resultData.data.images) &&
             resultData.data.images.length > 0) {
        imagesArray = resultData.data.images;
        console.log(`Found ${imagesArray.length} images via Structure 2 (response.data.images)`);
    }
    // Fallback to single imageUrl (existing logic)
    else if (resultData.imageUrl) {
        console.log("No images array found after checking structures, using single imageUrl:", resultData.imageUrl);
        const singleImage = {
            url: resultData.imageUrl,
            content_type: resultData.contentType || 'image/jpeg',
            width: 1024, // Default width
            height: 1024  // Default height
        };
        imagesArray = [singleImage];
    }

    if (imagesArray && imagesArray.length > 0) {
        // Initialize gallery with all found images
        window.imageHandlers.initGallery(imagesArray);

        // Add image count indicator if more than one image
        if (imagesArray.length > 1) {
            window.ui.addLogMessage(`Generated ${imagesArray.length} images successfully!`, "success");
        }
    } else {
        console.error("No images found in the result data after checking all known structures.");
        addLogMessage("No images found in the API response after checking all known structures.", "error");
    }
}

/**
 * Shows indeterminate progress bar
 * @param {string} statusText - Text to display above the progress bar
 */
function showIndeterminateProgress(statusText) {
    const generationProgressArea = document.getElementById('generation-progress-area');
    const progressBarIndicator = document.getElementById('progress-bar-indicator');
    const progressStatusText = document.getElementById('progress-status-text');

    if (generationProgressArea && progressBarIndicator && progressStatusText) {
        generationProgressArea.classList.remove('hidden');
        progressBarIndicator.classList.add('indeterminate');
        if (statusText) {
            progressStatusText.textContent = statusText;
        }
    }
}

/**
 * Hides the progress bar
 */
function hideIndeterminateProgress() {
    const generationProgressArea = document.getElementById('generation-progress-area');
    const progressBarIndicator = document.getElementById('progress-bar-indicator');
    const progressStatusText = document.getElementById('progress-status-text');

    if (generationProgressArea && progressBarIndicator && progressStatusText) {
        generationProgressArea.classList.add('hidden');
        progressBarIndicator.classList.remove('indeterminate');
        progressStatusText.textContent = 'Initializing...';
    }
}

/**
 * Updates the image size display based on the selected size option
 * @param {string} selectedValue - Selected image size option
 */
function updateImageSizeDisplay(selectedValue) {
    let dimensions = "";

    // Show/hide custom size inputs based on selection
    const customSizeContainer = document.getElementById('custom-size-container');
    if (selectedValue === 'custom') {
        customSizeContainer.classList.remove('hidden');
        const width = document.getElementById('custom-width').value;
        const height = document.getElementById('custom-height').value;
        dimensions = `${width} x ${height}`;
    } else {
        if (customSizeContainer) {
            customSizeContainer.classList.add('hidden');
        }

        // Predefined sizes
        switch (selectedValue) {
            case "square_hd": dimensions = "1024 x 1024"; break;
            case "square": dimensions = "1024 x 1024"; break;
            case "portrait_3_4": dimensions = "768 x 1024"; break;
            case "portrait_16_9": dimensions = "576 x 1024"; break;
            case "landscape_4_3": dimensions = "1024 x 768"; break;
            case "landscape_16_9": dimensions = "1024 x 576"; break;
            // Legacy format support
            case "3:4": dimensions = "768 x 1024"; break;
            case "1:1": dimensions = "1024 x 1024"; break;
            case "4:3": dimensions = "1024 x 768"; break;
            case "16:9": dimensions = "1024 x 576"; break;
            default: dimensions = "Custom size";
        }
    }

    document.getElementById('image-size-display').textContent = dimensions;
}

/**
 * Toggles the visibility of a section
 * @param {string} sectionId - ID of the section to toggle
 * @param {HTMLElement} spanElement - Span element containing the text
 * @param {HTMLElement} iconElement - Icon element
 */
function toggleSectionVisibility(sectionId, spanElement, iconElement) {
    const section = document.getElementById(sectionId);
    if (!section || !spanElement || !iconElement) return;

    const isHidden = section.classList.contains('hidden');
    if (isHidden) {
        section.classList.remove('hidden');
        spanElement.textContent = 'Less '; // Set text part
        iconElement.className = 'fas fa-chevron-up ml-1'; // Set icon part
    } else {
        section.classList.add('hidden');
        spanElement.textContent = 'Show ';
        iconElement.className = 'fas fa-chevron-down ml-1';
    }
}

/**
 * Sets up synchronization between sliders and number inputs
 */
function setupSliderSync() {
    document.querySelectorAll('.slider').forEach(slider => {
        const numberInput = slider.nextElementSibling;
        if (numberInput && numberInput.type === 'number') {
            slider.addEventListener('input', function() { numberInput.value = this.value; });
            numberInput.addEventListener('input', function() {
                let value = parseFloat(this.value);
                const min = parseFloat(slider.min);
                const max = parseFloat(slider.max);
                if (isNaN(value)) value = parseFloat(slider.defaultValue) || min;
                if (value < min) value = min;
                if (value > max) value = max;
                this.value = value;
                slider.value = value;
            });
        }
    });
}

/**
 * Switches the active interface panel.
 * @param {string} interfaceIdToShow - The ID of the interface panel to show.
 */
function switchInterfacePanel(interfaceIdToShow) {
    const interfacePanels = document.querySelectorAll('.interface-panel');
    interfacePanels.forEach(panel => {
        if (panel.id === interfaceIdToShow) {
            panel.classList.remove('hidden'); // Show by removing Tailwind's hidden class
            panel.classList.add('active-panel'); // Keep for styling active state if needed
        } else {
            panel.classList.add('hidden');    // Hide by adding Tailwind's hidden class
            panel.classList.remove('active-panel'); // Keep for styling active state if needed
        }
    });
    // Optionally, reset forms or clear results in the hidden panel
    // For example, clearKontextForm(); or clearFluxLorasForm();
    console.log(`Switched to interface: ${interfaceIdToShow}`);
    addLogMessage(`Switched to ${interfaceIdToShow.replace(/([A-Z])/g, ' $1').trim()} interface.`);
}

/**
 * Initializes the interface selector dropdown.
 */
function initializeInterfaceToggle() {
    const fluxLorasToggle = document.getElementById('fluxLorasToggle');
    const kontextProToggle = document.getElementById('kontextProToggle');
    
    if (fluxLorasToggle && kontextProToggle) {
        const setActiveTab = (activeTab, inactiveTab) => {
            // Style active tab
            activeTab.classList.add('active', 'border-blue-500', 'text-blue-400', '-mb-px');
            activeTab.classList.remove('text-gray-400');

            // Style inactive tab
            inactiveTab.classList.remove('active', 'border-blue-500', 'text-blue-400', '-mb-px');
            inactiveTab.classList.add('text-gray-400');
        };

        fluxLorasToggle.addEventListener('click', () => {
            switchInterfacePanel('fluxLorasInterface');
            setActiveTab(fluxLorasToggle, kontextProToggle);
        });
        
        kontextProToggle.addEventListener('click', () => {
            switchInterfacePanel('kontextProInterface');
            setActiveTab(kontextProToggle, fluxLorasToggle);
        });
        
        // Set initial active state.
        // The HTML should define kontextProToggle as active by default.
        // This JS ensures the panel and styles are correctly applied on load.
        if (kontextProToggle.classList.contains('active')) {
            switchInterfacePanel('kontextProInterface');
            setActiveTab(kontextProToggle, fluxLorasToggle); // Ensures styles are correctly applied
        } else if (fluxLorasToggle.classList.contains('active')) {
            // This case should ideally not happen if HTML is set up with Kontext Pro active by default
            switchInterfacePanel('fluxLorasInterface');
            setActiveTab(fluxLorasToggle, kontextProToggle);
        } else {
            // Fallback: if no tab is marked active in HTML, default to Kontext Pro
            switchInterfacePanel('kontextProInterface');
            setActiveTab(kontextProToggle, fluxLorasToggle);
        }
    }
}

/**
 * Displays the preview of the image selected for Kontext editing.
 * @param {Event} event - The file input change event.
 */
function displayKontextImagePreview(event) {
    const preview = document.getElementById('kontextImagePreview');
    const file = event.target.files[0];
    if (file && preview) {
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.classList.remove('hidden');

            // Get image dimensions and auto-select aspect ratio
            const img = new Image();
            img.onload = function() {
                autoSelectKontextAspectRatio(img.naturalWidth, img.naturalHeight);
            };
            img.src = e.target.result;
        }
        reader.readAsDataURL(file);
    } else if (preview) {
        preview.classList.add('hidden');
        preview.src = "#";
        // Optionally reset aspect ratio dropdown if image is cleared
        // const aspectRatioSelect = document.getElementById('kontextAspectRatio');
        // if (aspectRatioSelect) aspectRatioSelect.value = "1:1"; // Default or last known good
    }
}

/**
 * Automatically selects the closest aspect ratio in the Kontext Pro dropdown
 * based on the uploaded image's dimensions.
 * @param {number} width - The width of the uploaded image.
 * @param {number} height - The height of the uploaded image.
 */
function autoSelectKontextAspectRatio(width, height) {
    if (height === 0) {
        console.warn("Uploaded image height is 0, cannot determine aspect ratio.");
        return; // Or default to 1:1
    }

    const actualRatio = width / height;
    const aspectRatioSelect = document.getElementById('kontextAspectRatio');
    if (!aspectRatioSelect) {
        console.error("kontextAspectRatio select element not found.");
        return;
    }

    const predefinedRatios = [
        { value: "1:1", decimal: 1.0 },
        { value: "16:9", decimal: 16/9 },
        { value: "9:16", decimal: 9/16 },
        { value: "4:3", decimal: 4/3 },
        { value: "3:4", decimal: 3/4 },
        { value: "3:2", decimal: 3/2 },
        { value: "2:3", decimal: 2/3 },
        { value: "21:9", decimal: 21/9 },
        { value: "9:21", decimal: 9/21 }
    ];

    let closestMatch = predefinedRatios[0]; // Default to first option
    let minDifference = Math.abs(actualRatio - closestMatch.decimal);

    for (let i = 1; i < predefinedRatios.length; i++) {
        const difference = Math.abs(actualRatio - predefinedRatios[i].decimal);
        if (difference < minDifference) {
            minDifference = difference;
            closestMatch = predefinedRatios[i];
        }
    }

    aspectRatioSelect.value = closestMatch.value;
    console.log(`Uploaded image ratio: ${actualRatio.toFixed(3)}. Selected aspect ratio: ${closestMatch.value}`);
    // Optionally dispatch a change event if other UI elements depend on it
    // aspectRatioSelect.dispatchEvent(new Event('change'));
}

/**
 * Updates the status indicator for the Kontext Pro panel.
 * @param {string} statusText - Text to display.
 * @param {boolean} isError - Whether this is an error status.
 */
function updateKontextStatusIndicator(statusText, isError = false) {
    const indicator = document.getElementById('kontextStatusIndicator');
    if (!indicator) return;
    indicator.className = 'status-indicator '; // Reset classes
    if (statusText.toLowerCase().includes('progress')) indicator.classList.add('status-progress');
    else if (statusText.toLowerCase().includes('complete')) indicator.classList.add('status-completed');
    else if (isError) indicator.classList.add('status-error');
    else indicator.classList.add('status-default');
    indicator.textContent = statusText;
}

// --- Kontext Pro Gallery and Lightbox Logic ---
let kontextImagesArray = [];
let currentKontextImageIndex = 0;

/**
 * Initializes the gallery for Kontext Pro results.
 * @param {Array} images - Array of image objects from the API.
 */
function initKontextGallery(images) {
    kontextImagesArray = images || [];
    currentKontextImageIndex = 0;

    const galleryNav = document.getElementById('kontextGalleryNavigation');
    const galleryIndicator = document.getElementById('kontextGalleryIndicator');
    const imageElement = document.getElementById('kontextGeneratedImage');
    const placeholder = document.getElementById('kontextImagePlaceholder');

    if (!imageElement || !placeholder) return;

    if (kontextImagesArray.length > 0) {
        displayCurrentKontextImage();
        placeholder.classList.add('hidden');
        imageElement.classList.remove('hidden');
        imageElement.onclick = () => {
            if (kontextImagesArray[currentKontextImageIndex] && window.lightbox) {
                window.lightbox.openLightbox(kontextImagesArray[currentKontextImageIndex].url, "Kontext Edited Image");
            }
        };
    } else {
        placeholder.classList.remove('hidden');
        imageElement.classList.add('hidden');
        if (galleryNav) galleryNav.classList.add('hidden');
        if (galleryIndicator) galleryIndicator.innerHTML = ''; galleryIndicator.classList.add('hidden');
        return;
    }

    if (galleryNav && galleryIndicator) {
        if (kontextImagesArray.length > 1) {
            galleryNav.classList.remove('hidden');
            galleryIndicator.classList.remove('hidden');
            updateKontextGalleryIndicator();
        } else {
            galleryNav.classList.add('hidden');
            galleryIndicator.classList.add('hidden');
        }
    }
}

/**
 * Displays the current image in the Kontext Pro gallery.
 */
function displayCurrentKontextImage() {
    if (kontextImagesArray.length === 0) return;

    const imageElement = document.getElementById('kontextGeneratedImage');
    const downloadBtnContainer = document.getElementById('kontextDownloadBtn');
    const currentImage = kontextImagesArray[currentKontextImageIndex];

    if (imageElement) {
        imageElement.src = currentImage.url || currentImage.image_url || '';
    }

    if (downloadBtnContainer) {
        downloadBtnContainer.classList.remove('hidden');
        // Ensure imageHandlers and handleDownloadSpecificImage are available
        if (window.imageHandlers && window.imageHandlers.handleDownloadSpecificImage) {
             downloadBtnContainer.onclick = () => window.imageHandlers.handleDownloadSpecificImage(currentImage.url, `kontext_edited_${currentImage.seed || Date.now()}.jpg`);
        } else {
            console.warn("window.imageHandlers.handleDownloadSpecificImage not found for Kontext download button.");
        }
    }
    updateKontextGalleryIndicator();
}

/**
 * Updates the dot indicators for the Kontext Pro gallery.
 */
function updateKontextGalleryIndicator() {
    const galleryIndicator = document.getElementById('kontextGalleryIndicator');
    if (!galleryIndicator || kontextImagesArray.length <= 1) {
        if(galleryIndicator) galleryIndicator.innerHTML = ''; // Clear if not needed
        return;
    }
    galleryIndicator.innerHTML = ''; // Clear existing dots
    kontextImagesArray.forEach((_, index) => {
        const dot = document.createElement('span');
        dot.classList.add('gallery-dot');
        if (index === currentKontextImageIndex) {
            dot.classList.add('active');
        }
        dot.onclick = () => {
            currentKontextImageIndex = index;
            displayCurrentKontextImage();
        };
        galleryIndicator.appendChild(dot);
    });
}

/**
 * Sets up event listeners for Kontext Pro gallery navigation.
 */
function setupKontextGalleryNav() {
    const prevBtn = document.getElementById('kontextPrevImageBtn');
    const nextBtn = document.getElementById('kontextNextImageBtn');

    if (prevBtn) {
        prevBtn.onclick = () => {
            if (kontextImagesArray.length > 0) {
                currentKontextImageIndex = (currentKontextImageIndex - 1 + kontextImagesArray.length) % kontextImagesArray.length;
                displayCurrentKontextImage();
            }
        };
    }
    if (nextBtn) {
        nextBtn.onclick = () => {
            if (kontextImagesArray.length > 0) {
                currentKontextImageIndex = (currentKontextImageIndex + 1) % kontextImagesArray.length;
                displayCurrentKontextImage();
            }
        };
    }
}

/**
 * Displays the edited image from Kontext Pro.
 * @param {Object} resultData - The result data from the API.
 */
function displayKontextEditedImage(resultData) {
    console.log("Displaying Kontext edited image with robust logic:", resultData);
    const imageElement = document.getElementById('kontextGeneratedImage');
    const placeholder = document.getElementById('kontextImagePlaceholder');
    const downloadBtnContainer = document.getElementById('kontextDownloadBtn');

    if (!imageElement || !placeholder) {
        console.error("Required UI elements for Kontext image display are missing.");
        return;
    }

    let imageUrl;

    // Structure 1: Deeper nesting (response.data.data.images)
    if (resultData?.data?.data?.images?.[0]?.url) {
        imageUrl = resultData.data.data.images[0].url;
        console.log("Image URL found via Structure 1 (deep nesting).");
    }
    // Structure 2: Shallower nesting (response.data.images)
    else if (resultData?.data?.images?.[0]?.url) {
        imageUrl = resultData.data.images[0].url;
        console.log("Image URL found via Structure 2 (shallow nesting).");
    }

    if (imageUrl) {
        imageElement.src = imageUrl;
        placeholder.classList.add('hidden');
        imageElement.classList.remove('hidden');

        // Setup lightbox and download
        imageElement.onclick = () => window.lightbox?.openLightbox(imageUrl, "Kontext Edited Image");
        if (downloadBtnContainer) {
            downloadBtnContainer.classList.remove('hidden');
            downloadBtnContainer.onclick = () => window.imageHandlers?.handleDownloadSpecificImage(imageUrl, `kontext_edited_${Date.now()}.jpg`);
        }
    } else {
        addLogMessage("Could not find a valid image URL in the Kontext API response after checking all known structures.", "error");
        console.error("Failed to extract image URL from response:", resultData);
    }
}

/**
 * Shows the progress bar for Kontext Pro operations.
 * @param {string} statusText - Text to display for the progress status.
 */
function showKontextProgress(statusText = "Processing...") {
    const progressArea = document.getElementById('kontextProgressArea');
    const progressBar = document.getElementById('kontextProgressBarIndicator');
    const progressText = document.getElementById('kontextProgressStatusText');

    if (progressArea && progressBar && progressText) {
        progressArea.classList.remove('hidden');
        progressBar.style.width = '100%'; // For indeterminate, just show a full bar or animate it
        progressBar.classList.add('indeterminate');
        progressText.textContent = statusText;
    }
}

/**
 * Hides the progress bar for Kontext Pro operations.
 */
function hideKontextProgress() {
    const progressArea = document.getElementById('kontextProgressArea');
    if (progressArea) {
        progressArea.classList.add('hidden');
    }
}

/**
 * Adds a log message to a specific log container.
 * @param {string} message - The message to log.
 * @param {string} type - The type of log (e.g., 'info', 'error').
 * @param {string} containerId - The ID of the log content container.
 */
function addLogMessageToContainer(message, type = 'info', containerId = 'logs-content') {
    const logEntry = document.createElement('div');
    logEntry.className = type + '-log';
    logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
    const logsContent = document.getElementById(containerId);
    if (logsContent) {
        logsContent.appendChild(logEntry);
        logsContent.scrollTop = logsContent.scrollHeight;
    } else {
        console.warn(`Log container with ID '${containerId}' not found.`);
    }
}

// Expose functions to the global window object
window.ui = {
    updateStatusIndicator,
    addLogMessage,
    displayGeneratedImages,
    showIndeterminateProgress,
    hideIndeterminateProgress,
    updateImageSizeDisplay,
    toggleSectionVisibility,
    setupSliderSync,
    initializeInterfaceToggle,
    setupPromptPills,
    initializeQuickPromptTabs, // Expose the new function
    // Kontext Pro specific UI functions
    displayKontextImagePreview,
    autoSelectKontextAspectRatio,
    updateKontextStatusIndicator,
    initKontextGallery,
    displayKontextEditedImage,
    setupKontextGalleryNav,
    showKontextProgress,
    hideKontextProgress,
    addLogMessageToContainer,
    // Anatomical Checkbox
    setupAnatomicalCheckboxListener,
    setupCollapsiblePrompts,
    loadQuickPrompts // Expose loadQuickPrompts
};

/**
 * Toggles the visibility of the prompt strictness dropdown based on the anatomical checkbox.
 */
function ********************************() {
    const isAnatomicalCheckbox = document.getElementById('kontextIsAnatomical');
    const strictnessContainer = document.getElementById('kontextPromptStrictnessContainer');

    if (isAnatomicalCheckbox && strictnessContainer) {
        if (isAnatomicalCheckbox.checked) {
            strictnessContainer.classList.remove('hidden');
        } else {
            strictnessContainer.classList.add('hidden');
        }
    }
}

/**
 * Sets up the event listener for the anatomical checkbox.
 */
function setupAnatomicalCheckboxListener() {
    const isAnatomicalCheckbox = document.getElementById('kontextIsAnatomical');
    if (isAnatomicalCheckbox) {
        isAnatomicalCheckbox.addEventListener('change', ********************************);
    }
}

/**
 * Sets up collapsible prompt sections.
 */
function setupCollapsiblePrompts() {
    const collapsibles = document.querySelectorAll('.collapsible-trigger');
    collapsibles.forEach(button => {
        button.addEventListener('click', () => {
            const content = button.nextElementSibling;
            const icon = button.querySelector('.trigger-icon');

            const isCurrentlyOpen = content.classList.contains('open');

            if (isCurrentlyOpen) {
                // Close the section
                content.classList.remove('open');
                content.classList.add('hidden'); // Add hidden back (e.g., for display:none via Tailwind)
                icon.classList.remove('fa-chevron-up');
                icon.classList.add('fa-chevron-down');
            } else {
                // Open the section
                content.classList.remove('hidden'); // Remove hidden (e.g., for display:block via Tailwind)
                content.classList.add('open');
                icon.classList.remove('fa-chevron-down');
                icon.classList.add('fa-chevron-up');
            }
        });
    });
}

// --- Before/After Comparison Slider ---
// Function definition moved before window.ui export
// let comparisonOriginalUrl = ''; // This will be part of the function scope or module scope if needed elsewhere
// let comparisonEditedUrl = '';
// let comparisonIsSwapped = false;

// --- End Before/After Comparison Slider ---

// These global-like variables are now part of initComparisonSlider's local scope or managed within it.
// let comparisonOriginalUrl = '';
// let comparisonEditedUrl = '';
// let comparisonIsSwapped = false;

// initComparisonSlider function is now defined above window.ui export.
// --- End Before/After Comparison Slider ---
