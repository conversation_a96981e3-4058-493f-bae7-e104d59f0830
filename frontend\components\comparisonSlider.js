/**
 * Comparison Slider module
 * Handles the Before/After image comparison functionality.
 */
(function() {
    /**
     * Initializes the comparison slider.
     * @param {Object} sliderData - Data for the slider, including image URLs and metadata.
     * @param {string} sliderData.originalImageUrl - URL of the original image.
     * @param {string} sliderData.editedImageUrl - URL of the edited image.
     * @param {string} sliderData.originalDetails - Details for the original image (e.g., filename, dimensions).
     * @param {string} sliderData.editedDetails - Details for the edited image (e.g., dimensions).
     * @param {string} sliderData.processingTime - Time taken for processing.
     * @param {string} sliderData.seed - Seed used for generation.
     */
    function initComparisonSlider(sliderData) {
        const {
            originalImageUrl,
            editedImageUrl,
            originalDetails,
            editedDetails,
            processingTime,
            seed
        } = sliderData;

        const comparisonContainer = document.getElementById('kontextComparisonContainer');
        const beforeImage = document.getElementById('comparison-before-image');
        const afterImage = document.getElementById('comparison-after-image');
        const sliderHandle = document.getElementById('comparison-slider-handle');
        const sliderInput = document.getElementById('comparison-slider-input');

        // New metadata elements
        const metaOriginalEl = document.getElementById('meta-original-details');
        const metaEditedEl = document.getElementById('meta-edited-details');
        const metaTimeEl = document.getElementById('meta-time');
        const metaSeedEl = document.getElementById('meta-seed');

        if (!comparisonContainer || !beforeImage || !afterImage || !sliderHandle || !sliderInput || !metaOriginalEl || !metaEditedEl || !metaTimeEl || !metaSeedEl) {
            console.error('One or more comparison elements are missing from the DOM.');
            if (window.ui && window.ui.addLogMessageToContainer) {
                window.ui.addLogMessageToContainer('Failed to initialize comparison view: UI elements missing.', 'error', 'kontext-logs-content');
            }
            return;
        }

        let comparisonOriginalUrl = originalImageUrl;
        let comparisonEditedUrl = editedImageUrl;
        let comparisonIsSwapped = false;

        // Set images
        beforeImage.src = comparisonIsSwapped ? comparisonEditedUrl : comparisonOriginalUrl;
        afterImage.src = comparisonIsSwapped ? comparisonOriginalUrl : comparisonEditedUrl;

        // Set metadata and manage visibility
        const updateMetaField = (wrapperId, element, value) => {
            const wrapper = document.getElementById(wrapperId);
            // Convert value to string for consistent handling, then trim
            const displayValue = String(value || '').trim();

            if (displayValue && displayValue !== 'N/A' && displayValue !== 'undefineds') {
                element.textContent = displayValue;
                if (element.title !== undefined) {
                    element.title = displayValue;
                }
                wrapper.classList.remove('hidden');
                wrapper.classList.add('flex'); // Ensure it's flex for alignment
            } else {
                wrapper.classList.add('hidden');
                wrapper.classList.remove('flex');
            }
        };

        updateMetaField('meta-original-wrapper', metaOriginalEl, originalDetails);
        updateMetaField('meta-edited-wrapper', metaEditedEl, editedDetails);
        updateMetaField('meta-time-wrapper', metaTimeEl, processingTime);
        updateMetaField('meta-seed-wrapper', metaSeedEl, seed);
        
        // Ensure the main metadata container is visible if at least one item is shown
        const metadataContainer = document.getElementById('comparison-metadata');
        const visibleItems = metadataContainer.querySelectorAll('.meta-item:not(.hidden)');
        if (visibleItems.length > 0) {
            metadataContainer.classList.remove('hidden');
        } else {
            metadataContainer.classList.add('hidden');
        }


        comparisonContainer.style.display = 'block'; // Keep this for initial show
        // Delay adding 'active' class to allow CSS transition for opacity/transform
        setTimeout(() => {
            comparisonContainer.classList.add('active');
        }, 50); // Small delay

        const imageArea = document.getElementById('comparison-image-area');
        let isDragging = false;
        let dragStartX = 0;
        let handleOffsetX = 0;

        function updateImageClip(percentage) {
            afterImage.style.clipPath = `inset(0 0 0 ${percentage}%)`;
            sliderHandle.style.left = `calc(${percentage}% - ${sliderHandle.offsetWidth / 2}px)`;
            if (sliderInput.value !== percentage.toString()) {
                sliderInput.value = percentage;
            }
        }

        updateImageClip(50);
        sliderInput.value = 50;

        sliderInput.addEventListener('input', (e) => {
            updateImageClip(parseFloat(e.target.value));
        });

        const startDrag = (e) => {
            isDragging = true;
            sliderHandle.classList.add('dragging');
            document.body.style.cursor = 'ew-resize';
            
            const clientX = e.clientX || (e.touches && e.touches[0].clientX);
            dragStartX = clientX;
            // Calculate the offset of the click within the handle itself
            const handleRect = sliderHandle.getBoundingClientRect();
            const imageAreaRect = imageArea.getBoundingClientRect(); // Added for context
            // handleOffsetX is the click position relative to the handle's center,
            // adjusted by the handle's current position relative to the image area.
            handleOffsetX = clientX - (handleRect.left + handleRect.width / 2);
            console.log(`[startDrag] clientX: ${clientX}, handleRect.left: ${handleRect.left}, handleRect.width: ${handleRect.width}, handleOffsetX: ${handleOffsetX}`);
            e.stopPropagation(); // Prevent click from bubbling up to imageArea

        };

        const drag = (e) => {
            if (!isDragging || !imageArea) return;
            e.preventDefault();
            const rect = imageArea.getBoundingClientRect();
            let clientX = e.clientX || (e.touches && e.touches[0].clientX);
            if (clientX === undefined) return;

            // Calculate the new x position by considering the initial offset within the handle
            let x = clientX - rect.left - handleOffsetX;
            
            // Convert x to percentage relative to the imageArea width
            // but ensure the handle's center aligns with this percentage
            let percentage = (x / rect.width) * 100;
            
            percentage = Math.max(0, Math.min(100, percentage));
            updateImageClip(percentage);
            console.log(`[drag] clientX: ${clientX}, rect.left: ${rect.left}, handleOffsetX: ${handleOffsetX}, x: ${x}, percentage: ${percentage}`);
        };

        const endDrag = () => {
            if (isDragging) {
                isDragging = false;
                sliderHandle.classList.remove('dragging');
                document.body.style.cursor = 'default';
            }
        };

        sliderHandle.addEventListener('mousedown', startDrag);
        sliderHandle.addEventListener('touchstart', startDrag, { passive: false });

        // Add click listener to the image area to open lightbox for the edited image
        imageArea.addEventListener('click', (e) => {
            // Only open lightbox if the click is not on the slider handle itself
            if (e.target !== sliderHandle && !sliderHandle.contains(e.target)) {
                // Check if the 'Show Result Only' view is active or if the slider is at 100% (showing only after image)
                const isResultOnlyView = toggleViewBtn.dataset.viewMode === 'result-only';
                const isSliderAtFullAfter = parseFloat(sliderInput.value) === 100;

                if (afterImage.src && (isResultOnlyView || isSliderAtFullAfter)) {
                    window.lightbox.openLightbox(afterImage.src);
                } else if (beforeImage.src && parseFloat(sliderInput.value) === 0) {
                    // If slider is at 0%, show original image in lightbox
                    window.lightbox.openLightbox(beforeImage.src);
                } else if (afterImage.src && !isResultOnlyView && !isSliderAtFullAfter) {
                    // If in comparison mode and not at 100%, default to showing the after image
                    window.lightbox.openLightbox(afterImage.src);
                }
            }
        });

        document.addEventListener('mousemove', drag);
        document.addEventListener('touchmove', drag, { passive: false });

        document.addEventListener('mouseup', endDrag);
        document.addEventListener('touchend', endDrag);

        const resetBtn = document.getElementById('comparison-reset-btn');
        const swapBtn = document.getElementById('comparison-swap-btn');
        const toggleViewBtn = document.getElementById('comparison-toggle-view-btn');

        if (resetBtn) {
            resetBtn.onclick = () => {
                updateImageClip(50);
                sliderInput.value = 50;
            };
        }

        if (swapBtn) {
            swapBtn.onclick = () => {
                comparisonIsSwapped = !comparisonIsSwapped;
                beforeImage.src = comparisonIsSwapped ? comparisonEditedUrl : comparisonOriginalUrl;
                afterImage.src = comparisonIsSwapped ? comparisonOriginalUrl : comparisonEditedUrl;
                
                // Update metadata considering swapped state
                const currentOriginalDetails = comparisonIsSwapped ? editedDetails : originalDetails;
                const currentEditedDetails = comparisonIsSwapped ? originalDetails : editedDetails;
                
                updateMetaField('meta-original-wrapper', metaOriginalEl, currentOriginalDetails);
                updateMetaField('meta-edited-wrapper', metaEditedEl, currentEditedDetails);
                // Time and Seed remain the same regardless of swap
                updateMetaField('meta-time-wrapper', metaTimeEl, processingTime);
                updateMetaField('meta-seed-wrapper', metaSeedEl, seed);

                updateImageClip(parseFloat(sliderInput.value));
                if (window.ui && window.ui.addLogMessageToContainer) {
                    const logOriginal = metaOriginalEl.closest('.meta-item').classList.contains('hidden') ? 'N/A' : metaOriginalEl.textContent;
                    const logEdited = metaEditedEl.closest('.meta-item').classList.contains('hidden') ? 'N/A' : metaEditedEl.textContent;
                    window.ui.addLogMessageToContainer(`Images swapped. Before: ${logOriginal}, After: ${logEdited}`, 'info', 'kontext-logs-content');
                }
            };
        }

        if (toggleViewBtn) {
            toggleViewBtn.onclick = () => {
                const currentMode = toggleViewBtn.dataset.viewMode || 'compare';
                if (currentMode === 'compare') {
                    updateImageClip(0);
                    sliderInput.value = 0;
                    sliderInput.disabled = true;
                    sliderHandle.style.display = 'none';
                    toggleViewBtn.innerHTML = '<i class="fas fa-adjust"></i> Show Comparison';
                    toggleViewBtn.dataset.viewMode = 'result';
                    if (window.ui && window.ui.addLogMessageToContainer) {
                        window.ui.addLogMessageToContainer('Switched to Result Only view.', 'info', 'kontext-logs-content');
                    }
                } else {
                    updateImageClip(50);
                    sliderInput.value = 50;
                    sliderInput.disabled = false;
                    sliderHandle.style.display = 'block';
                    toggleViewBtn.innerHTML = '<i class="fas fa-eye"></i> Show Result Only';
                    toggleViewBtn.dataset.viewMode = 'compare';
                    if (window.ui && window.ui.addLogMessageToContainer) {
                        window.ui.addLogMessageToContainer('Switched to Comparison view.', 'info', 'kontext-logs-content');
                    }
                }
            };
            toggleViewBtn.innerHTML = '<i class="fas fa-eye"></i> Show Result Only';
            toggleViewBtn.dataset.viewMode = 'compare';
            sliderInput.disabled = false;
            sliderHandle.style.display = 'block';
        }

        const saveBtn = document.getElementById('comparison-save-btn');
        const fullscreenBtn = document.getElementById('comparison-fullscreen-btn');
        if (saveBtn) saveBtn.disabled = true;
        if (fullscreenBtn) fullscreenBtn.disabled = true;

        if (window.ui && window.ui.addLogMessageToContainer) {
            window.ui.addLogMessageToContainer('Before/After comparison view initialized.', 'info', 'kontext-logs-content');
        }
    }

    window.comparisonSlider = {
        init: initComparisonSlider
    };
})();