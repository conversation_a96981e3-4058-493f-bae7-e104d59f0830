/**
 * Lightbox component
 * Handles the image lightbox functionality
 */

/**
 * Opens the lightbox with the specified image
 * @param {string} imageUrl - URL of the image to display
 */
function openLightbox(imageUrl) {
    const lightboxModal = document.getElementById('image-lightbox-modal');
    const lightboxImage = document.getElementById('lightbox-image');
    if (lightboxModal && lightboxImage && imageUrl) {
        lightboxImage.src = imageUrl;

        // Get content type from the current image in the gallery
        if (window.imageHandlers && window.imageHandlers.getCurrentImageContentType) {
            const contentType = window.imageHandlers.getCurrentImageContentType();
            if (contentType) {
                lightboxImage.dataset.contentType = contentType;
            }
        } else {
            // Fallback to the old method
            const generatedImg = document.getElementById('generated-image');
            if (generatedImg && generatedImg.dataset.contentType) {
                lightboxImage.dataset.contentType = generatedImg.dataset.contentType;
            }
        }

        // Перевіряємо, чи зображення квадратне
        const checkImageShape = () => {
            if (lightboxImage.complete) {
                const isSquare = Math.abs(lightboxImage.naturalWidth - lightboxImage.naturalHeight) < 10;
                if (isSquare) {
                    lightboxImage.classList.add('square-image');
                    lightboxModal.classList.add('square-mode');
                } else {
                    lightboxImage.classList.remove('square-image');
                    lightboxModal.classList.remove('square-mode');
                }

                // Переконуємося, що зображення повністю видно
                setTimeout(() => {
                    lightboxImage.style.maxHeight = `${Math.min(window.innerHeight * 0.85, lightboxImage.naturalHeight)}px`;
                }, 50);
            } else {
                lightboxImage.onload = checkImageShape;
            }
        };

        checkImageShape();
        lightboxModal.classList.remove('hidden');
        // Додаємо клас до body, щоб запобігти прокрутці
        document.body.classList.add('overflow-hidden');
    }
}

/**
 * Closes the lightbox
 */
function closeLightbox() {
    const lightboxModal = document.getElementById('image-lightbox-modal');
    const lightboxImage = document.getElementById('lightbox-image');
    if (lightboxModal) {
        lightboxModal.classList.add('hidden');
        // Видаляємо класи для квадратних зображень
        lightboxModal.classList.remove('square-mode');
        if (lightboxImage) {
            lightboxImage.classList.remove('square-image');
            // Очищаємо стилі
            lightboxImage.style.maxHeight = '';
            // Затримка очищення src для плавного закриття
            setTimeout(() => {
                lightboxImage.src = '';
            }, 300);
        }
        // Видаляємо клас з body
        document.body.classList.remove('overflow-hidden');
    }
}

/**
 * Downloads the image from the lightbox
 */
function downloadLightboxImage() {
    const lightboxImage = document.getElementById('lightbox-image');
    if (lightboxImage && lightboxImage.src) {
        // Use the image handlers download function if available
        if (window.imageHandlers && window.imageHandlers.handleDownloadImage) {
            window.imageHandlers.handleDownloadImage();
            return;
        }

        // Fallback to direct download
        const imageUrl = lightboxImage.src;
        let fileName = 'generated-image.jpg';
        try {
            const url = new URL(imageUrl);
            const pathParts = url.pathname.split('/');
            const lastPart = pathParts[pathParts.length - 1];
            if (lastPart) {
                fileName = lastPart.replace(/[^a-zA-Z0-9.-]/g, '_');
                if (!/\.(jpe?g|png|gif|webp)$/i.test(fileName)) {
                    const contentType = lightboxImage.dataset.contentType || 'image/jpeg';
                    const ext = contentType.split('/')[1] || 'jpg';
                    fileName += '.' + ext;
                }
            }
        } catch (e) {
            console.warn("Could not parse URL for filename, using default.", e);
        }
        const link = document.createElement('a');
        link.href = imageUrl;
        link.download = fileName;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.ui.addLogMessage(`Downloading image: ${fileName}`, "info");
    } else {
        window.ui.addLogMessage("No image in lightbox to download.", "error");
    }
}

/**
 * Initializes the lightbox component
 */
function initLightbox() {
    const lightboxModal = document.getElementById('image-lightbox-modal');
    const lightboxImage = document.getElementById('lightbox-image');
    const lightboxCloseBtn = document.getElementById('lightbox-close-btn');
    const lightboxDownloadBtn = document.getElementById('lightbox-download-btn');
    const generatedImageDisplay = document.getElementById('generated-image');

    // Функція для адаптації розміру зображення при зміні розміру вікна
    const adjustImageSize = () => {
        if (lightboxImage && !lightboxModal.classList.contains('hidden')) {
            lightboxImage.style.maxHeight = `${Math.min(window.innerHeight * 0.85, lightboxImage.naturalHeight)}px`;
        }
    };

    if (generatedImageDisplay) {
        generatedImageDisplay.addEventListener('click', function() {
            const placeholder = document.getElementById('image-placeholder');
            const imageGallery = document.getElementById('image-gallery');
            if (this.src && imageGallery && !imageGallery.classList.contains('hidden') && placeholder.classList.contains('hidden')) {
                openLightbox(this.src);
            }
        });
    }

    if (lightboxCloseBtn) {
        lightboxCloseBtn.addEventListener('click', closeLightbox);
    }

    if (lightboxDownloadBtn) {
        lightboxDownloadBtn.addEventListener('click', downloadLightboxImage);
    }

    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape' && lightboxModal && !lightboxModal.classList.contains('hidden')) {
            closeLightbox();
        }
    });

    if (lightboxModal) {
        lightboxModal.addEventListener('click', function(event) {
            // Закриваємо лайтбокс тільки якщо клік був на фоні (не на зображенні чи контейнері)
            if (event.target === lightboxModal ||
                (event.target.closest('.relative') === null && event.target !== lightboxImage)) {
                closeLightbox();
            }
        });
    }

    // Додаємо обробник події для зміни розміру вікна
    window.addEventListener('resize', adjustImageSize);
}

// Export lightbox functions
window.lightbox = {
    openLightbox,
    closeLightbox,
    downloadLightboxImage,
    initLightbox
};
