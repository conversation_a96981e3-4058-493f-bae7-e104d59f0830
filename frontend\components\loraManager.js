/**
 * LoRA Manager component
 * Handles LoRA items in the form
 */

// Add a counter at the beginning of the script or within an init function for loraManager
let loraIdCounter = 1; // Start from 1 because 0 is used for the initial static item

/**
 * Adds a new LoRA item to the form
 */
function handleAddLoraItem() {
    console.log("Adding new LoRA item");
    const lorasContainer = document.getElementById('loras-container');
    const newLoraItem = document.createElement('div');
    // Keep existing className: 'lora-item mb-3 p-3 bg-gray-800 rounded-lg relative'
    newLoraItem.className = 'lora-item mb-3 p-3 bg-gray-800 rounded-lg'; // REMOVE 'relative'

    const currentLoraId = loraIdCounter++; // Increment counter for unique IDs

    newLoraItem.innerHTML = `
        <div class="flex items-start justify-between mb-2"> <!-- Path section wrapper with flex -->
            <div class="flex-grow mr-2"> <!-- Wrapper for label and input -->
                <label class="block text-xs font-medium mb-1">Path</label>
                <input type="text" class="input-field w-full p-1.5 rounded-lg text-sm" placeholder="Enter LoRA URL">
            </div>
            <button class="remove-lora-btn text-red-400 hover:text-red-300 p-1 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 w-6 h-6 flex-shrink-0 flex items-center justify-center" title="Remove LoRA" aria-label="Remove LoRA item">
                <i class="fas fa-trash text-sm"></i>
            </button>
        </div>
        <div> <!-- Scale section (ensure IDs for label, slider, number are correctly handled if they were part of previous changes) -->
            <label id="lora-scale-label-${currentLoraId}" class="block text-xs font-medium mb-1">Scale</label>
            <div class="flex items-center gap-2">
                <input type="range" id="lora-scale-slider-${currentLoraId}" min="0" max="1" step="0.01" value="0.7" class="slider flex-1" aria-labelledby="lora-scale-label-${currentLoraId} lora-scale-number-${currentLoraId}">
                <input type="number" id="lora-scale-number-${currentLoraId}" min="0" max="1" step="0.01" value="0.7" class="input-field w-16 p-1.5 rounded-lg text-sm text-center">
            </div>
        </div>
    `;
    
    // ... rest of the function (event listeners etc.) ...
    // Ensure the existing event listeners for slider and number input still work correctly with the new IDs if they were generic.
    // The current implementation uses querySelector within newLoraItem, so it should be fine.

    newLoraItem.querySelector('.remove-lora-btn').addEventListener('click', function() { handleRemoveLoraItem(newLoraItem); });
    lorasContainer.appendChild(newLoraItem);
    const slider = newLoraItem.querySelector('input[type="range"]');
    const numberInput = newLoraItem.querySelector('input[type="number"]');
    slider.addEventListener('input', function() { numberInput.value = this.value; });
    numberInput.addEventListener('input', function() { 
        let value = parseFloat(this.value);
        const min = parseFloat(slider.min);
        const max = parseFloat(slider.max);
        if (isNaN(value)) value = parseFloat(slider.defaultValue) || min;
        if (value < min) value = min;
        if (value > max) value = max;
        this.value = value;
        slider.value = value;
    });
    window.ui.addLogMessage("Added new LoRA configuration block", "info");
}

/**
 * Removes a LoRA item from the form
 * @param {HTMLElement} loraElement - The LoRA item element to remove
 */
function handleRemoveLoraItem(loraElement) {
    console.log("Removing LoRA item");
    const lorasContainer = document.getElementById('loras-container');
    if (lorasContainer.children.length > 1) {
        lorasContainer.removeChild(loraElement);
        window.ui.addLogMessage("Removed LoRA configuration block", "info");
    } else {
        window.ui.addLogMessage("Cannot remove the last LoRA block", "error");
    }
}

// Export LoRA manager functions
window.loraManager = {
    handleAddLoraItem,
    handleRemoveLoraItem
};
