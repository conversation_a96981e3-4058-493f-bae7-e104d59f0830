/**
 * Preset Manager component
 * Handles saving, loading, and managing configuration presets
 */

/**
 * Fix all presets in localStorage to ensure safety<PERSON>he<PERSON> is a boolean
 */
function fixAllPresets() {
    try {
        console.log("Checking and fixing all presets in localStorage...");
        const presetsJson = localStorage.getItem('imageGenerationPresets');
        if (!presetsJson) {
            console.log("No presets found in localStorage");
            return;
        }

        const presets = JSON.parse(presetsJson);
        let fixedCount = 0;

        presets.forEach(preset => {
            if (preset.safetyChecker === undefined) {
                console.warn(`Preset "${preset.name}" missing safetyChecker, setting default value (false)`);
                preset.safetyChecker = false;
                fixedCount++;
            } else if (typeof preset.safetyChecker !== 'boolean') {
                console.warn(`Preset "${preset.name}" has non-boolean safetyChecker: ${preset.safetyChecker} (${typeof preset.safetyChecker})`);
                // Convert to boolean and then set to false
                preset.safetyChecker = false;
                console.log(`Fixed to: ${preset.safetyChecker} (${typeof preset.safetyChecker})`);
                fixedCount++;
            } else if (preset.safetyChecker === true) {
                // Always set to false regardless of current value
                console.warn(`Preset "${preset.name}" had safetyChecker=true, overriding to false`);
                preset.safetyChecker = false;
                fixedCount++;
            }
        });

        if (fixedCount > 0) {
            console.log(`Fixed ${fixedCount} presets, saving to localStorage`);
            localStorage.setItem('imageGenerationPresets', JSON.stringify(presets));
        } else {
            console.log("All presets are valid, no fixes needed");
        }
    } catch (error) {
        console.error("Error fixing presets:", error);
    }
}

/**
 * Initialize the preset manager
 */
function initPresetManager() {
    console.log("Initializing preset manager");

    // Fix all presets in localStorage
    fixAllPresets();

    // Load presets from storage
    loadPresetsFromStorage();

    // Set up event listeners for save button
    const savePresetBtn = document.getElementById('save-preset-btn');
    if (savePresetBtn) {
        console.log("Save preset button found, adding event listener");
        savePresetBtn.addEventListener('click', handleSavePreset);

        // Make sure the button is visible
        savePresetBtn.style.display = 'flex';
    } else {
        console.error("Save preset button not found!");
    }

    // Add keyboard shortcut for saving preset (Ctrl+S)
    document.addEventListener('keydown', function(event) {
        if ((event.ctrlKey || event.metaKey) && event.key === 's' && document.activeElement.id === 'preset-name') {
            event.preventDefault(); // Prevent browser's save dialog
            handleSavePreset();
        }
    });
}

/**
 * Load presets from localStorage
 */
function loadPresetsFromStorage() {
    console.log("Loading presets from storage");
    const presets = getPresets();
    const presetsContainer = document.getElementById('presets-container');

    if (presetsContainer) {
        // Clear existing presets
        presetsContainer.innerHTML = '';

        // Add presets to the container
        if (presets.length === 0) {
            // Show "No saved presets" message with improved styling
            const noPresetsMessage = document.createElement('div');
            noPresetsMessage.className = 'flex flex-col items-center justify-center p-4 text-center';
            noPresetsMessage.innerHTML = `
                <i class="fas fa-save text-gray-600 mb-2 opacity-50 text-xl"></i>
                <p class="text-gray-400 text-xs">No saved presets</p>
                <p class="text-gray-500 text-xs mt-1">Create a preset to save your settings</p>
            `;
            presetsContainer.appendChild(noPresetsMessage);

            console.log("No presets found in storage");
        } else {
            console.log(`Found ${presets.length} presets in storage`);
            presets.forEach((preset, index) => {
                addPresetToUI(preset, index);
            });
        }
    } else {
        console.error("Presets container not found!");
    }
}

/**
 * Add a preset to the UI
 * @param {Object} preset - The preset to add
 * @param {number} index - The index of the preset
 */
function addPresetToUI(preset, index) {
    const presetsContainer = document.getElementById('presets-container');
    const presetElement = document.createElement('div');
    presetElement.className = 'preset-item flex items-center p-2 rounded-lg hover:bg-gray-700/70 border border-transparent hover:border-gray-600 transition-all duration-150 group cursor-pointer';
    presetElement.dataset.index = index;

    // Create and configure the preset name span
    const nameSpan = document.createElement('span');
    nameSpan.className = 'preset-name flex-grow min-w-0 truncate pr-2 text-sm text-gray-200 group-hover:text-white'; // Using the last known good classes for layout
    nameSpan.title = preset.name;
    nameSpan.textContent = preset.name; // Ensures preset.name is treated as plain text

    // Create the actions div and its buttons
    const actionsDiv = document.createElement('div');
    actionsDiv.className = 'preset-actions flex items-center gap-1 flex-none';
    actionsDiv.innerHTML = `
        <button class="load-preset-btn flex items-center justify-center text-blue-400 hover:text-blue-300 p-1.5 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-150 w-7 h-7" title="Load preset">
            <i class="fas fa-upload text-sm"></i>
        </button>
        <button class="delete-preset-btn flex items-center justify-center text-red-400 hover:text-red-300 p-1.5 rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-red-500 transition-colors duration-150 w-7 h-7" title="Delete preset">
            <i class="fas fa-trash text-sm"></i>
        </button>
    `;

    // Append name and actions to the preset element
    presetElement.appendChild(nameSpan);
    presetElement.appendChild(actionsDiv);

    // Add event listeners
    presetElement.addEventListener('mouseenter', function() {
        showPresetPreview(preset);
    });

    presetElement.addEventListener('mouseleave', function() {
        hidePresetPreview();
    });

    const loadBtn = actionsDiv.querySelector('.load-preset-btn'); // Target within actionsDiv
    loadBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        loadPreset(index);
    });

    const deleteBtn = actionsDiv.querySelector('.delete-preset-btn'); // Target within actionsDiv
    deleteBtn.addEventListener('click', function(e) {
        e.stopPropagation();
        deletePreset(index);
    });

    // Add to container
    presetsContainer.appendChild(presetElement);
}

/**
 * Show preview of preset on hover
 * @param {Object} preset - The preset to preview
 */
function showPresetPreview(preset) {
    const previewElement = document.getElementById('preset-preview');
    if (!previewElement) {
        const preview = document.createElement('div');
        preview.id = 'preset-preview';
        preview.className = 'preset-preview fixed bg-gray-900 border border-gray-700 p-4 shadow-lg z-50';

        // Create header with preset name
        let previewContent = `
            <div class="border-b border-gray-700 pb-2 mb-3">
                <h3 class="text-sm font-semibold text-white">${preset.name}</h3>
            </div>
        `;

        // Start content section
        previewContent += `<div class="text-xs space-y-2">`;

        // Add preset properties (excluding prompt)
        // Image Size
        if (preset.imageSize) {
            previewContent += `
                <div class="flex justify-between">
                    <span class="text-gray-400">Image Size:</span>
                    <span class="text-white font-medium">
                        ${typeof preset.imageSize === 'object'
                            ? `${preset.imageSize.width}×${preset.imageSize.height}`
                            : preset.imageSize}
                    </span>
                </div>`;
        }

        // Inference Steps
        if (preset.numInferenceSteps) {
            previewContent += `
                <div class="flex justify-between">
                    <span class="text-gray-400">Steps:</span>
                    <span class="text-white font-medium">${preset.numInferenceSteps}</span>
                </div>`;
        }

        // Guidance Scale
        if (preset.guidanceScale) {
            previewContent += `
                <div class="flex justify-between">
                    <span class="text-gray-400">Guidance Scale:</span>
                    <span class="text-white font-medium">${preset.guidanceScale}</span>
                </div>`;
        }

        // Seed
        if (preset.seed) {
            previewContent += `
                <div class="flex justify-between">
                    <span class="text-gray-400">Seed:</span>
                    <span class="text-white font-medium">${preset.seed}</span>
                </div>`;
        }

        // Sync Mode
        if (preset.syncMode !== undefined) {
            previewContent += `
                <div class="flex justify-between">
                    <span class="text-gray-400">Sync Mode:</span>
                    <span class="text-white font-medium">${preset.syncMode ? 'On' : 'Off'}</span>
                </div>`;
        }

        // Safety Checker
        if (preset.safetyChecker !== undefined) {
            previewContent += `
                <div class="flex justify-between">
                    <span class="text-gray-400">Safety Checker:</span>
                    <span class="text-white font-medium">${preset.safetyChecker ? 'On' : 'Off'}</span>
                </div>`;
            // Додаємо лог для відлагодження
            console.log("Safety Checker value in preset:", preset.safetyChecker);
        } else {
            console.log("Safety Checker is undefined in preset");
        }

        // Number of Images
        if (preset.numImages) {
            previewContent += `
                <div class="flex justify-between">
                    <span class="text-gray-400">Number of Images:</span>
                    <span class="text-white font-medium">${preset.numImages}</span>
                </div>`;
        }

        // Output Format
        if (preset.outputFormat) {
            previewContent += `
                <div class="flex justify-between">
                    <span class="text-gray-400">Output Format:</span>
                    <span class="text-white font-medium">${preset.outputFormat}</span>
                </div>`;
        }

        // LoRAs
        if (preset.loras && preset.loras.length > 0) {
            previewContent += `
                <div class="flex justify-between">
                    <span class="text-gray-400">LoRAs:</span>
                    <span class="text-white font-medium">${preset.loras.length} configured</span>
                </div>`;
        }

        // Close content section
        previewContent += `</div>`;

        // Add footer with hint
        previewContent += `
            <div class="mt-3 pt-2 border-t border-gray-700 text-center">
                <span class="text-xs text-gray-500">Click to load this preset</span>
            </div>
        `;

        preview.innerHTML = previewContent;
        document.body.appendChild(preview);

        // Position the preview next to the hovered element
        const hoveredElement = document.querySelector('.preset-item:hover');
        if (hoveredElement) {
            const rect = hoveredElement.getBoundingClientRect();
            preview.style.left = `${rect.right + 10}px`;
            preview.style.top = `${rect.top}px`;

            // Make sure preview doesn't go off screen
            const previewRect = preview.getBoundingClientRect();
            if (previewRect.right > window.innerWidth) {
                preview.style.left = `${rect.left - previewRect.width - 10}px`;
            }
            if (previewRect.bottom > window.innerHeight) {
                preview.style.top = `${window.innerHeight - previewRect.height - 10}px`;
            }
        }
    }
}

/**
 * Hide preset preview
 */
function hidePresetPreview() {
    const previewElement = document.getElementById('preset-preview');
    if (previewElement) {
        previewElement.remove();
    }
}

/**
 * Handle saving a new preset
 */
function handleSavePreset() {
    console.log("Save preset button clicked");

    // Get preset name
    const presetNameInput = document.getElementById('preset-name');
    if (!presetNameInput) {
        console.error("Preset name input not found!");
        window.ui.addLogMessage("Error: Preset name input not found", "error");
        return;
    }

    const presetName = presetNameInput.value.trim();
    if (!presetName) {
        window.ui.addLogMessage("Please enter a preset name", "error");
        return;
    }

    try {
        // Collect current form data (excluding prompt)
        const formData = window.formHandlers.collectInputData();
        if (!formData) {
            console.error("Failed to collect form data");
            window.ui.addLogMessage("Error collecting form data", "error");
            return;
        }

        delete formData.prompt; // Remove prompt from preset

        // Always set Safety Checker to false
        formData.safetyChecker = false;
        console.log("Safety Checker value forced to false for this preset");

        // Додаємо лог для відлагодження
        console.log("Saving preset with data:", formData);
        console.log("Safety Checker value being saved:", formData.safetyChecker);

        // Add name to the preset
        formData.name = presetName;

        // Save to localStorage
        savePreset(formData);

        // Clear preset name input
        presetNameInput.value = '';

        window.ui.addLogMessage(`Preset "${presetName}" saved successfully`, "success");
    } catch (error) {
        console.error("Error saving preset:", error);
        window.ui.addLogMessage(`Error saving preset: ${error.message}`, "error");
    }
}

/**
 * Save a preset to localStorage
 * @param {Object} preset - The preset to save
 */
function savePreset(preset) {
    try {
        // Always set safetyChecker to false regardless of current value
        preset.safetyChecker = false;

        // Log the forced value
        console.log("Safety Checker forced to false for this preset");

        // Log the final preset data being saved
        console.log("Final preset data being saved:", JSON.stringify(preset, null, 2));
        console.log("Safety Checker final value:", preset.safetyChecker);

        const presets = getPresets();
        presets.push(preset);
        localStorage.setItem('imageGenerationPresets', JSON.stringify(presets));
        loadPresetsFromStorage(); // Refresh the UI
    } catch (error) {
        console.error("Error saving preset to localStorage:", error);
        window.ui.addLogMessage(`Error saving preset: ${error.message}`, "error");
    }
}

/**
 * Load a preset by index
 * @param {number} index - The index of the preset to load
 */
function loadPreset(index) {
    try {
        console.log("Loading preset with index:", index);
        const presets = getPresets();

        if (index >= 0 && index < presets.length) {
            const preset = presets[index];
            console.log("Preset found:", preset);

            // Make sure formHandlers is available
            if (!window.formHandlers || !window.formHandlers.applyPreset) {
                console.error("formHandlers or applyPreset function not found!");
                window.ui.addLogMessage("Error: Could not apply preset - form handlers not available", "error");
                return;
            }

            // Make sure the DOM is fully loaded before applying preset
            // Use a longer timeout to ensure DOM is ready
            console.log("Waiting for DOM to be fully loaded before applying preset...");
            setTimeout(() => {
                try {
                    // Double-check that all required elements are available
                    const additionalSettingsDiv = document.getElementById('additional-settings');
                    const safetyCheckerElement = document.getElementById('safety-checker');

                    console.log("DOM check before applying preset:");
                    console.log("- Additional settings div found:", !!additionalSettingsDiv);
                    console.log("- Safety checker element found:", !!safetyCheckerElement);

                    // Apply the preset
                    window.formHandlers.applyPreset(preset);
                    window.ui.addLogMessage(`Preset "${preset.name}" loaded`, "info");

                    // Verify safety checker was set correctly
                    const finalSafetyCheckerValue = document.getElementById('safety-checker')?.checked;
                    console.log("Safety checker value after applying preset:", finalSafetyCheckerValue);
                    console.log("Expected value from preset:", preset.safetyChecker);

                    if (finalSafetyCheckerValue !== preset.safetyChecker) {
                        console.warn("Safety checker value doesn't match preset value after applying!");
                        // Try to set it one more time directly
                        if (document.getElementById('safety-checker')) {
                            document.getElementById('safety-checker').checked = preset.safetyChecker;
                            console.log("Manually corrected safety checker value to:", preset.safetyChecker);
                        }
                    }
                } catch (error) {
                    console.error("Error applying preset:", error);
                    window.ui.addLogMessage(`Error loading preset: ${error.message}`, "error");
                }
            }, 300); // Increased timeout to 300ms
        } else {
            console.error("Invalid preset index:", index);
            window.ui.addLogMessage("Error: Invalid preset index", "error");
        }
    } catch (error) {
        console.error("Error in loadPreset:", error);
        window.ui.addLogMessage(`Error loading preset: ${error.message}`, "error");
    }
}

/**
 * Delete a preset by index
 * @param {number} index - The index of the preset to delete
 */
function deletePreset(index) {
    const presets = getPresets();
    if (index >= 0 && index < presets.length) {
        const presetName = presets[index].name;
        presets.splice(index, 1);
        localStorage.setItem('imageGenerationPresets', JSON.stringify(presets));
        loadPresetsFromStorage(); // Refresh the UI
        window.ui.addLogMessage(`Preset "${presetName}" deleted`, "info");
    }
}

/**
 * Get all presets from localStorage
 * @returns {Array} Array of presets
 */
function getPresets() {
    try {
        const presetsJson = localStorage.getItem('imageGenerationPresets');
        if (!presetsJson) {
            return [];
        }

        const presets = JSON.parse(presetsJson);

        // Ensure all presets have safetyChecker property and it's a boolean
        presets.forEach(preset => {
            if (preset.safetyChecker === undefined) {
                console.warn(`Preset "${preset.name}" missing safetyChecker, setting default value (false)`);
                preset.safetyChecker = false;
            } else {
                // Force to boolean type
                preset.safetyChecker = Boolean(preset.safetyChecker);

                // Log the value for debugging
                console.log(`Preset "${preset.name}" safetyChecker value: ${preset.safetyChecker} (type: ${typeof preset.safetyChecker})`);

                // Override to false if it's true (to ensure all presets have safety checker disabled)
                if (preset.safetyChecker === true) {
                    console.warn(`Preset "${preset.name}" had safetyChecker=true, overriding to false`);
                    preset.safetyChecker = false;
                }
            }
        });

        return presets;
    } catch (error) {
        console.error("Error getting presets from localStorage:", error);
        return [];
    }
}

// Export preset manager functions
// @ts-ignore: Property 'presetManager' does not exist on type 'Window & typeof globalThis'
window.presetManager = {
    initPresetManager,
    loadPresetsFromStorage,
    handleSavePreset,
    loadPreset,
    deletePreset,
    fixAllPresets
};
