<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Generation Tool</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <link rel="stylesheet" href="assets/css/style.css">
    <link rel="stylesheet" href="assets/css/lightbox.css">
    <script src="components/comparisonSlider.js"></script>
</head>
<body class="dark-theme min-h-screen p-4">
    <div class="container mx-auto max-w-7xl flex flex-col lg:flex-row gap-4 justify-center">
        <!-- Presets Panel -->
        <div class="panel p-4 flex-none lg:w-[15%] lg:max-w-xs lg:max-h-[calc(100vh-2rem)] overflow-y-auto">
            <h2 class="text-xl font-bold mb-4">Presets</h2>

            <!-- Main content of presets panel -->
            <div class="space-y-5">
                <!-- Create New Preset Section -->
                <div class="bg-gray-800/50 rounded-lg border border-gray-700/50 overflow-hidden">
                    <div class="bg-gray-800 px-4 py-2 border-b border-gray-700/50">
                        <h3 class="text-sm font-medium text-white">Create New Preset</h3>
                    </div>
                    <div class="p-4">
                        <div class="mb-3">
                            <label for="preset-name" class="block text-xs font-medium mb-1.5 text-gray-300">Preset Name</label>
                            <input type="text" id="preset-name" class="input-field w-full p-2 rounded-lg text-sm" placeholder="Enter preset name">
                        </div>
                        <button id="save-preset-btn" class="btn-primary w-full py-2 px-4 rounded-lg flex items-center justify-center gap-2 text-sm" title="Save current settings as preset">
                            <i class="fas fa-save"></i> Save Preset
                        </button>
                        <p class="text-xs text-gray-400 mt-2 text-center">Current settings (excluding prompt) will be saved</p>
                    </div>
                </div>
<hr class="border-gray-700 my-6">

                <!-- Saved Presets Section -->
                <div class="bg-gray-800/50 rounded-lg border border-gray-700/50 overflow-hidden">
                    <div class="bg-gray-800 px-4 py-2 border-b border-gray-700/50">
                        <h3 class="text-sm font-medium text-white">Saved Presets</h3>
                    </div>
                    <div class="p-2">
                        <div id="presets-container" class="max-h-[calc(100vh-20rem)] overflow-y-auto">
                            <!-- Presets will be added here dynamically -->
                            <div class="text-gray-400 text-xs text-center p-4">No saved presets</div>
                        </div>
                    </div>
                </div>
            </div>
        </div> <!-- End of Presets Panel -->

        <!-- Right Column for Switchable Interfaces -->
        <div class="flex flex-col flex-1 gap-4">

            <!-- Container for the actual interface panels -->
            <div id="interfacesContainer" class="flex-1 contents relative"> <!-- Added relative for positioning -->
                <!-- New Tab-like Interface Toggle -->
                <div class="flex border-b border-gray-700 mb-4">
                    <button id="fluxLorasToggle" class="toggle-tab-button px-4 py-2 text-gray-400 hover:text-white focus:outline-none">
                        <i class="fa-brands fa-artstation mr-2"></i> Flux Loras
                    </button>
                    <button id="kontextProToggle" class="toggle-tab-button active px-4 py-2 -mb-px border-b-2 border-blue-500 text-blue-400 focus:outline-none">
                        <i class="fa-solid fa-wand-magic-sparkles mr-2"></i> Kontext Pro
                    </button>
                </div>
                <!-- Flux Loras Interface Panel (will wrap existing Input and Result) -->
                <div id="fluxLorasInterface" class="interface-panel hidden flex-col lg:flex-row flex-1 gap-4">
                    <!-- Original Input Panel starts here -->
                    <div class="panel p-4 flex-1 lg:w-[35%] lg:max-w-lg lg:max-h-[calc(100vh-2rem)] overflow-y-auto">
            <h2 class="text-xl font-bold mb-4">Input</h2>

            <div class="mb-4">
                <label for="form-selector" class="block text-xs font-medium mb-1">Form</label>
                <select id="form-selector" class="input-field w-full p-2 rounded-lg text-sm">
                    <option value="fal-ai/flux-lora" selected>Flux LoRA (Default)</option>
                    <option value="rundiffusion-fal/juggernaut-flux-lora">Juggernaut Flux</option>
                </select>
            </div>

            <div class="mb-4">
                <label for="prompt" class="block text-xs font-medium mb-1">Prompt</label>
                <textarea id="prompt" rows="3" class="input-field w-full p-2 rounded-lg text-sm" placeholder="Enter your prompt here..."></textarea>
                <div class="flex justify-end mt-1">
                    <button id="improve-prompt-btn" class="btn-secondary text-xs py-1 px-2 rounded-md flex items-center gap-1">
                        <i class="fas fa-magic text-xs"></i> Improve Prompt
                    </button>
                </div>
                <!-- Collapsible Pre-prompt Buttons Section -->
                <div class="collapsible-prompts mt-2">
                    <button class="collapsible-trigger text-xs font-medium text-gray-400 hover:text-white transition-colors w-full flex justify-between items-center p-1.5 rounded-md bg-gray-700/50 hover:bg-gray-700">
                        <span><i class="fas fa-lightbulb mr-2"></i>Quick Prompts</span>
                        <i class="fas fa-chevron-down trigger-icon transition-transform"></i>
                    </button>
                    <div class="collapsible-content hidden mt-2">
                        <div id="flux-prompt-pills" class="prompt-pills-container flex flex-wrap gap-2">
                            <button class="prompt-pill" data-prompt-text="hyperrealistic, 8k, photorealistic, "><i class="fas fa-camera-retro mr-1.5"></i>Realistic</button>
                            <button class="prompt-pill" data-prompt-text="anime style, key visual, vibrant, "><i class="fas fa-dragon mr-1.5"></i>Anime</button>
                            <button class="prompt-pill" data-prompt-text="cinematic, dramatic lighting, "><i class="fas fa-film mr-1.5"></i>Cinematic</button>
                            <button class="prompt-pill" data-prompt-text="fantasy, epic, detailed, "><i class="fas fa-hat-wizard mr-1.5"></i>Fantasy</button>
                            <button class="prompt-pill" data-prompt-text="sci-fi, futuristic, glowing neon, "><i class="fas fa-robot mr-1.5"></i>Sci-Fi</button>
                            <button class="prompt-pill" data-prompt-text="watercolor painting, artistic, "><i class="fas fa-palette mr-1.5"></i>Artistic</button>
                        </div>
                    </div>
                </div>
            </div>

            <div class="mb-4">
                <div class="flex justify-between items-center mb-1">
                    <label class="block text-xs font-medium">Loras</label>
                </div>
                <div id="loras-container">
                    <div class="lora-item mb-3 p-3 bg-gray-800 rounded-lg">
                        <div class="flex items-start justify-between mb-2"> <!-- This div is the new Path section wrapper with flex -->
                            <div class="flex-grow mr-2"> <!-- Wrapper for label and input -->
                                <label id="lora-path-label-0" class="block text-xs font-medium mb-1">Path</label>
                                <input type="text" class="input-field w-full p-1.5 rounded-lg text-sm" placeholder="Enter LoRA URL">
                            </div>
                            <button class="remove-lora-btn text-red-400 hover:text-red-300 p-1 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500 w-6 h-6 flex-shrink-0 flex items-center justify-center" title="Remove LoRA" aria-label="Remove LoRA item">
                                <i class="fas fa-trash text-sm"></i>
                            </button>
                        </div>
                        <div> <!-- Scale section -->
                            <label id="lora-scale-label-0" class="block text-xs font-medium mb-1">Scale</label>
                            <div class="flex items-center gap-2">
                                <input type="range" id="lora-scale-slider-0" min="0" max="1" step="0.01" value="0.7" class="slider flex-1" aria-labelledby="lora-scale-label-0 lora-scale-number-0">
                                <input type="number" id="lora-scale-number-0" min="0" max="1" step="0.01" value="0.7" class="input-field w-16 p-1.5 rounded-lg text-sm text-center">
                            </div>
                        </div>
                    </div>
                </div>
                <button id="add-lora-btn" class="btn-secondary w-full py-1.5 text-sm rounded-lg flex items-center justify-center gap-1" onclick="window.loraManager.handleAddLoraItem()">
                    <i class="fas fa-plus text-xs"></i> Add Item
                </button>
            </div>

            <div class="mb-4">
                <div class="flex justify-between items-center mb-1 cursor-pointer" onclick="window.ui.toggleSectionVisibility('additional-settings', this.querySelector('span'), this.querySelector('i'))">
                    <h3 class="text-sm font-medium">Additional Settings</h3>
                    <span class="text-xs text-gray-400">Less <i class="fas fa-chevron-up ml-1"></i></span>
                </div>
                <div id="additional-settings" class="pb-2">
                    <div class="mb-3">
                        <label class="block text-xs font-medium mb-1">Image Size</label>
                        <div class="flex items-center gap-2">
                            <select id="image-size" class="input-field flex-1 p-1.5 rounded-lg text-sm">
                                <option value="square_hd">Square HD</option>
                                <option value="square">Square</option>
                                <option value="portrait_3_4">Portrait 3:4</option>
                                <option value="portrait_16_9">Portrait 16:9</option>
                                <option value="landscape_4_3">Landscape 4:3</option>
                                <option value="landscape_16_9">Landscape 16:9</option>
                                <option value="custom">Custom Size</option>
                            </select>
                            <span id="image-size-display" class="text-xs text-gray-400">1024 x 1024</span>
                        </div>
                    </div>
                    <div id="custom-size-container" class="mb-3 hidden">
                        <label class="block text-xs font-medium mb-1">Custom Size (width x height)</label>
                        <div class="flex items-center gap-2">
                            <input type="number" id="custom-width" min="256" max="1024" step="8" value="1024" class="input-field w-1/2 p-1.5 rounded-lg text-sm text-center" placeholder="Width">
                            <span class="text-xs">x</span>
                            <input type="number" id="custom-height" min="256" max="1024" step="8" value="1024" class="input-field w-1/2 p-1.5 rounded-lg text-sm text-center" placeholder="Height">
                        </div>
                        <p class="text-xs text-gray-400 mt-1">Width and height must be multiples of 8, between 256 and 1024 pixels.</p>
                    </div>
                    <div class="mb-3">
                        <label id="inference-steps-label" class="block text-xs font-medium mb-1">Num Inference Steps</label>
                        <div class="flex items-center gap-2">
                            <input type="range" id="inference-steps-slider" min="1" max="100" value="28" class="slider flex-1" aria-labelledby="inference-steps-label inference-steps-number">
                            <input type="number" id="inference-steps-number" min="1" max="100" value="28" class="input-field w-16 p-1.5 rounded-lg text-sm text-center">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="block text-xs font-medium mb-1">Seed</label>
                        <div class="flex items-center gap-1">
                            <input type="text" id="seed" value="random" class="input-field flex-1 p-1.5 rounded-lg text-sm">
                            <button class="btn-secondary p-1.5 rounded-lg" onclick="window.formHandlers.handleRandomizeSeed()" title="Randomize seed" aria-label="Randomize seed">
                                <i class="fas fa-redo text-xs"></i>
                            </button>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label id="cfg-scale-label" class="block text-xs font-medium mb-1">Guidance scale (CFG)</label>
                        <div class="flex items-center gap-2">
                            <input type="range" id="cfg-scale-slider" min="1" max="20" step="0.1" value="3.5" class="slider flex-1" aria-labelledby="cfg-scale-label cfg-scale-number">
                            <input type="number" id="cfg-scale-number" min="1" max="20" step="0.1" value="3.5" class="input-field w-16 p-1.5 rounded-lg text-sm text-center">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="block text-xs font-medium mb-1">Sync Mode</label>
                        <label class="toggle-switch">
                            <input type="checkbox">
                            <span class="toggle-slider"></span>
                        </label>
                    </div>
                    <div class="mb-3">
                        <label id="num-images-label" class="block text-xs font-medium mb-1">Num Images</label>
                        <div class="flex items-center gap-2">
                            <input type="range" id="num-images-slider" min="1" max="4" value="1" class="slider flex-1" aria-labelledby="num-images-label num-images-number">
                            <input type="number" id="num-images-number" min="1" max="4" value="1" class="input-field w-16 p-1.5 rounded-lg text-sm text-center">
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="block text-xs font-medium mb-1" for="safety-checker">Enable Safety Checker</label>
                        <label class="toggle-switch">
                            <input type="checkbox" id="safety-checker">
                            <span class="toggle-slider"></span>
                        </label>
                        <p class="text-xs text-gray-400 mt-1">Safety checker is disabled by default for better image generation results.</p>
                    </div>
                </div>
            </div>
            <div class="mb-4">
                <label for="output-format" class="block text-xs font-medium mb-1">Output Format</label>
                <select id="output-format" class="input-field w-full p-1.5 rounded-lg text-sm">
                    <option value="jpeg">jpeg</option>
                    <option value="png">png</option>
                </select>
            </div>
            <div class="flex gap-2">
                <button id="reset-btn" class="btn-secondary flex-1 py-2 rounded-lg text-sm">Reset</button>
                <button id="run-btn" class="btn-primary flex-1 py-2 rounded-lg flex items-center justify-center gap-1 text-sm">
                    Run <span class="text-xs opacity-70">(ctrl + ↵)</span>
                </button>
            </div>
        </div>

        <!-- Right Panel - Result -->
        <div class="panel p-3 flex-1 lg:w-[50%] lg:max-w-3xl lg:max-h-[calc(100vh-2rem)] overflow-y-auto">
            <h2 class="text-xl font-bold mb-4">Result</h2>
            <div class="mb-3">
                <span id="status-indicator" class="status-indicator status-default text-xs">Ready</span>
            </div>
            <div class="mb-3">
                <select id="preview-mode" class="input-field p-1.5 rounded-lg text-sm">
                    <option selected>Preview</option>
                </select>
            </div>
            <div class="image-container mb-4 aspect-square w-full">
                <div id="image-placeholder" class="flex items-center justify-center h-full text-gray-400">
                    <div class="text-center">
                        <i class="fas fa-image fa-2x mb-1"></i>
                        <p class="text-xs">Generated image will appear here</p>
                    </div>
                </div>
                <div id="image-gallery" class="hidden w-full h-full relative">
                    <img id="generated-image" class="w-full h-full object-contain" src="" alt="Generated image">
                    <div id="gallery-navigation" class="hidden absolute inset-x-0 top-1/2 transform -translate-y-1/2 flex justify-between px-2 z-10">
                        <button id="prev-image-btn" class="gallery-nav-btn" title="Previous image" aria-label="Previous image">
                            <i class="fas fa-chevron-left"></i>
                        </button>
                        <button id="next-image-btn" class="gallery-nav-btn" title="Next image" aria-label="Next image">
                            <i class="fas fa-chevron-right"></i>
                        </button>
                    </div>
                    <div id="gallery-indicator" class="hidden absolute bottom-2 left-1/2 transform -translate-x-1/2 flex space-x-1 z-10">
                        <!-- Dots will be added dynamically -->
                    </div>
                </div>
                <button id="download-btn" class="download-btn hidden" onclick="window.imageHandlers.handleDownloadImage()" title="Download image" aria-label="Download image">
                    <i class="fas fa-download"></i>
                </button>
            </div>

            <!-- Progress Bar Area (initially hidden or at 0%) -->
            <div id="generation-progress-area" class="my-4 hidden">
                <div class="text-xs text-gray-400 mb-1 text-center" id="progress-status-text">Initializing...</div>
                <div class="w-full bg-gray-700 rounded-full h-1.5 dark:bg-gray-600">
                    <div id="progress-bar-indicator" class="bg-blue-600 h-1.5 rounded-full" style="width: 0%"></div>
                </div>
            </div>

            <div class="mb-3">
                <h3 class="text-sm font-medium mb-1">What would you like to do next?</h3>
                <div class="flex flex-wrap gap-2 justify-center">
                    <button class="btn-secondary px-3 py-1 rounded-lg text-xs flex items-center"><i class="fas fa-search-plus mr-1.5"></i>Upscale</button>
                    <button class="btn-secondary px-3 py-1 rounded-lg text-xs flex items-center"><i class="fas fa-video mr-1.5"></i>Make Video</button>
                    <button class="btn-secondary px-3 py-1 rounded-lg text-xs flex items-center"><i class="fas fa-share-alt mr-1.5"></i>Share</button>
                    <button class="btn-secondary px-3 py-1 rounded-lg text-xs flex items-center"><i class="fas fa-folder-plus mr-1.5"></i>Save to Collection</button>
                </div>
            </div>

            <div class="mb-4 text-xs">
                <p id="request-info">Your request took 0.00 seconds and will cost $0.000 per megapixel.</p>
            </div>

            <div>
                <div class="flex justify-between items-center mb-1 cursor-pointer" onclick="window.ui.toggleSectionVisibility('logs-container', this.querySelector('span'), this.querySelector('i'))">
                    <h3 class="text-sm font-medium">Logs</h3>
                    <span class="text-xs text-gray-400">Show <i class="fas fa-chevron-down ml-1"></i></span>
                </div>
                <div id="logs-container" class="hidden">
                    <div id="logs-content" class="logs-container text-xs w-[95%] mx-auto">
                        <div class="info-log">System initialized</div>
                        <div class="info-log">Ready for input</div>
                    </div>
                </div>
            </div>
                    </div> <!-- End of original Result Panel -->
                </div> <!-- End of fluxLorasInterface -->

                <!-- FLUX.1 Kontext [pro] Interface Panel (New, initially hidden) -->
                <div id="kontextProInterface" class="interface-panel active-panel flex flex-col lg:flex-row flex-1 gap-4">
                    <!-- Kontext Input Panel -->
                    <div class="panel p-4 flex-1 lg:w-[35%] lg:max-w-lg lg:max-h-[calc(100vh-2rem)] overflow-y-auto">
                        <h2 class="text-xl font-bold mb-4">Kontext Pro Input</h2>
                        <div class="mb-4">
                            <label for="kontextImageUpload" class="block text-xs font-medium mb-1">Upload Image</label>
                            <input type="file" id="kontextImageUpload" accept="image/*" class="input-field w-full p-2 rounded-lg text-sm file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-gray-700 file:text-gray-200 hover:file:bg-gray-600 cursor-pointer">
                        </div>
                        <div class="mb-4 flex justify-center items-center">
                            <img id="kontextImagePreview" src="#" alt="Image Preview" class="hidden max-w-full h-auto rounded-lg mb-2 border border-gray-700">
                        </div>
                        <div class="mb-4">
                            <label for="kontextPrompt" class="block text-xs font-medium mb-1">Edit Prompt</label>
                            <div class="relative">
                                <textarea id="kontextPrompt" rows="3" class="input-field w-full p-2 pr-8 rounded-lg text-sm" placeholder="Describe your edits... e.g., 'make the cat wear a hat'"></textarea>
                                <button id="clearKontextPromptBtn" class="absolute top-2 right-2 text-gray-500 hover:text-gray-700 focus:outline-none hidden" aria-label="Clear prompt">
                                    <i class="fas fa-times-circle"></i>
                                </button>
                            </div>
                            
                            <!-- Collapsible Pre-prompt Buttons Section -->
                            <div class="collapsible-prompts mt-2">
                                <button class="collapsible-trigger text-xs font-medium text-gray-400 hover:text-white transition-colors w-full flex justify-between items-center p-1.5 rounded-md bg-gray-700/50 hover:bg-gray-700">
                                    <span><i class="fas fa-lightbulb mr-2"></i>Quick Prompts</span>
                                    <i class="fas fa-chevron-down trigger-icon transition-transform"></i>
                                </button>
                                <div class="collapsible-content hidden mt-2">
                                    <div id="quick-prompts-container" class="mt-2">
                                        <!-- Quick prompts will be dynamically loaded here by JavaScript -->
                                        <div class="text-gray-400 text-xs text-center p-4">Loading quick prompts...</div>
                                    </div>
                                </div>
                            </div>

                             <!-- Anatomical Request Checkbox -->
                             <div class="my-3 flex items-center">
                                 <input id="kontextIsAnatomical" name="kontextIsAnatomical" type="checkbox" class="h-4 w-4 rounded border-gray-500 text-blue-600 focus:ring-blue-500 bg-gray-700">
                                 <label for="kontextIsAnatomical" class="ml-2 block text-sm font-medium text-gray-300">Anatomical Request (e.g., body shape changes)</label>
                             </div>
 
                             <!-- Prompt Strictness Level Selector (initially hidden, shown by JS if anatomical is checked) -->
                             <div id="kontextPromptStrictnessContainer" class="my-3 hidden border border-gray-600 p-3 rounded-md bg-gray-700/50"> <!-- Added ID and hidden class -->
                                 <label for="kontextPromptStrictness" class="block text-xs font-medium text-gray-300 mb-1">Prompt Strictness Level:</label>
                                 <select id="kontextPromptStrictness" name="kontextPromptStrictness" class="input-field w-full p-2 rounded-lg text-sm bg-gray-700 border border-gray-600 text-white focus:ring-blue-500 focus:border-blue-500">
                                     <option value="cautious" selected>Cautious / Subtle</option>
                                     <option value="balanced">Balanced</option>
                                     <option value="explicit">Direct / Explicit</option>
                                    <option value="extreme">Extreme / Strategic</option>
                                 </select>
                             </div>
 
                 <div class="mt-1.5 flex justify-between items-center"> <!-- Container for status and buttons -->
                     <span id="kontextImprovePromptStatus" class="text-xs text-gray-300 flex-grow"></span> <!-- Status message area, flex-grow to push buttons right -->
                     <div class="flex items-center gap-2"> <!-- Group for buttons -->
                         <button id="revert-kontext-prompt-btn" class="btn-secondary text-xs py-1 px-2 rounded-md hidden items-center gap-1" title="Revert to your original prompt">
                             <i class="fas fa-undo text-xs"></i> Revert
                         </button>
                         <button id="improve-kontext-prompt-btn" class="btn-secondary text-xs py-1 px-2 rounded-md flex items-center gap-1">
                             <i class="fas fa-magic text-xs"></i> Improve Prompt
                         </button>
                     </div>
                 </div>
             </div>
                        
                        <!-- Optional Kontext Parameters -->
                        <div class="mb-4">
                             <div class="flex justify-between items-center mb-1 cursor-pointer" onclick="window.ui.toggleSectionVisibility('kontext-additional-settings', this.querySelector('span'), this.querySelector('i'))">
                                <h3 class="text-sm font-medium">Additional Kontext Settings</h3>
                                <span class="text-xs text-gray-400">Show <i class="fas fa-chevron-down ml-1"></i></span>
                            </div>
                            <div id="kontext-additional-settings" class="hidden space-y-2 mt-2">
                                <div class="mb-3">
                                    <label id="kontext-guidance-label" class="block text-xs font-medium mb-1">Guidance Scale (Kontext)</label>
                                    <div class="flex items-center gap-2">
                                        <input type="range" id="kontextGuidanceScaleSlider" min="0" max="20" step="0.1" value="3.5" class="slider flex-1" aria-labelledby="kontext-guidance-label kontextGuidanceScaleInput">
                                        <input type="number" id="kontextGuidanceScaleInput" min="0" max="20" step="0.1" value="3.5" class="input-field w-16 p-1.5 rounded-lg text-sm text-center">
                                    </div>
                                </div>
                                 <div class="mb-3">
                                    <label id="kontext-num-images-label" class="block text-xs font-medium mb-1">Number of Images (Kontext)</label>
                                    <div class="flex items-center gap-2">
                                        <input type="range" id="kontextNumImagesSlider" min="1" max="4" value="1" class="slider flex-1" aria-labelledby="kontext-num-images-label kontextNumImagesInput">
                                        <input type="number" id="kontextNumImagesInput" min="1" max="4" value="1" class="input-field w-16 p-1.5 rounded-lg text-sm text-center">
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="kontextOutputFormat" class="block text-xs font-medium mb-1">Output Format (Kontext)</label>
                                    <select id="kontextOutputFormat" class="input-field w-full p-1.5 rounded-lg text-sm">
                                        <option value="jpeg" selected>jpeg</option>
                                        <option value="png">png</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="kontextSafetyTolerance" class="block text-xs font-medium mb-1">Safety Tolerance (Kontext)</label>
                                    <select id="kontextSafetyTolerance" class="input-field w-full p-1.5 rounded-lg text-sm">
                                        <option value="1">1 (Strict)</option>
                                        <option value="2">2</option>
                                        <option value="3">3</option>
                                        <option value="4">4</option>
                                        <option value="5" selected>5 (Permissive)</option>
                                        <option value="6">6</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label for="kontextAspectRatio" class="block text-xs font-medium mb-1">Aspect Ratio (Kontext)</label>
                                    <select id="kontextAspectRatio" class="input-field w-full p-1.5 rounded-lg text-sm">
                                        <option value="1:1" selected>1:1 (Square)</option>
                                        <option value="16:9">16:9 (Landscape)</option>
                                        <option value="9:16">9:16 (Portrait)</option>
                                        <option value="4:3">4:3 (Landscape)</option>
                                        <option value="3:4">3:4 (Portrait)</option>
                                        <option value="3:2">3:2 (Landscape)</option>
                                        <option value="2:3">2:3 (Portrait)</option>
                                        <option value="21:9">21:9 (Widescreen Landscape)</option>
                                        <option value="9:21">9:21 (Widescreen Portrait)</option>
                                    </select>
                                </div>
                                 <div class="mb-3">
                                    <label for="kontextSeed" class="block text-xs font-medium mb-1">Seed (Kontext)</label>
                                    <div class="flex items-center gap-1">
                                        <input type="text" id="kontextSeed" value="random" class="input-field flex-1 p-1.5 rounded-lg text-sm">
                                        <button class="btn-secondary p-1.5 rounded-lg" onclick="document.getElementById('kontextSeed').value = Math.floor(Math.random() * 1000000000);" title="Randomize Kontext seed" aria-label="Randomize Kontext seed">
                                            <i class="fas fa-random text-xs"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <button id="submitKontextEditButton" class="btn-primary w-full py-2 rounded-lg text-sm flex items-center justify-center gap-1">Edit Image (Kontext)</button>
                    </div>
                    <!-- Kontext Result Panel -->
                    <div class="panel p-3 flex-1 lg:w-[50%] lg:max-w-3xl lg:max-h-[calc(100vh-2rem)] overflow-y-auto">
                        <h2 class="text-xl font-bold mb-4">Kontext Pro Result</h2>
                        <div id="kontextStatusIndicatorArea" class="mb-3">
                           <span id="kontextStatusIndicator" class="status-indicator status-default text-xs">Ready</span>
                        </div>

                        <!-- Before/After Comparison Component -->
                        <div id="kontextComparisonContainer" class="mb-3 w-full">
                            <h3 class="text-center">Before/After Comparison</h3>

                            <div id="comparison-image-area" class="relative w-full aspect-square mx-auto max-w-xl bg-gray-800 rounded-lg overflow-hidden shadow-lg border border-gray-700">
                                <img id="comparison-before-image" src="" alt="Original Image" class="absolute top-0 left-0 w-full h-full object-contain select-none">
                                <img id="comparison-after-image" src="" alt="Edited Image" class="absolute top-0 left-0 w-full h-full object-contain select-none">
                                <div id="comparison-slider-handle" class="absolute top-0 bottom-0 w-1.5 bg-blue-500/70 cursor-ew-resize hover:bg-blue-400 transition-colors duration-150 z-20">
                                    <div class="absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 w-8 h-8 bg-blue-500 rounded-full flex items-center justify-center shadow-md">
                                        <i>⟷</i>
                                    </div>
                                </div>
                            </div>
                            <div class="mt-3 px-2">
                                <input type="range" id="comparison-slider-input" min="0" max="100" value="50" class="w-full h-2 bg-gray-700 rounded-lg appearance-none cursor-pointer slider accent-blue-500">
                            </div>

                            <div class="mt-4 flex flex-wrap justify-center gap-2 px-2">
                                <button id="comparison-reset-btn" class="btn-secondary px-3 py-1.5 text-xs rounded-md flex items-center gap-1.5" title="Reset slider position">
                                    <i class="fas fa-sync-alt"></i> Reset
                                </button>
                                <button id="comparison-swap-btn" class="btn-secondary px-3 py-1.5 text-xs rounded-md flex items-center gap-1.5" title="Swap Before/After images">
                                    <i class="fas fa-exchange-alt"></i> Swap Sides
                                </button>
                                <button id="comparison-toggle-view-btn" class="btn-secondary px-3 py-1.5 text-xs rounded-md flex items-center gap-1.5" title="Toggle between comparison and result view" data-view-mode="compare">
                                    <i class="fas fa-eye"></i> Show Result Only
                                </button>
                                <button id="comparison-save-btn" class="btn-secondary px-3 py-1.5 text-xs rounded-md flex items-center gap-1.5 disabled-btn" title="Save comparison (Not implemented)" disabled>
                                    <i class="fas fa-save"></i> Save Comparison
                                </button>
                                <button id="comparison-fullscreen-btn" class="btn-secondary px-3 py-1.5 text-xs rounded-md flex items-center gap-1.5 disabled-btn" title="Fullscreen (Not implemented)" disabled>
                                    <i class="fas fa-expand"></i> Fullscreen
                                </button>
                            </div>

                            <div id="comparison-metadata" class="mt-4 text-xs text-gray-400 flex flex-wrap justify-center items-center gap-x-3 gap-y-1 bg-gray-900/50 p-2 rounded-md border border-gray-700/50">
                                <span id="meta-original-wrapper" class="meta-item hidden items-center"><i class="fas fa-image mr-1.5 text-gray-500"></i><span id="meta-original-details" class="truncate" title="N/A">N/A</span></span>
                                <span id="meta-edited-wrapper" class="meta-item hidden items-center"><i class="fas fa-magic mr-1.5 text-gray-500"></i><span id="meta-edited-details">N/A</span></span>
                                <span id="meta-time-wrapper" class="meta-item hidden items-center"><i class="fas fa-clock mr-1.5 text-gray-500"></i><span id="meta-time">N/A</span></span>
                                <span id="meta-seed-wrapper" class="meta-item hidden items-center"><i class="fas fa-dice-five mr-1.5 text-gray-500"></i><span id="meta-seed">N/A</span></span>
                            </div>
                        </div>
                        <!-- End Before/After Comparison Component -->

                        <div id="kontextProgressArea" class="my-4 hidden">
                           <div class="text-xs text-gray-400 mb-1 text-center" id="kontextProgressStatusText">Initializing...</div>
                           <div class="w-full bg-gray-700 rounded-full h-1.5 dark:bg-gray-600">
                                <div id="kontextProgressBarIndicator" class="bg-blue-600 h-1.5 rounded-full" style="width: 0%"></div>
                           </div>
                       </div>
                       <!-- Kontext Logs (optional, similar to existing logs) -->
                        <div>
                            <div class="flex justify-between items-center mb-1 cursor-pointer" onclick="window.ui.toggleSectionVisibility('kontext-logs-container', this.querySelector('span'), this.querySelector('i'))">
                                <h3 class="text-sm font-medium">Kontext Logs</h3>
                                <span class="text-xs text-gray-400">Show <i class="fas fa-chevron-down ml-1"></i></span>
                            </div>
                            <div id="kontext-logs-container" class="hidden">
                                <div id="kontext-logs-content" class="logs-container text-xs w-[95%] mx-auto">
                                    <div class="info-log">Kontext system ready</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div> <!-- End of kontextProInterface -->
            </div> <!-- End of interfacesContainer -->
        </div> <!-- End of Right Column for Switchable Interfaces -->
    </div> <!-- End of main container -->

    <!-- Lightbox Modal (initially hidden) -->
    <div id="image-lightbox-modal" class="fixed inset-0 bg-black/75 flex items-center justify-center z-50 hidden">
        <div class="relative bg-gray-900 p-2 md:p-3 rounded-lg shadow-xl flex flex-col">
            <div class="flex-grow flex items-center justify-center p-1">
                <img id="lightbox-image" src="" alt="Enlarged view" class="w-auto h-auto max-w-full">
            </div>
            <div class="absolute top-2 right-2 md:top-3 md:right-3 flex gap-1">
                <button id="lightbox-download-btn" title="Download image" aria-label="Download enlarged image" class="text-white bg-black/40 hover:bg-black/60 rounded-full p-1.5 w-8 h-8 md:w-10 md:h-10 flex items-center justify-center transition-colors">
                    <i class="fas fa-download text-sm md:text-base"></i>
                </button>
                <button id="lightbox-close-btn" title="Close lightbox" aria-label="Close enlarged image view" class="text-white bg-black/40 hover:bg-black/60 rounded-full p-1.5 w-8 h-8 md:w-10 md:h-10 flex items-center justify-center transition-colors">
                    <i class="fas fa-times text-sm md:text-base"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- JavaScript modules -->
    <script src="assets/js/ui.js"></script>
    <script src="assets/js/api.js"></script>
    <script src="assets/js/formHandlers.js"></script>
    <script src="assets/js/imageHandlers.js"></script>
    <script src="components/lightbox.js"></script>
    <script src="components/loraManager.js"></script>
    <script src="components/presetManager.js"></script>
    <script src="components/comparisonSlider.js"></script>
    <script src="assets/js/main.js"></script>
</body>
</html>
