Article 1
Doing simple edits to an image has always been way harder than it should be. You need to learn Photoshop's million buttons, spend forever figuring out which tool does what, and even basic changes end up taking hours. The recent wave of AI tools promised to fix this, but most of them just created new problems. You still have to describe everything you want in detail, and half the time they change things you never asked them to touch.

FLUX.1 Kontext is a new family of AI models (Dev, Pro and Max) that changes this completely. Instead of describing what you want to create, you simply tell it what you want to change. Need to make a car red? Just say "change the car color to red". Want to update the text on a sign? Tell it "change 'FOR SALE' to 'SOLD'" and it handles the rest while keeping everything else exactly the same.

Contemporary two-story house with neon 'FOR SALE' sign on the front lawn, viewed at sunset with warm interior lighting
Original image
Same modern house with a neon 'SOLD' sign in front, indicating the property has been purchased, during early evening light
change the holographic sign to say 'SOLD' in bold red light
Modern white house with large glass windows during a social gathering, with people mingling on the patio at dusk
remove the sign and add a party with many people around the house
FLUX.1 Kontext comes from Black Forest Labs, the team behind the original FLUX models. This represents a big shift toward natural language image editing. It keeps characters looking consistent when you move them to different scenes, preserves text styling when you change words, and lets you make multiple edits in sequence. You can build up complex changes step by step, with each edit building on the previous one without losing what you've already done.

Availability note
FLUX.1 Kontext [max] and [pro] are available now via API. FLUX.1 Kontext [dev] is currently in research testing and will be released publicly soon.

What is FLUX.1 Kontext
Most AI editing tools work backwards. They make you describe the entire image you want to end up with, like you're commissioning a painting from scratch. FLUX.1 Kontext flips this around. You just tell the model what needs fixing in the current image, and it handles that specific thing while leaving everything else untouched.

Character consistency across scenes
The model excels at maintaining character identity across completely different environments. Take a photo of someone and place them as a chef in a restaurant kitchen or as an astronaut on Mars. The facial features, expressions, and distinctive characteristics remain perfectly consistent while everything else transforms around them.

Elderly man with white, tousled hair and a mustache wearing a black suit and tie, posing for a serious portrait against a neutral background
A photorealistic portrait of Albert Einstein, clearly showing his face, hair, and expression. ideally front-facing or 3/4 angle, with minimal background clutter
This opens up powerful workflows for storytelling and content creation. Instead of hiring models for multiple photo shoots or spending hours in Photoshop compositing different backgrounds, you can generate entire character narratives from a single reference image.

The consistency isn't just about faces. FLUX.1 Kontext understands object identity too. A specific car, building, or product maintains its unique characteristics as you place it in different scenarios. This makes it invaluable for product marketing where you need the same item shown in multiple contexts without the expense of multiple photo shoots.

Precise object-level control
FLUX.1 Kontext understands what you're pointing at and can transport it between contexts. Take a logo and place it on a sticker. Take that sticker and apply it to a laptop. Take the laptop and position it in a coffee shop scene. Each step preserves the object's identity while naturally integrating it into the new environment with proper lighting, shadows, and perspective.

This localized semantic editing works because the model understands object boundaries and relationships within images. It doesn't just copy and paste elements. It comprehends how objects should behave in different contexts, how a sticker curves around a laptop edge, how a laptop reflects coffee shop lighting, how shadows and reflections adapt to new environments.

A rock band logo with bold gothic lettering saying "NEON STORM", electric blue and purple colors, lightning bolt accents, black background, heavy metal style

'Neon Storm' electric logo in glowing blue and pink text on a black background
Original image
Black vinyl record with a glowing 'Neon Storm' label featuring electric lightning effects
Put this logo as the center label on a black vinyl record
Close-up of a turntable spinning the 'Neon Storm' vinyl under soft lighting
Place this vinyl record on a vintage turntable with the needle positioned on the record
Vintage turntable setup playing a vinyl with the 'Neon Storm' label, surrounded by studio equipment
Show this turntable setup in a professional recording studio with mixing boards, monitors, and warm studio lighting
Man using a turntable with a 'Neon Storm' record in a home studio with speakers and audio gear
Zoom out to show a musician in the studio working at the mixing board with the turntable visible in the background
Product placement workflows become incredibly fluid with this capability. Move products through multiple scenarios without complex compositing. Brand asset management transforms when you can transport logos and graphics across different media and contexts while maintaining perfect integration. Marketing teams can visualize products in countless environments without expensive photo shoots or 3D rendering.

This process involves more than simply positioning objects in a new scene. Objects scale appropriately for their new context, adopt realistic lighting from their surroundings, and integrate with proper depth and perspective relationships. This makes FLUX.1 Kontext feel less like an editing tool and more like having the ability to naturally relocate any object into any scene where it belongs.

Superior text editing
Most AI models struggle with text because they treat it like any other visual element. They might change the font, alter the styling, or completely miss the context. FLUX.1 Kontext understands that text has meaning and maintains the original typography, effects, and positioning while making precise changes.

Update a vintage poster from "SALE" to "SOLD" and the model preserves the ornate lettering style, drop shadows, and color gradients. Change a street sign from "Main St" to "Oak Ave" and the official font, reflective properties, and mounting hardware stay identical. This typography preservation makes it perfect for localization workflows where you need to translate signage, update marketing materials, or personalize graphics without losing design integrity.

A vibrant fruit juice pouch standing upright on a glossy pastel table, surrounded by floating slices of orange, kiwi, and passionfruit, playful splashes of juice in the air, the pouch has the word ‘RUNWARE’ in large bubbly gradient letters with a soft drop shadow, warm ambient lighting and summer ad feel

Instruction verbs that work well
FLUX.1 Kontext responds best to clear action verbs. Here are the most effective ones.

For modifications: "Change", "Make", "Transform", "Convert"

"Change the sky to sunset"
"Make the walls brick"
"Convert to black and white"
For additions: "Add", "Include", "Put"

"Add sunglasses to the person"
"Put mountains in the background"
For removals: "Remove", "Delete", "Take away"

"Remove the person in the background"
"Delete the text from the sign"
For replacements: "Replace", "Swap", "Substitute"

"Replace 'OPEN' with 'CLOSED'"
"Swap the blue car with a red truck"
For positioning: "Move", "Place", "Position"

"Move the person to the left side"
"Place a tree behind the house"
Simple instruction templates
These patterns work for most common editing tasks. Start with these frameworks and add more details as needed.

Object modification: "[Action] the [object] to [description]"

"Change the car to red"
"Make the building taller"
Text replacement: "Replace '[old text]' with '[new text]'"

"Replace 'SALE' with 'SOLD'"
Style changes: "Convert to [style] while maintaining [what to preserve]"

"Convert to watercolor while maintaining the composition"
Character edits: "Change the [person description] to [change] while preserving [identity features]"

"Change the woman with blonde hair to wearing a red dress while preserving her facial features"
Start simple and be specific
FLUX.1 Kontext excels at iterative editing, not scene recreation. Instead of describing entire scenes, focus on specific changes. "Change the car color to red" tells Kontext exactly what to modify. "Make this image have a red car in it" sounds like you're asking it to recreate the entire scene with a red car, which isn't what you want.

Don't try to make five changes in one instruction. Start with the most important edit, see the result, then add the next change. This approach gives you more control and better results, especially since the model has a 512 token maximum for prompts.

Step 1: "Change to daytime"
Step 2: "Add people walking on the sidewalk"
Step 3: "Make the building walls brick"
Each edit builds on the previous one while preserving what you've already accomplished.

Text editing has special rules
Use quotation marks around the specific text you want to change. This tells Kontext you're doing text replacement, not adding new text elements.

Good: Replace "SALE" with "SOLD"
Less effective: Change the sign to say SOLD instead of SALE
Pro tip: If you want to preserve the original styling, add "while maintaining the same font style and color". Complex or stylized fonts work better when you explicitly ask to preserve their characteristics.

Style transfer needs specific language
Name the exact style you want. "Make it artistic" is too vague. "Convert to watercolor painting" or "Transform to pencil sketch with cross-hatching" gives Kontext clear direction.

When using style references: If you have a reference image with a specific style, use prompts like "Using this style, [describe what you want to generate]". This works especially well with FLUX.1 Kontext [pro].

Composition control prevents unwanted changes
Simple background changes can accidentally move your subject. Instead of "put him on a beach", try "change the background to a beach while keeping the person in the exact same position, scale, and pose".

When you want surgical precision: Add phrases like "maintain identical subject placement" or "only replace the environment around them" to prevent unwanted repositioning.

Common troubleshooting
When editing people, avoid pronouns. Instead of saying "make her hair longer," say "make the woman with short black hair have longer hair". Kontext needs clear identity markers to maintain consistency across edits.

If Kontext changes too much: Be more explicit about what should stay the same. Add "while maintaining all other aspects of the original image" or "everything else should remain unchanged".

If character identity drifts: Use more specific descriptors and avoid broad transformation verbs. "Change the clothes to medieval armor" works better than "transform into a medieval character".

If style transfer loses important details: Describe the visual characteristics of the style you want. "Convert to oil painting with visible brushstrokes, thick paint texture, and rich color depth" preserves more scene information than just "make it an oil painting".


Article 2
Understanding the Magic: What Makes FLUX.1 Kontext Different?
So, what’s the big deal about FLUX.1 Kontext? It’s not just another tool; it’s a shift in how we interact with AI for image creation. Instead of wrestling with lengthy descriptive prompts for every little tweak, FLUX.1 thrives on direct instructions. You tell it what to change, not just what to see.

Here’s why it’s turning heads:

Instruction-Based Editing: This is the core. You can say “Change the car color to red” or “Add a hat on the character” instead of re-describing the entire scene. It’s intuitive and saves a ton of time.
Deep Contextual Understanding: FLUX.1 is smart. It “reads” the context of your image and your prompt, so your edits make sense and blend naturally. No more awkwardly pasted-on elements!
Rock-Solid Character Consistency: Ever struggled to keep a character looking the same across different images? FLUX.1 excels here. You can maintain a character’s features, outfit, or vibe even in totally different scenes — a massive win for storytelling and branding.
Precision Local Editing: Need to tweak just one tiny detail without messing up the rest? FLUX.1 allows for highly targeted modifications.
Style Reference & Transfer: Got an artistic style you love? FLUX.1 can generate new scenes while preserving that unique style from a reference image, all guided by your text prompts.
Speedy & Iterative: The model is designed for quick iterations. You can layer edits, refine step-by-step, and watch your vision come to life without frustrating delays or loss of quality.
Essentially, FLUX.1 aims to be a more holistic editing environment, reducing the need to jump between multiple specialized AI tools.

The Art of Instruction: Key Principles for Crafting FLUX.1 Kontext Prompts
To get the best out of FLUX.1 Kontext, you need to speak its language. It’s less about painting a poetic picture with words and more about giving clear, actionable directions.

Command with Verbs: Use action words! “Change,” “replace,” “add,” “remove,” “transform” are your best friends. Be mindful, as “change” might yield different results than “transform.”
Preserve What’s Perfect: Use “while maintaining…” or “keeping…” clauses to tell the AI what not to alter. For example, “…while maintaining the same facial features.” This is crucial for preventing unwanted changes.
Specificity is Your Superpower: Ambiguity is your enemy. Name your subjects clearly (e.g., “the woman with short black hair” instead of “she”). Specify art styles (e.g., “1960s pop art poster style” not just “make it artistic”). Detail colors, textures, and lighting.
Iterate, Iterate, Iterate: Don’t try to do everything in one go. Break complex transformations into a sequence of simpler edits. FLUX.1 is built for this step-by-step refinement.
Mind the Limit: Prompts have a 512-token limit. For complex edits, break them into smaller, sequential instructions for better control and results.
20 Essential FLUX.1 Kontext Prompts to Ignite Your Creativity
Ready to dive in? Here are 20 prompts, categorized for different creative goals, to get you started with FLUX.1 Kontext. Each includes an input idea, the prompt, and the expected outcome.

Precision Edits: Modifying, Removing, Adding Objects and Elements
These prompts focus on FLUX.1 Kontext’s ability to make localized changes, add elements, remove unwanted details, or alter object attributes, all while preserving the integrity of the surrounding scene. Imagine the time saved compared to manual editing!

1. Scenario: changing the car Color To green.
Prompt: “Change the car color to vibrant metallic green.”
Expected Outcome: Only the car’s color changes; reflections and background remain consistent.

2. Scenario: Removing Repeating Tiled Watermarks from a Complex Scene

Input Image:


FLUX.1 Kontext Prompt: “Remove all visible watermarks from this image. This includes any superimposed text, logos, repeating patterns, or other overlay graphics that are not part of the original scene. Seamlessly reconstruct the underlying image details where the watermarks were present. Ensure the final image is clean, the original textures, colors, and overall quality are perfectly preserved, and the image is free of any removal artifacts or smudging.”
Expected Outcome/Showcased Feature: All tiled watermarks are erased across the complex scene. The AI intelligently fills in the varied background details (sky, architecture, lighting), which would be incredibly tedious and difficult to do manually while maintaining realism.



2. Scenario: Removing Any Specified Unwanted Object from a Scene


FLUX.1 Kontext Prompt (Universal Template): “Remove the [describe the unwanted object and its approximate location, e.g., ‘red fire hydrant on the left sidewalk’] from the image. Naturally reconstruct the background details (such as [mention specific background elements if critical, e.g., ‘grass and trees,’ ‘building facade,’ ‘sky’]) that were obscured by this object. Ensure the rest of the image, including [mention any key elements to preserve, e.g., ‘the main subject,’ ‘the surrounding landscape’], remains completely untouched and its original quality is perfectly preserved.” *

Expected Outcome/Showcased Feature: The specified unwanted object is seamlessly erased, and the area it occupied is realistically filled in, blending with the surrounding environment. This template allows users to easily target various objects for removal without needing complex manual editing tools like cloning or patching, saving considerable cleanup time and effort.

Unlock Peak Efficiency: FLUX.1 Kontext Prompts That Slash Your Workload

These prompts go beyond simple tweaks, targeting common labor-intensive tasks. Imagine the hours saved!

1. . Scenario: Instant E-commerce Lifestyle Shots

Traditional Bottleneck: Arranging and paying for multiple lifestyle photoshoots for each product to show it in context, or spending hours in Photoshop trying to realistically composite a product into stock photos.
Input Image Idea: A clean studio shot of a “pair of sleek wireless headphones” on a plain white background.
FLUX.1 Kontext Prompt: “Place the sleek wireless headphones onto the head of a male model posing in a professional photography studio. The scene should be illuminated with clean studio lighting that accentuates the headphone’s design. Ensure the headphones fit naturally on his head and that their original design, color, and details are perfectly maintained, with lighting on the headphones consistent with the studio environment.”
Expected Outcome & Work Reduction Benefit: You get a dynamic, realistic lifestyle image of your product in use without a separate photoshoot or complex manual editing. This allows for rapid creation of diverse marketing assets, saving significant time and budget.
2. Scenario: Rapid Ad Creative Localization & Variation

Traditional Bottleneck: Manually redesigning ad banners or social media posts for different regions (e.g., changing backgrounds to local landmarks, altering text) or for A/B testing different visual themes. This often involves multiple design revisions.
Input Image Idea: An existing ad banner featuring “a family enjoying a picnic with a generic park background” and English text “Family Fun Day!”.

FLUX.1 Kontext Prompt: “Change the background to a sunny beach scene with clear blue water. Replace the text ‘Family Fun Day!’ with ‘Vacaciones en Familia!’ using a playful, bold font. Keep the family, their poses, and expressions identical, and adjust the lighting on them to match the bright beach setting.”

3. Scenario: Consistent Character/Mascot Placement for Storytelling

Traditional Bottleneck: Illustrating or animating a brand mascot or character consistently across various scenes, marketing materials, or storyboards. This requires significant artistic skill and time for each new instance.
Input Image Idea: A well-defined image of “Professor Wiz, the owl mascot wearing a graduation cap and spectacles.”
FLUX.1 Kontext Prompt: “Professor Wiz, the owl mascot, is now standing at a podium giving a lecture in a university hall filled with students (students can be slightly blurred). Maintain his exact appearance, including graduation cap and spectacles, and ensure he looks engaged with the audience.”
Expected Outcome & Work Reduction Benefit: Your mascot is perfectly placed in a new, complex scene while retaining all its defining features. This is invaluable for quickly creating engaging content series, presentations, or social media campaigns without the repetitive work of redrawing or re-rendering the character.
4. Scenario: Ultra-Fast Architectural & Interior Design Iterations

Traditional Bottleneck: Showing clients different material finishes, furniture arrangements, or lighting conditions for a design. This typically means re-rendering 3D models (which can take hours or days) or complex, time-consuming Photoshop edits.
Input Image Idea: An architectural render of a “modern living room with a grey fabric sofa, light wood flooring, and daytime lighting from a large window.”
FLUX.1 Kontext Prompt: “Change the grey fabric sofa to a dark brown leather sofa. Transform the light wood flooring to polished concrete. Adjust the lighting to a cozy evening ambiance with warm interior lights on, while maintaining the room’s structure, window placement, and other existing furniture.”
Expected Outcome & Work Reduction Benefit: Multiple design variations are visualized almost instantly. Designers can offer clients a wider range of options in a fraction of the time, accelerating feedback loops and decision-making without waiting for lengthy re-renders or manual mockups.
5. Scenario: Batch Product Recolorization for Catalogs

Traditional Bottleneck: Photographing every single color variant of a product (e.g., apparel, electronics, accessories) or painstakingly and often imperfectly changing colors in photo editing software while preserving textures and lighting.
Input Image Idea: A high-quality photo of “a bright yellow stand mixer” on a kitchen counter.
FLUX.1 Kontext Prompt: “Change the color of the bright yellow stand mixer to a pastel mint green, while meticulously maintaining its metallic finish, all logos, button details, shadows, and highlights consistent with the existing kitchen counter lighting.”
Expected Outcome & Work Reduction Benefit: You generate a photorealistic image of your product in a new color, ready for your online store or catalog. This dramatically reduces the need for extensive photoshoots for each color variant, saving significant production costs and speeding up time-to-market for new product lines.
Character Consistency: Keeping Your Subjects Coherent
6. Input Image Idea: Portrait of “a man with a short beard and glasses, wearing a yellow jacket.”

Prompt: “The man with a short beard and glasses is now walking through a bustling futuristic city street at night, neon lights reflecting in his glasses, while maintaining his facial features, beard, glasses, and yellow jacket.”


7. Input Image Idea: Image of “the elf warrior with pointed ears and silver armor” standing in a forest.

Prompt: “Change the setting for the elf warrior with pointed ears and silver armor to an ancient, lava-filled cavern, with him cautiously stepping onto a rock bridge, while maintaining his exact facial features, armor design, and alert expression.”
Expected Outcome: Character consistently appears in a drastically different, high-fantasy environment.
8. Input Image Idea: Full-body shot of “the girl in the red polka-dot dress.”

Prompt: “Change the girl in the red polka-dot dress’s outfit to a sleek black spy catsuit with a utility belt, while maintaining her youthful facial features and determined stance.”
Expected Outcome: Attire transforms completely, but character identity and pose are preserved.
9. Input Image Idea: Image of “a uniquely designed robot with blue optical sensors.”

Prompt: “Place this robot with blue optical sensors into a scene depicting a serene Japanese zen garden, tending to the raked sand, while maintaining its original mechanical design and blue optical sensors.”
Expected Outcome: Stylized robot character integrated into a contrasting peaceful environment.
10. Input Image Idea: A character “the wizard with a long white beard holding a staff”.

Prompt: “The wizard with a long white beard is now underwater, surrounded by glowing jellyfish, his staff emitting a magical light, while maintaining his facial features and iconic beard.”
Expected Outcome: Character consistency is maintained in a surreal, challenging environment.
Style Alchemy: Transforming and Applying Artistic Visions
11. Input Image Idea: Detailed photo of a modern cityscape.


Prompt: “Transform this cityscape to a Van Gogh ‘Starry Night’ painting style, with swirling brushstrokes and vivid night colors, while maintaining the original composition and recognizable skyscraper forms.”

12. Input Image Idea: Color portrait photo.

Prompt: “Convert this portrait to a vibrant pop art style similar to Andy Warhol, using bold, contrasting colors and a screen-printed look, while maintaining all facial features accurately.”
Expected Outcome: High-fidelity pop art conversion preserving likeness.
13. Input Image Idea: Simple digital illustration of a house.

Prompt: “Restyle this house illustration to a detailed architectural blueprint style, with white lines on a blue background, including annotations and measurement lines, while keeping the house’s overall structure.”
Expected Outcome: 2D illustration becomes a convincing architectural blueprint.
14. Input Image Idea: Photo of a forest path.

Prompt: “Transform this forest path scene into a fantasy bioluminescent forest at night, with glowing plants and magical particles in the air, while keeping the path layout recognizable.”
Expected Outcome: Scene adopts a magical, illuminated aesthetic.
15. Input Image Idea: A photograph of a classic car.

Prompt: “Transform this classic car photo into a gritty, comic book ink sketch style, with heavy blacks, sharp outlines, and dramatic cross-hatching for shadows, while maintaining the car’s distinct model features.”
Expected Outcome: The car is rendered in a dynamic comic book style, preserving its form.
Textual Transformations: Editing and Integrating Typography
16. Input Image Idea: Photo of a shop sign reading “OPEN”.

Prompt: “Replace the text ‘OPEN’ with ‘CLOSED FOR LUNCH’ on the sign, while maintaining the same vintage font style, red color, and slightly weathered look of the original text.”
Output:

17. Input Image Idea: Blank parchment scroll image.

Prompt: “Add the text ‘Secret Map’ in an old English gothic script font, dark sepia ink, centered at the top of the scroll.”
Expected Outcome: New text added in specified style, appearing naturally inscribed.
18. Input Image Idea: Event poster with headline “MUSIC FEST 2025”.

Prompt: “Replace ‘MUSIC FEST 2025’ with ‘SOUNDWAVE GALA 2026’ while maintaining the existing futuristic bold font, glowing blue neon effect, and central positioning.”
Expected Outcome: Headline text updated, complex typographic effects maintained.
Advanced Scene Evolution (Iterative Example)
Imagine starting with a photo: “A woman standing on a sunny beach.”

19. Prompt 1 (Initial Change):

Input Image Idea: Woman on a sunny beach.
Prompt: “Change the sunny beach background to a snowy mountaintop scene at sunset for the woman, while keeping her in the exact same position, pose, and maintaining her facial features and current clothing.”
Expected Outcome: Woman is now on a snowy mountain at sunset, her appearance and pose unchanged.
20. Prompt 2 (Adding an Element — apply to output of Prompt 19):

Input Image Idea: Output from Prompt 19 (Woman on snowy mountain).
Prompt: “Add a friendly Siberian husky dog sitting next to her on the snow, looking towards the sunset, while maintaining the woman’s appearance and the sunset lighting.”
Expected Outcome: A husky is added realistically to the scene, consistent with lighting and character.
Pro-Tips for FLUX.1 Kontext Mastery
Troubleshooting: If edits go awry (e.g., character identity changes, composition shifts), reinforce preservation with more specific clauses like “preserving his exact facial features, eye color” or “keeping the person in the exact same position, scale, and pose.”
Prompt Precision Levels:
Simple Edits: Quick, direct (e.g., “Change to nighttime.”) — good for rapid ideas.
Controlled Edits: Add preservation clauses for stability (e.g., “Change to nighttime while maintaining the style of the painting.”) — better for predictable results.
Complex Transforms: Detailed instructions for multiple changes (e.g., “Change setting to nighttime, add streetlights illuminating the road, while maintaining the car’s design and color.”) — maximum control. Start simple and add complexity as needed.