{"name": "@fal-ai/client", "description": "The fal.ai client for JavaScript and TypeScript", "version": "1.4.0", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/fal-ai/fal-js.git", "directory": "libs/client"}, "keywords": ["fal", "client", "ai", "ml", "typescript"], "exports": {".": "./src/index.js", "./endpoints": "./src/types/endpoints.js"}, "typesVersions": {"*": {"endpoints": ["src/types/endpoints.d.ts"]}}, "main": "./src/index.js", "types": "./src/index.d.ts", "dependencies": {"@msgpack/msgpack": "^3.0.0-beta2", "eventsource-parser": "^1.1.2", "robot3": "^0.4.1"}, "engines": {"node": ">=18.0.0"}, "type": "commonjs"}