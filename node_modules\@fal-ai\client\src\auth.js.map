{"version": 3, "file": "auth.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/auth.ts"], "names": [], "mappings": ";;;;;;;;;;;;AASA,sDAoBC;AA7BD,qCAAyD;AACzD,uCAA4C;AAC5C,mCAA0C;AAE7B,QAAA,wBAAwB,GAAG,GAAG,CAAC;AAE5C;;GAEG;AACH,SAAsB,qBAAqB,CACzC,GAAW,EACX,MAAsB;;QAEtB,MAAM,KAAK,GAAG,IAAA,uBAAe,EAAC,GAAG,CAAC,CAAC;QACnC,MAAM,KAAK,GAAoB,MAAM,IAAA,yBAAe,EAAc;YAChE,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,GAAG,IAAA,sBAAa,GAAE,UAAU;YACvC,MAAM;YACN,KAAK,EAAE;gBACL,YAAY,EAAE,CAAC,KAAK,CAAC,KAAK,CAAC;gBAC3B,gBAAgB,EAAE,gCAAwB;aAC3C;SACF,CAAC,CAAC;QACH,iFAAiF;QACjF,yCAAyC;QACzC,IAAI,OAAO,KAAK,KAAK,QAAQ,IAAI,KAAK,CAAC,QAAQ,CAAC,EAAE,CAAC;YACjD,OAAO,KAAK,CAAC,QAAQ,CAAC,CAAC;QACzB,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;CAAA", "sourcesContent": ["import { getRestApiUrl, RequiredConfig } from \"./config\";\nimport { dispatchRequest } from \"./request\";\nimport { parseEndpointId } from \"./utils\";\n\nexport const TOKEN_EXPIRATION_SECONDS = 120;\n\n/**\n * Get a token to connect to the realtime endpoint.\n */\nexport async function getTemporaryAuthToken(\n  app: string,\n  config: RequiredConfig,\n): Promise<string> {\n  const appId = parseEndpointId(app);\n  const token: string | object = await dispatchRequest<any, string>({\n    method: \"POST\",\n    targetUrl: `${getRestApiUrl()}/tokens/`,\n    config,\n    input: {\n      allowed_apps: [appId.alias],\n      token_expiration: TOKEN_EXPIRATION_SECONDS,\n    },\n  });\n  // keep this in case the response was wrapped (old versions of the proxy do that)\n  // should be safe to remove in the future\n  if (typeof token !== \"string\" && token[\"detail\"]) {\n    return token[\"detail\"];\n  }\n  return token;\n}\n"]}