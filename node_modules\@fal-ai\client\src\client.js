"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createFalClient = createFalClient;
const config_1 = require("./config");
const queue_1 = require("./queue");
const realtime_1 = require("./realtime");
const request_1 = require("./request");
const response_1 = require("./response");
const storage_1 = require("./storage");
const streaming_1 = require("./streaming");
/**
 * Creates a new reference of the `FalClient`.
 * @param userConfig Optional configuration to override the default settings.
 * @returns a new instance of the `FalClient`.
 */
function createFalClient(userConfig = {}) {
    const config = (0, config_1.createConfig)(userConfig);
    const storage = (0, storage_1.createStorageClient)({ config });
    const queue = (0, queue_1.createQueueClient)({ config, storage });
    const streaming = (0, streaming_1.createStreamingClient)({ config, storage });
    const realtime = (0, realtime_1.createRealtimeClient)({ config });
    return {
        queue,
        realtime,
        storage,
        streaming,
        stream: streaming.stream,
        run(endpointId_1) {
            return __awaiter(this, arguments, void 0, function* (endpointId, options = {}) {
                const input = options.input
                    ? yield storage.transformInput(options.input)
                    : undefined;
                return (0, request_1.dispatchRequest)({
                    method: options.method,
                    targetUrl: (0, request_1.buildUrl)(endpointId, options),
                    input: input,
                    config: Object.assign(Object.assign({}, config), { responseHandler: response_1.resultResponseHandler }),
                    options: {
                        signal: options.abortSignal,
                    },
                });
            });
        },
        subscribe: (endpointId, options) => __awaiter(this, void 0, void 0, function* () {
            const { request_id: requestId } = yield queue.submit(endpointId, options);
            if (options.onEnqueue) {
                options.onEnqueue(requestId);
            }
            yield queue.subscribeToStatus(endpointId, Object.assign({ requestId }, options));
            return queue.result(endpointId, { requestId });
        }),
    };
}
//# sourceMappingURL=client.js.map