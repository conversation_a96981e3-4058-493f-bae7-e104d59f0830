{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/client.ts"], "names": [], "mappings": ";;;;;;;;;;;AAwFA,0CAyCC;AAjID,qCAAgD;AAChD,mCAAgF;AAChF,yCAAkE;AAClE,uCAAsD;AACtD,yCAAmD;AACnD,uCAA+D;AAC/D,2CAAqE;AA6ErE;;;;GAIG;AACH,SAAgB,eAAe,CAAC,aAAqB,EAAE;IACrD,MAAM,MAAM,GAAG,IAAA,qBAAY,EAAC,UAAU,CAAC,CAAC;IACxC,MAAM,OAAO,GAAG,IAAA,6BAAmB,EAAC,EAAE,MAAM,EAAE,CAAC,CAAC;IAChD,MAAM,KAAK,GAAG,IAAA,yBAAiB,EAAC,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;IACrD,MAAM,SAAS,GAAG,IAAA,iCAAqB,EAAC,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;IAC7D,MAAM,QAAQ,GAAG,IAAA,+BAAoB,EAAC,EAAE,MAAM,EAAE,CAAC,CAAC;IAClD,OAAO;QACL,KAAK;QACL,QAAQ;QACR,OAAO;QACP,SAAS;QACT,MAAM,EAAE,SAAS,CAAC,MAAM;QAClB,GAAG;iEACP,UAAc,EACd,UAAqC,EAAE;gBAEvC,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK;oBACzB,CAAC,CAAC,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC;oBAC7C,CAAC,CAAC,SAAS,CAAC;gBACd,OAAO,IAAA,yBAAe,EAAwC;oBAC5D,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,SAAS,EAAE,IAAA,kBAAQ,EAAC,UAAU,EAAE,OAAO,CAAC;oBACxC,KAAK,EAAE,KAAsB;oBAC7B,MAAM,kCACD,MAAM,KACT,eAAe,EAAE,gCAAqB,GACvC;oBACD,OAAO,EAAE;wBACP,MAAM,EAAE,OAAO,CAAC,WAAW;qBAC5B;iBACF,CAAC,CAAC;YACL,CAAC;SAAA;QACD,SAAS,EAAE,CAAO,UAAU,EAAE,OAAO,EAAE,EAAE;YACvC,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,GAAG,MAAM,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,OAAO,CAAC,CAAC;YAC1E,IAAI,OAAO,CAAC,SAAS,EAAE,CAAC;gBACtB,OAAO,CAAC,SAAS,CAAC,SAAS,CAAC,CAAC;YAC/B,CAAC;YACD,MAAM,KAAK,CAAC,iBAAiB,CAAC,UAAU,kBAAI,SAAS,IAAK,OAAO,EAAG,CAAC;YACrE,OAAO,KAAK,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QACjD,CAAC,CAAA;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["import { Config, createConfig } from \"./config\";\nimport { createQueueClient, QueueClient, QueueSubscribeOptions } from \"./queue\";\nimport { createRealtimeClient, RealtimeClient } from \"./realtime\";\nimport { buildUrl, dispatchRequest } from \"./request\";\nimport { resultResponse<PERSON><PERSON><PERSON> } from \"./response\";\nimport { createStorageClient, StorageClient } from \"./storage\";\nimport { createStreamingClient, StreamingClient } from \"./streaming\";\nimport { EndpointType, InputType, OutputType } from \"./types/client\";\nimport { Result, RunOptions } from \"./types/common\";\n\n/**\n * The main client type, it provides access to simple API model usage,\n * as well as access to the `queue` and `storage` APIs.\n *\n * @see createFalClient\n */\nexport interface FalClient {\n  /**\n   * The queue client to interact with the queue API.\n   */\n  readonly queue: QueueClient;\n\n  /**\n   * The realtime client to interact with the realtime API\n   * and receive updates in real-time.\n   * @see #RealtimeClient\n   * @see #RealtimeClient.connect\n   */\n  readonly realtime: RealtimeClient;\n\n  /**\n   * The storage client to interact with the storage API.\n   */\n  readonly storage: StorageClient;\n\n  /**\n   * The streaming client to interact with the streaming API.\n   * @see #stream\n   */\n  readonly streaming: StreamingClient;\n\n  /**\n   * Runs a fal endpoint identified by its `endpointId`.\n   *\n   * @param endpointId The endpoint id, e.g. `fal-ai/fast-sdxl`.\n   * @param options The request options, including the input payload.\n   * @returns A promise that resolves to the result of the request once it's completed.\n   *\n   * @note\n   * We **do not recommend** this use for most use cases as it will block the client\n   * until the response is received. Moreover, if the connection is closed before\n   * the response is received, the request will be lost. Instead, we recommend\n   * using the `subscribe` method for most use cases.\n   */\n  run<Id extends EndpointType>(\n    endpointId: Id,\n    options: RunOptions<InputType<Id>>,\n  ): Promise<Result<OutputType<Id>>>;\n\n  /**\n   * Subscribes to updates for a specific request in the queue.\n   *\n   * @param endpointId - The ID of the API endpoint.\n   * @param options - Options to configure how the request is run and how updates are received.\n   * @returns A promise that resolves to the result of the request once it's completed.\n   */\n  subscribe<Id extends EndpointType>(\n    endpointId: Id,\n    options: RunOptions<InputType<Id>> & QueueSubscribeOptions,\n  ): Promise<Result<OutputType<Id>>>;\n\n  /**\n   * Calls a fal app that supports streaming and provides a streaming-capable\n   * object as a result, that can be used to get partial results through either\n   * `AsyncIterator` or through an event listener.\n   *\n   * @param endpointId the endpoint id, e.g. `fal-ai/llavav15-13b`.\n   * @param options the request options, including the input payload.\n   * @returns the `FalStream` instance.\n   */\n  stream: StreamingClient[\"stream\"];\n}\n\n/**\n * Creates a new reference of the `FalClient`.\n * @param userConfig Optional configuration to override the default settings.\n * @returns a new instance of the `FalClient`.\n */\nexport function createFalClient(userConfig: Config = {}): FalClient {\n  const config = createConfig(userConfig);\n  const storage = createStorageClient({ config });\n  const queue = createQueueClient({ config, storage });\n  const streaming = createStreamingClient({ config, storage });\n  const realtime = createRealtimeClient({ config });\n  return {\n    queue,\n    realtime,\n    storage,\n    streaming,\n    stream: streaming.stream,\n    async run<Id extends EndpointType>(\n      endpointId: Id,\n      options: RunOptions<InputType<Id>> = {},\n    ): Promise<Result<OutputType<Id>>> {\n      const input = options.input\n        ? await storage.transformInput(options.input)\n        : undefined;\n      return dispatchRequest<InputType<Id>, Result<OutputType<Id>>>({\n        method: options.method,\n        targetUrl: buildUrl(endpointId, options),\n        input: input as InputType<Id>,\n        config: {\n          ...config,\n          responseHandler: resultResponseHandler,\n        },\n        options: {\n          signal: options.abortSignal,\n        },\n      });\n    },\n    subscribe: async (endpointId, options) => {\n      const { request_id: requestId } = await queue.submit(endpointId, options);\n      if (options.onEnqueue) {\n        options.onEnqueue(requestId);\n      }\n      await queue.subscribeToStatus(endpointId, { requestId, ...options });\n      return queue.result(endpointId, { requestId });\n    },\n  };\n}\n"]}