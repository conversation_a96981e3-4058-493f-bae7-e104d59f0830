{"version": 3, "file": "config.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/config.ts"], "names": [], "mappings": ";;;AAaA,kDAOC;AAuFD,oCA4BC;AAKD,sCAEC;AA9ID,6CAIsB;AAEtB,yCAAoD;AACpD,uCAAsC;AAMtC,SAAgB,mBAAmB;IACjC,IAAI,OAAO,KAAK,KAAK,WAAW,EAAE,CAAC;QACjC,MAAM,IAAI,KAAK,CACb,wFAAwF,CACzF,CAAC;IACJ,CAAC;IACD,OAAO,KAAK,CAAC;AACf,CAAC;AA6CD;;;;;GAKG;AACH,SAAS,eAAe;IACtB,OAAO,CACL,OAAO,OAAO,KAAK,WAAW;QAC9B,OAAO,CAAC,GAAG;QACX,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,WAAW;YACzC,CAAC,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU,KAAK,WAAW;gBAC5C,OAAO,OAAO,CAAC,GAAG,CAAC,cAAc,KAAK,WAAW,CAAC,CAAC,CACxD,CAAC;AACJ,CAAC;AAEM,MAAM,kBAAkB,GAAwB,GAAG,EAAE;IAC1D,IAAI,CAAC,eAAe,EAAE,EAAE,CAAC;QACvB,OAAO,SAAS,CAAC;IACnB,CAAC;IAED,IAAI,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,KAAK,WAAW,EAAE,CAAC;QAC/C,OAAO,OAAO,CAAC,GAAG,CAAC,OAAO,CAAC;IAC7B,CAAC;IAED,OAAO,OAAO,CAAC,GAAG,CAAC,UAAU;QAC3B,CAAC,CAAC,GAAG,OAAO,CAAC,GAAG,CAAC,UAAU,IAAI,OAAO,CAAC,GAAG,CAAC,cAAc,EAAE;QAC3D,CAAC,CAAC,SAAS,CAAC;AAChB,CAAC,CAAC;AAZW,QAAA,kBAAkB,sBAY7B;AAEF,MAAM,cAAc,GAAoB;IACtC,WAAW,EAAE,0BAAkB;IAC/B,+BAA+B,EAAE,KAAK;IACtC,iBAAiB,EAAE,CAAC,OAAO,EAAE,EAAE,CAAC,OAAO,CAAC,OAAO,CAAC,OAAO,CAAC;IACxD,eAAe,EAAE,iCAAsB;CACxC,CAAC;AAEF;;;;GAIG;AACH,SAAgB,YAAY,CAAC,MAAc;;IACzC,IAAI,aAAa,GAAG,8CACf,cAAc,GACd,MAAM,KACT,KAAK,EAAE,MAAA,MAAM,CAAC,KAAK,mCAAI,mBAAmB,EAAE,GAC3B,CAAC;IACpB,IAAI,MAAM,CAAC,QAAQ,EAAE,CAAC;QACpB,aAAa,mCACR,aAAa,KAChB,iBAAiB,EAAE,IAAA,2BAAc,EAC/B,aAAa,CAAC,iBAAiB,EAC/B,IAAA,sBAAS,EAAC,EAAE,SAAS,EAAE,MAAM,CAAC,QAAQ,EAAE,CAAC,CAC1C,GACF,CAAC;IACJ,CAAC;IACD,MAAM,EAAE,WAAW,EAAE,kBAAkB,EAAE,+BAA+B,EAAE,GACxE,aAAa,CAAC;IAChB,MAAM,WAAW,GACf,OAAO,kBAAkB,KAAK,UAAU;QACtC,CAAC,CAAC,kBAAkB,EAAE;QACtB,CAAC,CAAC,kBAAkB,CAAC;IACzB,IAAI,IAAA,mBAAS,GAAE,IAAI,WAAW,IAAI,CAAC,+BAA+B,EAAE,CAAC;QACnE,OAAO,CAAC,IAAI,CACV,gEAAgE;YAC9D,kDAAkD,CACrD,CAAC;IACJ,CAAC;IACD,OAAO,aAAa,CAAC;AACvB,CAAC;AAED;;GAEG;AACH,SAAgB,aAAa;IAC3B,OAAO,2BAA2B,CAAC;AACrC,CAAC", "sourcesContent": ["import {\n  withMiddleware,\n  withProxy,\n  type RequestMiddleware,\n} from \"./middleware\";\nimport type { ResponseHandler } from \"./response\";\nimport { defaultResponseHandler } from \"./response\";\nimport { isBrowser } from \"./runtime\";\n\nexport type CredentialsResolver = () => string | undefined;\n\ntype FetchType = typeof fetch;\n\nexport function resolveDefaultFetch(): FetchType {\n  if (typeof fetch === \"undefined\") {\n    throw new Error(\n      \"Your environment does not support fetch. Please provide your own fetch implementation.\",\n    );\n  }\n  return fetch;\n}\n\nexport type Config = {\n  /**\n   * The credentials to use for the fal client. When using the\n   * client in the browser, it's recommended to use a proxy server to avoid\n   * exposing the credentials in the client's environment.\n   *\n   * By default it tries to use the `FAL_KEY` environment variable, when\n   * `process.env` is defined.\n   *\n   * @see https://fal.ai/docs/model-endpoints/server-side\n   * @see #suppressLocalCredentialsWarning\n   */\n  credentials?: undefined | string | CredentialsResolver;\n  /**\n   * Suppresses the warning when the fal credentials are exposed in the\n   * browser's environment. Make sure you understand the security implications\n   * before enabling this option.\n   */\n  suppressLocalCredentialsWarning?: boolean;\n  /**\n   * The URL of the proxy server to use for the client requests. The proxy\n   * server should forward the requests to the fal api.\n   */\n  proxyUrl?: string;\n  /**\n   * The request middleware to use for the client requests. By default it\n   * doesn't apply any middleware.\n   */\n  requestMiddleware?: RequestMiddleware;\n  /**\n   * The response handler to use for the client requests. By default it uses\n   * a built-in response handler that returns the JSON response.\n   */\n  responseHandler?: ResponseHandler<any>;\n  /**\n   * The fetch implementation to use for the client requests. By default it uses\n   * the global `fetch` function.\n   */\n  fetch?: FetchType;\n};\n\nexport type RequiredConfig = Required<Config>;\n\n/**\n * Checks if the required FAL environment variables are set.\n *\n * @returns `true` if the required environment variables are set,\n * `false` otherwise.\n */\nfunction hasEnvVariables(): boolean {\n  return (\n    typeof process !== \"undefined\" &&\n    process.env &&\n    (typeof process.env.FAL_KEY !== \"undefined\" ||\n      (typeof process.env.FAL_KEY_ID !== \"undefined\" &&\n        typeof process.env.FAL_KEY_SECRET !== \"undefined\"))\n  );\n}\n\nexport const credentialsFromEnv: CredentialsResolver = () => {\n  if (!hasEnvVariables()) {\n    return undefined;\n  }\n\n  if (typeof process.env.FAL_KEY !== \"undefined\") {\n    return process.env.FAL_KEY;\n  }\n\n  return process.env.FAL_KEY_ID\n    ? `${process.env.FAL_KEY_ID}:${process.env.FAL_KEY_SECRET}`\n    : undefined;\n};\n\nconst DEFAULT_CONFIG: Partial<Config> = {\n  credentials: credentialsFromEnv,\n  suppressLocalCredentialsWarning: false,\n  requestMiddleware: (request) => Promise.resolve(request),\n  responseHandler: defaultResponseHandler,\n};\n\n/**\n * Configures the fal client.\n *\n * @param config the new configuration.\n */\nexport function createConfig(config: Config): RequiredConfig {\n  let configuration = {\n    ...DEFAULT_CONFIG,\n    ...config,\n    fetch: config.fetch ?? resolveDefaultFetch(),\n  } as RequiredConfig;\n  if (config.proxyUrl) {\n    configuration = {\n      ...configuration,\n      requestMiddleware: withMiddleware(\n        configuration.requestMiddleware,\n        withProxy({ targetUrl: config.proxyUrl }),\n      ),\n    };\n  }\n  const { credentials: resolveCredentials, suppressLocalCredentialsWarning } =\n    configuration;\n  const credentials =\n    typeof resolveCredentials === \"function\"\n      ? resolveCredentials()\n      : resolveCredentials;\n  if (isBrowser() && credentials && !suppressLocalCredentialsWarning) {\n    console.warn(\n      \"The fal credentials are exposed in the browser's environment. \" +\n        \"That's not recommended for production use cases.\",\n    );\n  }\n  return configuration;\n}\n\n/**\n * @returns the URL of the fal REST api endpoint.\n */\nexport function getRestApiUrl(): string {\n  return \"https://rest.alpha.fal.ai\";\n}\n"]}