{"version": 3, "file": "index.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/index.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;AAAA,qCAA2D;AAM3D,mCAA2D;AAAlD,yGAAA,eAAe,OAAA;AACxB,2CAAyD;AAAhD,4GAAA,cAAc,OAAA;AAAE,uGAAA,SAAS,OAAA;AAIlC,uCAAuD;AAA9C,oGAAA,QAAQ,OAAA;AAAE,2GAAA,eAAe,OAAA;AAIlC,iDAA+B;AAM/B,iCAA0C;AAAjC,wGAAA,eAAe,OAAA;AAMxB;;;GAGG;AACU,QAAA,GAAG,GAAuB,CAAC,SAAS,wBAAwB;IACvE,IAAI,eAAe,GAAc,IAAA,wBAAe,GAAE,CAAC;IACnD,OAAO;QACL,MAAM,CAAC,MAAc;YACnB,eAAe,GAAG,IAAA,wBAAe,EAAC,MAAM,CAAC,CAAC;QAC5C,CAAC;QACD,IAAI,KAAK;YACP,OAAO,eAAe,CAAC,KAAK,CAAC;QAC/B,CAAC;QACD,IAAI,QAAQ;YACV,OAAO,eAAe,CAAC,QAAQ,CAAC;QAClC,CAAC;QACD,IAAI,OAAO;YACT,OAAO,eAAe,CAAC,OAAO,CAAC;QACjC,CAAC;QACD,IAAI,SAAS;YACX,OAAO,eAAe,CAAC,SAAS,CAAC;QACnC,CAAC;QACD,GAAG,CAA0B,EAAM,EAAE,OAAkC;YACrE,OAAO,eAAe,CAAC,GAAG,CAAK,EAAE,EAAE,OAAO,CAAC,CAAC;QAC9C,CAAC;QACD,SAAS,CACP,UAAc,EACd,OAAkC;YAElC,OAAO,eAAe,CAAC,SAAS,CAAK,UAAU,EAAE,OAAO,CAAC,CAAC;QAC5D,CAAC;QACD,MAAM,CACJ,UAAc,EACd,OAAqC;YAErC,OAAO,eAAe,CAAC,MAAM,CAAK,UAAU,EAAE,OAAO,CAAC,CAAC;QACzD,CAAC;KAC2B,CAAC;AACjC,CAAC,CAAC,EAAE,CAAC", "sourcesContent": ["import { createFalClient, type FalClient } from \"./client\";\nimport { Config } from \"./config\";\nimport { StreamOptions } from \"./streaming\";\nimport { EndpointType, InputType } from \"./types/client\";\nimport { RunOptions } from \"./types/common\";\n\nexport { createFalClient, type FalClient } from \"./client\";\nexport { withMiddleware, withProxy } from \"./middleware\";\nexport type { RequestMiddleware } from \"./middleware\";\nexport type { QueueClient } from \"./queue\";\nexport type { RealtimeClient } from \"./realtime\";\nexport { ApiError, ValidationError } from \"./response\";\nexport type { ResponseHandler } from \"./response\";\nexport type { StorageClient } from \"./storage\";\nexport type { FalStream, StreamingClient } from \"./streaming\";\nexport * from \"./types/common\";\nexport type {\n  QueueStatus,\n  ValidationErrorInfo,\n  WebHookResponse,\n} from \"./types/common\";\nexport { parseEndpointId } from \"./utils\";\n\ntype SingletonFalClient = {\n  config(config: Config): void;\n} & FalClient;\n\n/**\n * Creates a singleton instance of the client. This is useful as a compatibility\n * layer for existing code that uses the clients version prior to 1.0.0.\n */\nexport const fal: SingletonFalClient = (function createSingletonFalClient() {\n  let currentInstance: FalClient = createFalClient();\n  return {\n    config(config: Config) {\n      currentInstance = createFalClient(config);\n    },\n    get queue() {\n      return currentInstance.queue;\n    },\n    get realtime() {\n      return currentInstance.realtime;\n    },\n    get storage() {\n      return currentInstance.storage;\n    },\n    get streaming() {\n      return currentInstance.streaming;\n    },\n    run<Id extends EndpointType>(id: Id, options: RunOptions<InputType<Id>>) {\n      return currentInstance.run<Id>(id, options);\n    },\n    subscribe<Id extends EndpointType>(\n      endpointId: Id,\n      options: RunOptions<InputType<Id>>,\n    ) {\n      return currentInstance.subscribe<Id>(endpointId, options);\n    },\n    stream<Id extends EndpointType>(\n      endpointId: Id,\n      options: StreamOptions<InputType<Id>>,\n    ) {\n      return currentInstance.stream<Id>(endpointId, options);\n    },\n  } satisfies SingletonFalClient;\n})();\n"]}