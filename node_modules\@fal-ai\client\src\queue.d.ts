import { RequiredConfig } from "./config";
import { StorageClient } from "./storage";
import { FalStream, StreamingConnectionMode } from "./streaming";
import { EndpointType, InputType, OutputType } from "./types/client";
import { CompletedQueueStatus, InQueueQueueStatus, QueueStatus, Result, RunOptions } from "./types/common";
export type QueuePriority = "low" | "normal";
export type QueueStatusSubscriptionOptions = QueueStatusOptions & Omit<QueueSubscribeOptions, "onEnqueue" | "webhookUrl">;
/**
 * Options for subscribing to the request queue.
 */
export type QueueSubscribeOptions = {
    /**
     * The mode to use for subscribing to updates. It defaults to `polling`.
     * You can also use client-side streaming by setting it to `streaming`.
     *
     * **Note:** Streaming is currently experimental and once stable, it will
     * be the default mode.
     *
     * @see pollInterval
     */
    mode?: "polling" | "streaming";
    /**
     * Callback function that is called when a request is enqueued.
     * @param requestId - The unique identifier for the enqueued request.
     */
    onEnqueue?: (requestId: string) => void;
    /**
     * Callback function that is called when the status of the queue changes.
     * @param status - The current status of the queue.
     */
    onQueueUpdate?: (status: QueueStatus) => void;
    /**
     * If `true`, the response will include the logs for the request.
     * Defaults to `false`.
     */
    logs?: boolean;
    /**
     * The timeout (in milliseconds) for the request. If the request is not
     * completed within this time, the subscription will be cancelled.
     *
     * Keep in mind that although the client resolves the function on a timeout,
     * and will try to cancel the request on the server, the server might not be
     * able to cancel the request if it's already running.
     *
     * Note: currently, the timeout is not enforced and the default is `undefined`.
     * This behavior might change in the future.
     */
    timeout?: number;
    /**
     * The URL to send a webhook notification to when the request is completed.
     * @see WebHookResponse
     */
    webhookUrl?: string;
    /**
     * The priority of the request. It defaults to `normal`.
     * @see QueuePriority
     */
    priority?: QueuePriority;
} & ({
    mode?: "polling";
    /**
     * The interval (in milliseconds) at which to poll for updates.
     * If not provided, a default value of `500` will be used.
     *
     * This value is ignored if `mode` is set to `streaming`.
     */
    pollInterval?: number;
} | {
    mode: "streaming";
    /**
     * The connection mode to use for streaming updates. It defaults to `server`.
     * Set to `client` if your server proxy doesn't support streaming.
     */
    connectionMode?: StreamingConnectionMode;
});
/**
 * Options for submitting a request to the queue.
 */
export type SubmitOptions<Input> = RunOptions<Input> & {
    /**
     * The URL to send a webhook notification to when the request is completed.
     * @see WebHookResponse
     */
    webhookUrl?: string;
    /**
     * The priority of the request. It defaults to `normal`.
     * @see QueuePriority
     */
    priority?: QueuePriority;
};
type BaseQueueOptions = {
    /**
     * The unique identifier for the enqueued request.
     */
    requestId: string;
    /**
     * The signal to abort the request.
     */
    abortSignal?: AbortSignal;
};
export type QueueStatusOptions = BaseQueueOptions & {
    /**
     * If `true`, the response will include the logs for the request.
     * Defaults to `false`.
     */
    logs?: boolean;
};
export type QueueStatusStreamOptions = QueueStatusOptions & {
    /**
     * The connection mode to use for streaming updates. It defaults to `server`.
     * Set to `client` if your server proxy doesn't support streaming.
     */
    connectionMode?: StreamingConnectionMode;
};
/**
 * Represents a request queue with methods for submitting requests,
 * checking their status, retrieving results, and subscribing to updates.
 */
export interface QueueClient {
    /**
     * Submits a request to the queue.
     *
     * @param endpointId - The ID of the function web endpoint.
     * @param options - Options to configure how the request is run.
     * @returns A promise that resolves to the result of enqueuing the request.
     */
    submit<Id extends EndpointType>(endpointId: Id, options: SubmitOptions<InputType<Id>>): Promise<InQueueQueueStatus>;
    /**
     * Retrieves the status of a specific request in the queue.
     *
     * @param endpointId - The ID of the function web endpoint.
     * @param options - Options to configure how the request is run.
     * @returns A promise that resolves to the status of the request.
     */
    status(endpointId: string, options: QueueStatusOptions): Promise<QueueStatus>;
    /**
     * Subscribes to updates for a specific request in the queue using HTTP streaming events.
     *
     * @param endpointId - The ID of the function web endpoint.
     * @param options - Options to configure how the request is run and how updates are received.
     * @returns The streaming object that can be used to listen for updates.
     */
    streamStatus(endpointId: string, options: QueueStatusStreamOptions): Promise<FalStream<unknown, QueueStatus>>;
    /**
     * Subscribes to updates for a specific request in the queue using polling or streaming.
     * See `options.mode` for more details.
     *
     * @param endpointId - The ID of the function web endpoint.
     * @param options - Options to configure how the request is run and how updates are received.
     * @returns A promise that resolves to the final status of the request.
     */
    subscribeToStatus(endpointId: string, options: QueueStatusSubscriptionOptions): Promise<CompletedQueueStatus>;
    /**
     * Retrieves the result of a specific request from the queue.
     *
     * @param endpointId - The ID of the function web endpoint.
     * @param options - Options to configure how the request is run.
     * @returns A promise that resolves to the result of the request.
     */
    result<Id extends EndpointType>(endpointId: Id, options: BaseQueueOptions): Promise<Result<OutputType<Id>>>;
    /**
     * Cancels a request in the queue.
     *
     * @param endpointId - The ID of the function web endpoint.
     * @param options - Options to configure how the request
     * is run and how updates are received.
     * @returns A promise that resolves once the request is cancelled.
     * @throws {Error} If the request cannot be cancelled.
     */
    cancel(endpointId: string, options: BaseQueueOptions): Promise<void>;
}
type QueueClientDependencies = {
    config: RequiredConfig;
    storage: StorageClient;
};
export declare const createQueueClient: ({ config, storage, }: QueueClientDependencies) => QueueClient;
export {};
