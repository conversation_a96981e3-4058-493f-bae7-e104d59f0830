{"version": 3, "file": "queue.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/queue.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;AACA,uCAAsD;AACtD,yCAAmD;AAEnD,2CAAiE;AAUjE,mCAA0C;AAQ1C,MAAM,qBAAqB,GAAG,GAAG,CAAC;AA8M3B,MAAM,iBAAiB,GAAG,CAAC,EAChC,MAAM,EACN,OAAO,GACiB,EAAe,EAAE;IACzC,MAAM,GAAG,GAAgB;QACjB,MAAM,CACV,UAAkB,EAClB,OAA6B;;gBAE7B,MAAM,EAAE,UAAU,EAAE,QAAQ,KAAoB,OAAO,EAAtB,UAAU,UAAK,OAAO,EAAjD,0BAAuC,CAAU,CAAC;gBACxD,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK;oBACzB,CAAC,CAAC,MAAM,OAAO,CAAC,cAAc,CAAC,OAAO,CAAC,KAAK,CAAC;oBAC7C,CAAC,CAAC,SAAS,CAAC;gBACd,OAAO,IAAA,yBAAe,EAA4B;oBAChD,MAAM,EAAE,OAAO,CAAC,MAAM;oBACtB,SAAS,EAAE,IAAA,kBAAQ,EAAC,UAAU,kCACzB,UAAU,KACb,SAAS,EAAE,OAAO,EAClB,KAAK,EAAE,UAAU,CAAC,CAAC,CAAC,EAAE,WAAW,EAAE,UAAU,EAAE,CAAC,CAAC,CAAC,SAAS,IAC3D;oBACF,OAAO,EAAE;wBACP,sBAAsB,EAAE,QAAQ,aAAR,QAAQ,cAAR,QAAQ,GAAI,QAAQ;qBAC7C;oBACD,KAAK,EAAE,KAAc;oBACrB,MAAM;oBACN,OAAO,EAAE;wBACP,MAAM,EAAE,OAAO,CAAC,WAAW;qBAC5B;iBACF,CAAC,CAAC;YACL,CAAC;SAAA;QACK,MAAM;iEACV,UAAkB,EAClB,EAAE,SAAS,EAAE,IAAI,GAAG,KAAK,EAAE,WAAW,EAAsB;gBAE5D,MAAM,KAAK,GAAG,IAAA,uBAAe,EAAC,UAAU,CAAC,CAAC;gBAC1C,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5D,OAAO,IAAA,yBAAe,EAAuB;oBAC3C,MAAM,EAAE,KAAK;oBACb,SAAS,EAAE,IAAA,kBAAQ,EAAC,GAAG,MAAM,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;wBAC5D,SAAS,EAAE,OAAO;wBAClB,KAAK,EAAE,EAAE,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,EAAE;wBACjC,IAAI,EAAE,aAAa,SAAS,SAAS;qBACtC,CAAC;oBACF,MAAM;oBACN,OAAO,EAAE;wBACP,MAAM,EAAE,WAAW;qBACpB;iBACF,CAAC,CAAC;YACL,CAAC;SAAA;QAEK,YAAY;iEAChB,UAAkB,EAClB,EAAE,SAAS,EAAE,IAAI,GAAG,KAAK,EAAE,cAAc,EAA4B;gBAErE,MAAM,KAAK,GAAG,IAAA,uBAAe,EAAC,UAAU,CAAC,CAAC;gBAC1C,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAE5D,MAAM,WAAW,GAAG;oBAClB,IAAI,EAAE,IAAI,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG;iBACvB,CAAC;gBAEF,MAAM,GAAG,GAAG,IAAA,kBAAQ,EAAC,GAAG,MAAM,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;oBAC7D,SAAS,EAAE,OAAO;oBAClB,IAAI,EAAE,aAAa,SAAS,gBAAgB;oBAC5C,KAAK,EAAE,WAAW;iBACnB,CAAC,CAAC;gBAEH,OAAO,IAAI,qBAAS,CAAuB,UAAU,EAAE,MAAM,EAAE;oBAC7D,GAAG;oBACH,MAAM,EAAE,KAAK;oBACb,cAAc;oBACd,WAAW;iBACZ,CAAC,CAAC;YACL,CAAC;SAAA;QAEK,iBAAiB,CACrB,UAAU,EACV,OAAO;;gBAEP,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC;gBACpC,MAAM,OAAO,GAAG,OAAO,CAAC,OAAO,CAAC;gBAChC,IAAI,SAAS,GAAc,SAAS,CAAC;gBAErC,MAAM,iBAAiB,GAAG,GAAG,EAAE;oBAC7B,mEAAmE;oBACnE,mEAAmE;oBACnE,0EAA0E;gBAC5E,CAAC,CAAC;gBACF,IAAI,OAAO,CAAC,IAAI,KAAK,WAAW,EAAE,CAAC;oBACjC,MAAM,MAAM,GAAG,MAAM,GAAG,CAAC,YAAY,CAAC,UAAU,EAAE;wBAChD,SAAS;wBACT,IAAI,EAAE,OAAO,CAAC,IAAI;wBAClB,cAAc,EACZ,gBAAgB,IAAI,OAAO;4BACzB,CAAC,CAAE,OAAO,CAAC,cAA0C;4BACrD,CAAC,CAAC,SAAS;qBAChB,CAAC,CAAC;oBACH,MAAM,IAAI,GAAiB,EAAE,CAAC;oBAC9B,IAAI,OAAO,EAAE,CAAC;wBACZ,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;4BAC1B,MAAM,CAAC,KAAK,EAAE,CAAC;4BACf,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;4BAC/D,oEAAoE;4BACpE,4DAA4D;4BAC5D,kEAAkE;4BAClE,2CAA2C;4BAC3C,MAAM,IAAI,KAAK,CACb,8DAA8D,OAAO,IAAI,CAC1E,CAAC;wBACJ,CAAC,EAAE,OAAO,CAAC,CAAC;oBACd,CAAC;oBACD,MAAM,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAiB,EAAE,EAAE;wBACtC,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;4BAC1B,qDAAqD;4BACrD,IACE,MAAM,IAAI,IAAI;gCACd,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;gCACxB,IAAI,CAAC,IAAI,CAAC,MAAM,GAAG,CAAC,EACpB,CAAC;gCACD,IAAI,CAAC,IAAI,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC;4BAC1B,CAAC;4BACD,OAAO,CAAC,aAAa,CAAC,MAAM,IAAI,IAAI,CAAC,CAAC,iCAAM,IAAI,KAAE,IAAI,IAAG,CAAC,CAAC,IAAI,CAAC,CAAC;wBACnE,CAAC;oBACH,CAAC,CAAC,CAAC;oBACH,MAAM,UAAU,GAAG,MAAM,MAAM,CAAC,IAAI,EAAE,CAAC;oBACvC,IAAI,SAAS,EAAE,CAAC;wBACd,YAAY,CAAC,SAAS,CAAC,CAAC;oBAC1B,CAAC;oBACD,OAAO,UAAkC,CAAC;gBAC5C,CAAC;gBACD,iEAAiE;gBACjE,OAAO,IAAI,OAAO,CAAuB,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;;oBAC3D,IAAI,gBAA2B,CAAC;oBAChC,sEAAsE;oBACtE,sDAAsD;oBACtD,MAAM,YAAY,GAChB,cAAc,IAAI,OAAO,IAAI,OAAO,OAAO,CAAC,YAAY,KAAK,QAAQ;wBACnE,CAAC,CAAC,CAAC,MAAA,OAAO,CAAC,YAAY,mCAAI,qBAAqB,CAAC;wBACjD,CAAC,CAAC,qBAAqB,CAAC;oBAE5B,MAAM,mBAAmB,GAAG,GAAG,EAAE;wBAC/B,IAAI,SAAS,EAAE,CAAC;4BACd,YAAY,CAAC,SAAS,CAAC,CAAC;wBAC1B,CAAC;wBACD,IAAI,gBAAgB,EAAE,CAAC;4BACrB,YAAY,CAAC,gBAAgB,CAAC,CAAC;wBACjC,CAAC;oBACH,CAAC,CAAC;oBACF,IAAI,OAAO,EAAE,CAAC;wBACZ,SAAS,GAAG,UAAU,CAAC,GAAG,EAAE;4BAC1B,mBAAmB,EAAE,CAAC;4BACtB,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC,KAAK,CAAC,iBAAiB,CAAC,CAAC;4BAC/D,MAAM,CACJ,IAAI,KAAK,CACP,8DAA8D,OAAO,IAAI,CAC1E,CACF,CAAC;wBACJ,CAAC,EAAE,OAAO,CAAC,CAAC;oBACd,CAAC;oBACD,MAAM,IAAI,GAAG,GAAS,EAAE;;wBACtB,IAAI,CAAC;4BACH,MAAM,aAAa,GAAG,MAAM,GAAG,CAAC,MAAM,CAAC,UAAU,EAAE;gCACjD,SAAS;gCACT,IAAI,EAAE,MAAA,OAAO,CAAC,IAAI,mCAAI,KAAK;gCAC3B,WAAW,EAAE,OAAO,CAAC,WAAW;6BACjC,CAAC,CAAC;4BACH,IAAI,OAAO,CAAC,aAAa,EAAE,CAAC;gCAC1B,OAAO,CAAC,aAAa,CAAC,aAAa,CAAC,CAAC;4BACvC,CAAC;4BACD,IAAI,aAAa,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;gCACzC,mBAAmB,EAAE,CAAC;gCACtB,OAAO,CAAC,aAAa,CAAC,CAAC;gCACvB,OAAO;4BACT,CAAC;4BACD,gBAAgB,GAAG,UAAU,CAAC,IAAI,EAAE,YAAY,CAAC,CAAC;wBACpD,CAAC;wBAAC,OAAO,KAAK,EAAE,CAAC;4BACf,mBAAmB,EAAE,CAAC;4BACtB,MAAM,CAAC,KAAK,CAAC,CAAC;wBAChB,CAAC;oBACH,CAAC,CAAA,CAAC;oBACF,IAAI,EAAE,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC;gBACvB,CAAC,CAAC,CAAC;YACL,CAAC;SAAA;QAEK,MAAM;iEACV,UAAkB,EAClB,EAAE,SAAS,EAAE,WAAW,EAAoB;gBAE5C,MAAM,KAAK,GAAG,IAAA,uBAAe,EAAC,UAAU,CAAC,CAAC;gBAC1C,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5D,OAAO,IAAA,yBAAe,EAA0B;oBAC9C,MAAM,EAAE,KAAK;oBACb,SAAS,EAAE,IAAA,kBAAQ,EAAC,GAAG,MAAM,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;wBAC5D,SAAS,EAAE,OAAO;wBAClB,IAAI,EAAE,aAAa,SAAS,EAAE;qBAC/B,CAAC;oBACF,MAAM,kCACD,MAAM,KACT,eAAe,EAAE,gCAAqB,GACvC;oBACD,OAAO,EAAE;wBACP,MAAM,EAAE,WAAW;qBACpB;iBACF,CAAC,CAAC;YACL,CAAC;SAAA;QAEK,MAAM;iEACV,UAAkB,EAClB,EAAE,SAAS,EAAE,WAAW,EAAoB;gBAE5C,MAAM,KAAK,GAAG,IAAA,uBAAe,EAAC,UAAU,CAAC,CAAC;gBAC1C,MAAM,MAAM,GAAG,KAAK,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,KAAK,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;gBAC5D,MAAM,IAAA,yBAAe,EAAgB;oBACnC,MAAM,EAAE,KAAK;oBACb,SAAS,EAAE,IAAA,kBAAQ,EAAC,GAAG,MAAM,GAAG,KAAK,CAAC,KAAK,IAAI,KAAK,CAAC,KAAK,EAAE,EAAE;wBAC5D,SAAS,EAAE,OAAO;wBAClB,IAAI,EAAE,aAAa,SAAS,SAAS;qBACtC,CAAC;oBACF,MAAM;oBACN,OAAO,EAAE;wBACP,MAAM,EAAE,WAAW;qBACpB;iBACF,CAAC,CAAC;YACL,CAAC;SAAA;KACF,CAAC;IACF,OAAO,GAAG,CAAC;AACb,CAAC,CAAC;AAlOW,QAAA,iBAAiB,qBAkO5B", "sourcesContent": ["import { RequiredConfig } from \"./config\";\nimport { buildUrl, dispatchRequest } from \"./request\";\nimport { resultResponse<PERSON>and<PERSON> } from \"./response\";\nimport { StorageClient } from \"./storage\";\nimport { FalStream, StreamingConnectionMode } from \"./streaming\";\nimport { EndpointType, InputType, OutputType } from \"./types/client\";\nimport {\n  CompletedQueueStatus,\n  InQueueQueueStatus,\n  QueueStatus,\n  RequestLog,\n  Result,\n  RunOptions,\n} from \"./types/common\";\nimport { parseEndpointId } from \"./utils\";\n\nexport type QueuePriority = \"low\" | \"normal\";\nexport type QueueStatusSubscriptionOptions = QueueStatusOptions &\n  Omit<QueueSubscribeOptions, \"onEnqueue\" | \"webhookUrl\">;\n\ntype TimeoutId = ReturnType<typeof setTimeout> | undefined;\n\nconst DEFAULT_POLL_INTERVAL = 500;\n\n/**\n * Options for subscribing to the request queue.\n */\nexport type QueueSubscribeOptions = {\n  /**\n   * The mode to use for subscribing to updates. It defaults to `polling`.\n   * You can also use client-side streaming by setting it to `streaming`.\n   *\n   * **Note:** Streaming is currently experimental and once stable, it will\n   * be the default mode.\n   *\n   * @see pollInterval\n   */\n  mode?: \"polling\" | \"streaming\";\n\n  /**\n   * Callback function that is called when a request is enqueued.\n   * @param requestId - The unique identifier for the enqueued request.\n   */\n  onEnqueue?: (requestId: string) => void;\n\n  /**\n   * Callback function that is called when the status of the queue changes.\n   * @param status - The current status of the queue.\n   */\n  onQueueUpdate?: (status: QueueStatus) => void;\n\n  /**\n   * If `true`, the response will include the logs for the request.\n   * Defaults to `false`.\n   */\n  logs?: boolean;\n\n  /**\n   * The timeout (in milliseconds) for the request. If the request is not\n   * completed within this time, the subscription will be cancelled.\n   *\n   * Keep in mind that although the client resolves the function on a timeout,\n   * and will try to cancel the request on the server, the server might not be\n   * able to cancel the request if it's already running.\n   *\n   * Note: currently, the timeout is not enforced and the default is `undefined`.\n   * This behavior might change in the future.\n   */\n  timeout?: number;\n\n  /**\n   * The URL to send a webhook notification to when the request is completed.\n   * @see WebHookResponse\n   */\n  webhookUrl?: string;\n\n  /**\n   * The priority of the request. It defaults to `normal`.\n   * @see QueuePriority\n   */\n  priority?: QueuePriority;\n} & (\n  | {\n      mode?: \"polling\";\n      /**\n       * The interval (in milliseconds) at which to poll for updates.\n       * If not provided, a default value of `500` will be used.\n       *\n       * This value is ignored if `mode` is set to `streaming`.\n       */\n      pollInterval?: number;\n    }\n  | {\n      mode: \"streaming\";\n\n      /**\n       * The connection mode to use for streaming updates. It defaults to `server`.\n       * Set to `client` if your server proxy doesn't support streaming.\n       */\n      connectionMode?: StreamingConnectionMode;\n    }\n);\n\n/**\n * Options for submitting a request to the queue.\n */\nexport type SubmitOptions<Input> = RunOptions<Input> & {\n  /**\n   * The URL to send a webhook notification to when the request is completed.\n   * @see WebHookResponse\n   */\n  webhookUrl?: string;\n\n  /**\n   * The priority of the request. It defaults to `normal`.\n   * @see QueuePriority\n   */\n  priority?: QueuePriority;\n};\n\ntype BaseQueueOptions = {\n  /**\n   * The unique identifier for the enqueued request.\n   */\n  requestId: string;\n\n  /**\n   * The signal to abort the request.\n   */\n  abortSignal?: AbortSignal;\n};\n\nexport type QueueStatusOptions = BaseQueueOptions & {\n  /**\n   * If `true`, the response will include the logs for the request.\n   * Defaults to `false`.\n   */\n  logs?: boolean;\n};\n\nexport type QueueStatusStreamOptions = QueueStatusOptions & {\n  /**\n   * The connection mode to use for streaming updates. It defaults to `server`.\n   * Set to `client` if your server proxy doesn't support streaming.\n   */\n  connectionMode?: StreamingConnectionMode;\n};\n\n/**\n * Represents a request queue with methods for submitting requests,\n * checking their status, retrieving results, and subscribing to updates.\n */\nexport interface QueueClient {\n  /**\n   * Submits a request to the queue.\n   *\n   * @param endpointId - The ID of the function web endpoint.\n   * @param options - Options to configure how the request is run.\n   * @returns A promise that resolves to the result of enqueuing the request.\n   */\n  submit<Id extends EndpointType>(\n    endpointId: Id,\n    options: SubmitOptions<InputType<Id>>,\n  ): Promise<InQueueQueueStatus>;\n\n  /**\n   * Retrieves the status of a specific request in the queue.\n   *\n   * @param endpointId - The ID of the function web endpoint.\n   * @param options - Options to configure how the request is run.\n   * @returns A promise that resolves to the status of the request.\n   */\n  status(endpointId: string, options: QueueStatusOptions): Promise<QueueStatus>;\n\n  /**\n   * Subscribes to updates for a specific request in the queue using HTTP streaming events.\n   *\n   * @param endpointId - The ID of the function web endpoint.\n   * @param options - Options to configure how the request is run and how updates are received.\n   * @returns The streaming object that can be used to listen for updates.\n   */\n  streamStatus(\n    endpointId: string,\n    options: QueueStatusStreamOptions,\n  ): Promise<FalStream<unknown, QueueStatus>>;\n\n  /**\n   * Subscribes to updates for a specific request in the queue using polling or streaming.\n   * See `options.mode` for more details.\n   *\n   * @param endpointId - The ID of the function web endpoint.\n   * @param options - Options to configure how the request is run and how updates are received.\n   * @returns A promise that resolves to the final status of the request.\n   */\n  subscribeToStatus(\n    endpointId: string,\n    options: QueueStatusSubscriptionOptions,\n  ): Promise<CompletedQueueStatus>;\n\n  /**\n   * Retrieves the result of a specific request from the queue.\n   *\n   * @param endpointId - The ID of the function web endpoint.\n   * @param options - Options to configure how the request is run.\n   * @returns A promise that resolves to the result of the request.\n   */\n  result<Id extends EndpointType>(\n    endpointId: Id,\n    options: BaseQueueOptions,\n  ): Promise<Result<OutputType<Id>>>;\n\n  /**\n   * Cancels a request in the queue.\n   *\n   * @param endpointId - The ID of the function web endpoint.\n   * @param options - Options to configure how the request\n   * is run and how updates are received.\n   * @returns A promise that resolves once the request is cancelled.\n   * @throws {Error} If the request cannot be cancelled.\n   */\n  cancel(endpointId: string, options: BaseQueueOptions): Promise<void>;\n}\n\ntype QueueClientDependencies = {\n  config: RequiredConfig;\n  storage: StorageClient;\n};\n\nexport const createQueueClient = ({\n  config,\n  storage,\n}: QueueClientDependencies): QueueClient => {\n  const ref: QueueClient = {\n    async submit<Input>(\n      endpointId: string,\n      options: SubmitOptions<Input>,\n    ): Promise<InQueueQueueStatus> {\n      const { webhookUrl, priority, ...runOptions } = options;\n      const input = options.input\n        ? await storage.transformInput(options.input)\n        : undefined;\n      return dispatchRequest<Input, InQueueQueueStatus>({\n        method: options.method,\n        targetUrl: buildUrl(endpointId, {\n          ...runOptions,\n          subdomain: \"queue\",\n          query: webhookUrl ? { fal_webhook: webhookUrl } : undefined,\n        }),\n        headers: {\n          \"x-fal-queue-priority\": priority ?? \"normal\",\n        },\n        input: input as Input,\n        config,\n        options: {\n          signal: options.abortSignal,\n        },\n      });\n    },\n    async status(\n      endpointId: string,\n      { requestId, logs = false, abortSignal }: QueueStatusOptions,\n    ): Promise<QueueStatus> {\n      const appId = parseEndpointId(endpointId);\n      const prefix = appId.namespace ? `${appId.namespace}/` : \"\";\n      return dispatchRequest<unknown, QueueStatus>({\n        method: \"get\",\n        targetUrl: buildUrl(`${prefix}${appId.owner}/${appId.alias}`, {\n          subdomain: \"queue\",\n          query: { logs: logs ? \"1\" : \"0\" },\n          path: `/requests/${requestId}/status`,\n        }),\n        config,\n        options: {\n          signal: abortSignal,\n        },\n      });\n    },\n\n    async streamStatus(\n      endpointId: string,\n      { requestId, logs = false, connectionMode }: QueueStatusStreamOptions,\n    ): Promise<FalStream<unknown, QueueStatus>> {\n      const appId = parseEndpointId(endpointId);\n      const prefix = appId.namespace ? `${appId.namespace}/` : \"\";\n\n      const queryParams = {\n        logs: logs ? \"1\" : \"0\",\n      };\n\n      const url = buildUrl(`${prefix}${appId.owner}/${appId.alias}`, {\n        subdomain: \"queue\",\n        path: `/requests/${requestId}/status/stream`,\n        query: queryParams,\n      });\n\n      return new FalStream<unknown, QueueStatus>(endpointId, config, {\n        url,\n        method: \"get\",\n        connectionMode,\n        queryParams,\n      });\n    },\n\n    async subscribeToStatus(\n      endpointId,\n      options,\n    ): Promise<CompletedQueueStatus> {\n      const requestId = options.requestId;\n      const timeout = options.timeout;\n      let timeoutId: TimeoutId = undefined;\n\n      const handleCancelError = () => {\n        // Ignore errors as the client will follow through with the timeout\n        // regardless of the server response. In case cancelation fails, we\n        // still want to reject the promise and consider the client call canceled.\n      };\n      if (options.mode === \"streaming\") {\n        const status = await ref.streamStatus(endpointId, {\n          requestId,\n          logs: options.logs,\n          connectionMode:\n            \"connectionMode\" in options\n              ? (options.connectionMode as StreamingConnectionMode)\n              : undefined,\n        });\n        const logs: RequestLog[] = [];\n        if (timeout) {\n          timeoutId = setTimeout(() => {\n            status.abort();\n            ref.cancel(endpointId, { requestId }).catch(handleCancelError);\n            // TODO this error cannot bubble up to the user since it's thrown in\n            // a closure in the global scope due to setTimeout behavior.\n            // User will get a platform error instead. We should find a way to\n            // make this behavior aligned with polling.\n            throw new Error(\n              `Client timed out waiting for the request to complete after ${timeout}ms`,\n            );\n          }, timeout);\n        }\n        status.on(\"data\", (data: QueueStatus) => {\n          if (options.onQueueUpdate) {\n            // accumulate logs to match previous polling behavior\n            if (\n              \"logs\" in data &&\n              Array.isArray(data.logs) &&\n              data.logs.length > 0\n            ) {\n              logs.push(...data.logs);\n            }\n            options.onQueueUpdate(\"logs\" in data ? { ...data, logs } : data);\n          }\n        });\n        const doneStatus = await status.done();\n        if (timeoutId) {\n          clearTimeout(timeoutId);\n        }\n        return doneStatus as CompletedQueueStatus;\n      }\n      // default to polling until status streaming is stable and faster\n      return new Promise<CompletedQueueStatus>((resolve, reject) => {\n        let pollingTimeoutId: TimeoutId;\n        // type resolution isn't great in this case, so check for its presence\n        // and and type so the typechecker behaves as expected\n        const pollInterval =\n          \"pollInterval\" in options && typeof options.pollInterval === \"number\"\n            ? (options.pollInterval ?? DEFAULT_POLL_INTERVAL)\n            : DEFAULT_POLL_INTERVAL;\n\n        const clearScheduledTasks = () => {\n          if (timeoutId) {\n            clearTimeout(timeoutId);\n          }\n          if (pollingTimeoutId) {\n            clearTimeout(pollingTimeoutId);\n          }\n        };\n        if (timeout) {\n          timeoutId = setTimeout(() => {\n            clearScheduledTasks();\n            ref.cancel(endpointId, { requestId }).catch(handleCancelError);\n            reject(\n              new Error(\n                `Client timed out waiting for the request to complete after ${timeout}ms`,\n              ),\n            );\n          }, timeout);\n        }\n        const poll = async () => {\n          try {\n            const requestStatus = await ref.status(endpointId, {\n              requestId,\n              logs: options.logs ?? false,\n              abortSignal: options.abortSignal,\n            });\n            if (options.onQueueUpdate) {\n              options.onQueueUpdate(requestStatus);\n            }\n            if (requestStatus.status === \"COMPLETED\") {\n              clearScheduledTasks();\n              resolve(requestStatus);\n              return;\n            }\n            pollingTimeoutId = setTimeout(poll, pollInterval);\n          } catch (error) {\n            clearScheduledTasks();\n            reject(error);\n          }\n        };\n        poll().catch(reject);\n      });\n    },\n\n    async result<Output>(\n      endpointId: string,\n      { requestId, abortSignal }: BaseQueueOptions,\n    ): Promise<Result<Output>> {\n      const appId = parseEndpointId(endpointId);\n      const prefix = appId.namespace ? `${appId.namespace}/` : \"\";\n      return dispatchRequest<unknown, Result<Output>>({\n        method: \"get\",\n        targetUrl: buildUrl(`${prefix}${appId.owner}/${appId.alias}`, {\n          subdomain: \"queue\",\n          path: `/requests/${requestId}`,\n        }),\n        config: {\n          ...config,\n          responseHandler: resultResponseHandler,\n        },\n        options: {\n          signal: abortSignal,\n        },\n      });\n    },\n\n    async cancel(\n      endpointId: string,\n      { requestId, abortSignal }: BaseQueueOptions,\n    ): Promise<void> {\n      const appId = parseEndpointId(endpointId);\n      const prefix = appId.namespace ? `${appId.namespace}/` : \"\";\n      await dispatchRequest<unknown, void>({\n        method: \"put\",\n        targetUrl: buildUrl(`${prefix}${appId.owner}/${appId.alias}`, {\n          subdomain: \"queue\",\n          path: `/requests/${requestId}/cancel`,\n        }),\n        config,\n        options: {\n          signal: abortSignal,\n        },\n      });\n    },\n  };\n  return ref;\n};\n"]}