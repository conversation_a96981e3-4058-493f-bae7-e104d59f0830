import { RequiredConfig } from "./config";
import { ApiError } from "./response";
type WithRequestId = {
    request_id: string;
};
/**
 * A connection object that allows you to `send` request payloads to a
 * realtime endpoint.
 */
export interface RealtimeConnection<Input> {
    send(input: Input & Partial<WithRequestId>): void;
    close(): void;
}
/**
 * Options for connecting to the realtime endpoint.
 */
export interface RealtimeConnectionHandler<Output> {
    /**
     * The connection key. This is used to reuse the same connection
     * across multiple calls to `connect`. This is particularly useful in
     * contexts where the connection is established as part of a component
     * lifecycle (e.g. React) and the component is re-rendered multiple times.
     */
    connectionKey?: string;
    /**
     * If `true`, the connection will only be established on the client side.
     * This is useful for frameworks that reuse code for both server-side
     * rendering and client-side rendering (e.g. Next.js).
     *
     * This is set to `true` by default when running on React in the server.
     * Otherwise, it is set to `false`.
     *
     * Note that more SSR frameworks might be automatically detected
     * in the future. In the meantime, you can set this to `true` when needed.
     */
    clientOnly?: boolean;
    /**
     * The throtle duration in milliseconds. This is used to throtle the
     * calls to the `send` function. Realtime apps usually react to user
     * input, which can be very frequent (e.g. fast typing or mouse/drag movements).
     *
     * The default value is `128` milliseconds.
     */
    throttleInterval?: number;
    /**
     * Configures the maximum amount of frames to store in memory before starting to drop
     * old ones for in favor of the newer ones. It must be between `1` and `60`.
     *
     * The recommended is `2`. The default is `undefined` so it can be determined
     * by the app (normally is set to the recommended setting).
     */
    maxBuffering?: number;
    /**
     * Callback function that is called when a result is received.
     * @param result - The result of the request.
     */
    onResult(result: Output & WithRequestId): void;
    /**
     * Callback function that is called when an error occurs.
     * @param error - The error that occurred.
     */
    onError?(error: ApiError<any>): void;
}
export interface RealtimeClient {
    /**
     * Connect to the realtime endpoint. The default implementation uses
     * WebSockets to connect to fal function endpoints that support WSS.
     *
     * @param app the app alias or identifier.
     * @param handler the connection handler.
     */
    connect<Input = any, Output = any>(app: string, handler: RealtimeConnectionHandler<Output>): RealtimeConnection<Input>;
}
type RealtimeClientDependencies = {
    config: RequiredConfig;
};
export declare function createRealtimeClient({ config, }: RealtimeClientDependencies): RealtimeClient;
export {};
