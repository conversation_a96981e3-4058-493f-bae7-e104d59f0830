{"version": 3, "file": "realtime.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/realtime.ts"], "names": [], "mappings": ";;AAwWA,oDAyKC;AAjhBD,uDAAuD;AACvD,8CAAkD;AAClD,mCAWgB;AAChB,iCAAyE;AAEzE,yCAAsC;AACtC,uCAAsC;AACtC,mCAAoE;AAUpE,MAAM,YAAY,GAA6B,GAAG,EAAE,CAAC,CAAC;IACpD,eAAe,EAAE,SAAS;CAC3B,CAAC,CAAC;AAqBH,SAAS,QAAQ,CAAC,OAAgB;IAChC,OAAO,OAAO,CAAC,KAAK,KAAK,SAAS,CAAC;AACrC,CAAC;AAED,SAAS,OAAO,CAAC,OAAgB;IAC/B,OAAO,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;AAC5B,CAAC;AAED,SAAS,cAAc,CAAC,OAAgB,EAAE,KAAgB;IACxD,uCACK,OAAO,KACV,eAAe,EAAE,KAAK,CAAC,OAAO,IAC9B;AACJ,CAAC;AAED,SAAS,eAAe,CAAC,OAAgB;IACvC,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;QACzE,OAAO,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IAC5B,CAAC;IACD,uCACK,OAAO,KACV,SAAS,EAAE,SAAS,IACpB;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,OAAgB,EAAE,KAAgB;IACrD,IAAI,OAAO,CAAC,SAAS,IAAI,OAAO,CAAC,SAAS,CAAC,UAAU,KAAK,SAAS,CAAC,IAAI,EAAE,CAAC;QACzE,IAAI,KAAK,CAAC,OAAO,YAAY,UAAU,EAAE,CAAC;YACxC,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,OAAO,CAAC,SAAS,CAAC,IAAI,CAAC,IAAA,gBAAM,EAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC;QAChD,CAAC;QAED,uCACK,OAAO,KACV,eAAe,EAAE,SAAS,IAC1B;IACJ,CAAC;IACD,uCACK,OAAO,KACV,eAAe,EAAE,KAAK,CAAC,OAAO,IAC9B;AACJ,CAAC;AAED,SAAS,WAAW,CAAC,OAAgB;IACnC,uCACK,OAAO,KACV,KAAK,EAAE,SAAS,IAChB;AACJ,CAAC;AAED,SAAS,QAAQ,CAAC,OAAgB,EAAE,KAAyB;IAC3D,uCACK,OAAO,KACV,KAAK,EAAE,KAAK,CAAC,KAAK,IAClB;AACJ,CAAC;AAED,SAAS,qBAAqB,CAC5B,OAAgB,EAChB,KAAqB;IAErB,uCACK,OAAO,KACV,SAAS,EAAE,KAAK,CAAC,SAAS,IAC1B;AACJ,CAAC;AAED,gBAAgB;AAChB,MAAM,sBAAsB,GAAG,IAAA,sBAAa,EAC1C,MAAM,EACN;IACE,IAAI,EAAE,IAAA,cAAK,EACT,IAAA,mBAAU,EAAC,MAAM,EAAE,YAAY,EAAE,IAAA,eAAM,EAAC,cAAc,CAAC,CAAC,EACxD,IAAA,mBAAU,EAAC,aAAa,EAAE,MAAM,EAAE,IAAA,eAAM,EAAC,WAAW,CAAC,CAAC,EACtD,IAAA,mBAAU,EAAC,OAAO,EAAE,MAAM,EAAE,IAAA,eAAM,EAAC,eAAe,CAAC,CAAC,CACrD;IACD,UAAU,EAAE,IAAA,cAAK,EACf,IAAA,mBAAU,EAAC,YAAY,EAAE,YAAY,CAAC,EACtC,IAAA,mBAAU,EAAC,WAAW,EAAE,QAAQ,EAAE,IAAA,eAAM,EAAC,qBAAqB,CAAC,CAAC,EAChE,IAAA,mBAAU,EAAC,kBAAkB,EAAE,MAAM,EAAE,IAAA,eAAM,EAAC,eAAe,CAAC,CAAC,EAC/D,IAAA,mBAAU,EAAC,MAAM,EAAE,YAAY,EAAE,IAAA,eAAM,EAAC,cAAc,CAAC,CAAC,EACxD,IAAA,mBAAU,EAAC,OAAO,EAAE,MAAM,EAAE,IAAA,eAAM,EAAC,eAAe,CAAC,CAAC,EACpD,IAAA,kBAAS,EAAC,cAAc,EAAE,IAAA,cAAK,EAAC,OAAO,CAAC,CAAC,CAC1C;IACD,YAAY,EAAE,IAAA,cAAK,EACjB,IAAA,mBAAU,EAAC,cAAc,EAAE,gBAAgB,CAAC,EAC5C,IAAA,mBAAU,EAAC,MAAM,EAAE,cAAc,EAAE,IAAA,eAAM,EAAC,cAAc,CAAC,CAAC,EAC1D,IAAA,mBAAU,EAAC,OAAO,EAAE,MAAM,EAAE,IAAA,eAAM,EAAC,eAAe,CAAC,CAAC,CACrD;IACD,cAAc,EAAE,IAAA,cAAK,EACnB,IAAA,mBAAU,EAAC,eAAe,EAAE,YAAY,EAAE,IAAA,eAAM,EAAC,QAAQ,CAAC,CAAC,EAC3D,IAAA,mBAAU,EACR,cAAc,EACd,MAAM,EACN,IAAA,eAAM,EAAC,WAAW,CAAC,EACnB,IAAA,eAAM,EAAC,eAAe,CAAC,CACxB,EACD,IAAA,mBAAU,EAAC,MAAM,EAAE,gBAAgB,EAAE,IAAA,eAAM,EAAC,cAAc,CAAC,CAAC,EAC5D,IAAA,mBAAU,EAAC,OAAO,EAAE,MAAM,EAAE,IAAA,eAAM,EAAC,eAAe,CAAC,CAAC,CACrD;IACD,MAAM,EAAE,IAAA,cAAK,EACX,IAAA,mBAAU,EAAC,MAAM,EAAE,QAAQ,EAAE,IAAA,eAAM,EAAC,WAAW,CAAC,CAAC,EACjD,IAAA,mBAAU,EAAC,cAAc,EAAE,MAAM,EAAE,IAAA,eAAM,EAAC,WAAW,CAAC,CAAC,EACvD,IAAA,mBAAU,EAAC,kBAAkB,EAAE,MAAM,EAAE,IAAA,eAAM,EAAC,eAAe,CAAC,CAAC,EAC/D,IAAA,mBAAU,EAAC,OAAO,EAAE,MAAM,EAAE,IAAA,eAAM,EAAC,eAAe,CAAC,CAAC,CACrD;IACD,MAAM,EAAE,IAAA,cAAK,EACX,IAAA,mBAAU,EAAC,MAAM,EAAE,QAAQ,CAAC,EAC5B,IAAA,mBAAU,EAAC,OAAO,EAAE,MAAM,EAAE,IAAA,eAAM,EAAC,eAAe,CAAC,CAAC,CACrD;CACF,EACD,YAAY,CACb,CAAC;AA2FF,SAAS,gBAAgB,CACvB,GAAW,EACX,EAAE,KAAK,EAAE,YAAY,EAAqB;IAE1C,IAAI,YAAY,KAAK,SAAS,IAAI,CAAC,YAAY,GAAG,CAAC,IAAI,YAAY,GAAG,EAAE,CAAC,EAAE,CAAC;QAC1E,MAAM,IAAI,KAAK,CAAC,yDAAyD,CAAC,CAAC;IAC7E,CAAC;IACD,MAAM,WAAW,GAAG,IAAI,eAAe,CAAC;QACtC,aAAa,EAAE,KAAK;KACrB,CAAC,CAAC;IACH,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;QAC/B,WAAW,CAAC,GAAG,CAAC,eAAe,EAAE,YAAY,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;IAC5D,CAAC;IACD,MAAM,KAAK,GAAG,IAAA,8BAAsB,EAAC,GAAG,CAAC,CAAC;IAC1C,OAAO,iBAAiB,KAAK,aAAa,WAAW,CAAC,QAAQ,EAAE,EAAE,CAAC;AACrE,CAAC;AAED,MAAM,yBAAyB,GAAG,GAAG,CAAC;AAEtC,SAAS,mBAAmB,CAAC,OAAY;IACvC,2DAA2D;IAC3D,OAAO,OAAO,CAAC,QAAQ,CAAC,KAAK,OAAO,IAAI,OAAO,CAAC,OAAO,CAAC,KAAK,cAAc,CAAC;AAC9E,CAAC;AAED;;GAEG;AACH,MAAM,mBAAmB,GAAG;IAC1B,cAAc,EAAE,IAAI;IACpB,UAAU,EAAE,IAAI;CACjB,CAAC;AAkBF,MAAM,eAAe,GAAG,IAAI,GAAG,EAAkC,CAAC;AAClE,MAAM,mBAAmB,GAAG,IAAI,GAAG,EAAsC,CAAC;AAC1E,SAAS,gBAAgB,CACvB,GAAW,EACX,gBAAwB,EACxB,QAA4B;IAE5B,IAAI,CAAC,eAAe,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;QAC9B,MAAM,OAAO,GAAG,IAAA,kBAAS,EAAC,sBAAsB,EAAE,QAAQ,CAAC,CAAC;QAC5D,eAAe,CAAC,GAAG,CAAC,GAAG,kCAClB,OAAO,KACV,aAAa,EACX,gBAAgB,GAAG,CAAC;gBAClB,CAAC,CAAC,IAAA,gBAAQ,EAAC,OAAO,CAAC,IAAI,EAAE,gBAAgB,EAAE,IAAI,CAAC;gBAChD,CAAC,CAAC,OAAO,CAAC,IAAI,IAClB,CAAC;IACL,CAAC;IACD,OAAO,eAAe,CAAC,GAAG,CAAC,GAAG,CAA2B,CAAC;AAC5D,CAAC;AAED,MAAM,IAAI,GAAG,GAAG,EAAE;IAChB,WAAW;AACb,CAAC,CAAC;AAEF;;;;GAIG;AACH,8DAA8D;AAC9D,MAAM,cAAc,GAA4B;IAC9C,IAAI,EAAE,IAAI;IACV,KAAK,EAAE,IAAI;CACZ,CAAC;AAEF,SAAS,kBAAkB,CAAC,IAAS;IACnC,OAAO,CACL,IAAI,CAAC,MAAM,KAAK,OAAO;QACvB,IAAI,CAAC,IAAI,KAAK,eAAe;QAC7B,CAAC,gBAAgB,CAAC,IAAI,CAAC,CACxB,CAAC;AACJ,CAAC;AAQD,SAAS,gBAAgB,CAAC,IAAS;IACjC,OAAO,IAAI,CAAC,IAAI,KAAK,aAAa,CAAC;AACrC,CAAC;AAMD,SAAgB,oBAAoB,CAAC,EACnC,MAAM,GACqB;IAC3B,OAAO;QACL,OAAO,CACL,GAAW,EACX,OAA0C;YAE1C,MAAM;YACJ,uEAAuE;YACvE,UAAU,GAAG,IAAA,eAAO,GAAE,IAAI,CAAC,IAAA,mBAAS,GAAE,EACtC,aAAa,GAAG,MAAM,CAAC,UAAU,EAAE,EACnC,YAAY,EACZ,gBAAgB,GAAG,yBAAyB,GAC7C,GAAG,OAAO,CAAC;YACZ,IAAI,UAAU,IAAI,CAAC,IAAA,mBAAS,GAAE,EAAE,CAAC;gBAC/B,OAAO,cAAc,CAAC;YACxB,CAAC;YAED,IAAI,aAAiC,CAAC;YAEtC,8EAA8E;YAC9E,8EAA8E;YAC9E,yEAAyE;YACzE,0EAA0E;YAC1E,gCAAgC;YAChC,mBAAmB,CAAC,GAAG,CAAC,aAAa,EAAE;gBACrC,OAAO,EAAE,OAAO,CAAC,OAAO;gBACxB,QAAQ,EAAE,OAAO,CAAC,QAAQ;aAC3B,CAAC,CAAC;YACH,MAAM,YAAY,GAAG,GAAG,EAAE,CACxB,mBAAmB,CAAC,GAAG,CAAC,aAAa,CAA+B,CAAC;YACvE,MAAM,YAAY,GAAG,gBAAgB,CACnC,aAAa,EACb,gBAAgB,EAChB,CAAC,EAAE,OAAO,EAAE,OAAO,EAAE,IAAI,EAAE,EAAE,EAAE;gBAC7B,MAAM,EAAE,eAAe,EAAE,KAAK,EAAE,GAAG,OAAO,CAAC;gBAC3C,IAAI,OAAO,CAAC,OAAO,KAAK,QAAQ,IAAI,eAAe,EAAE,CAAC;oBACpD,IAAI,CAAC,EAAE,IAAI,EAAE,MAAM,EAAE,OAAO,EAAE,eAAe,EAAE,CAAC,CAAC;gBACnD,CAAC;gBACD,IACE,OAAO,CAAC,OAAO,KAAK,cAAc;oBAClC,KAAK,KAAK,SAAS;oBACnB,aAAa,KAAK,OAAO,CAAC,OAAO,EACjC,CAAC;oBACD,IAAI,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,CAAC,CAAC;oBAC/B,IAAA,4BAAqB,EAAC,GAAG,EAAE,MAAM,CAAC;yBAC/B,IAAI,CAAC,CAAC,KAAK,EAAE,EAAE;wBACd,IAAI,CAAC,EAAE,IAAI,EAAE,eAAe,EAAE,KAAK,EAAE,CAAC,CAAC;wBACvC,MAAM,sBAAsB,GAAG,IAAI,CAAC,KAAK,CACvC,+BAAwB,GAAG,GAAG,GAAG,IAAI,CACtC,CAAC;wBACF,UAAU,CAAC,GAAG,EAAE;4BACd,IAAI,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,CAAC,CAAC;wBAChC,CAAC,EAAE,sBAAsB,CAAC,CAAC;oBAC7B,CAAC,CAAC;yBACD,KAAK,CAAC,CAAC,KAAK,EAAE,EAAE;wBACf,IAAI,CAAC,EAAE,IAAI,EAAE,cAAc,EAAE,KAAK,EAAE,CAAC,CAAC;oBACxC,CAAC,CAAC,CAAC;gBACP,CAAC;gBACD,IACE,OAAO,CAAC,OAAO,KAAK,YAAY;oBAChC,aAAa,KAAK,OAAO,CAAC,OAAO;oBACjC,KAAK,KAAK,SAAS,EACnB,CAAC;oBACD,MAAM,EAAE,GAAG,IAAI,SAAS,CACtB,gBAAgB,CAAC,GAAG,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAC/C,CAAC;oBACF,EAAE,CAAC,MAAM,GAAG,GAAG,EAAE;wBACf,IAAI,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,SAAS,EAAE,EAAE,EAAE,CAAC,CAAC;oBAC7C,CAAC,CAAC;oBACF,EAAE,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;wBACrB,IAAI,KAAK,CAAC,IAAI,KAAK,mBAAmB,CAAC,cAAc,EAAE,CAAC;4BACtD,MAAM,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,YAAY,EAAE,CAAC;4BAC1C,OAAO,CACL,IAAI,mBAAQ,CAAC;gCACX,OAAO,EAAE,iCAAiC,KAAK,CAAC,MAAM,EAAE;gCACxD,MAAM,EAAE,KAAK,CAAC,IAAI;6BACnB,CAAC,CACH,CAAC;wBACJ,CAAC;wBACD,IAAI,CAAC,EAAE,IAAI,EAAE,kBAAkB,EAAE,IAAI,EAAE,KAAK,CAAC,IAAI,EAAE,CAAC,CAAC;oBACvD,CAAC,CAAC;oBACF,EAAE,CAAC,OAAO,GAAG,CAAC,KAAK,EAAE,EAAE;wBACrB,oDAAoD;wBACpD,MAAM,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,YAAY,EAAE,CAAC;wBAC1C,OAAO,CAAC,IAAI,mBAAQ,CAAC,EAAE,OAAO,EAAE,eAAe,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC,CAAC,CAAC;oBACnE,CAAC,CAAC;oBACF,EAAE,CAAC,SAAS,GAAG,CAAC,KAAK,EAAE,EAAE;wBACvB,MAAM,EAAE,QAAQ,EAAE,GAAG,YAAY,EAAE,CAAC;wBAEpC,6CAA6C;wBAC7C,IAAI,KAAK,CAAC,IAAI,YAAY,WAAW,EAAE,CAAC;4BACtC,MAAM,MAAM,GAAG,IAAA,gBAAM,EAAC,IAAI,UAAU,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC,CAAC;4BAClD,QAAQ,CAAC,MAAM,CAAC,CAAC;4BACjB,OAAO;wBACT,CAAC;wBACD,IAAI,KAAK,CAAC,IAAI,YAAY,UAAU,EAAE,CAAC;4BACrC,MAAM,MAAM,GAAG,IAAA,gBAAM,EAAC,KAAK,CAAC,IAAI,CAAC,CAAC;4BAClC,QAAQ,CAAC,MAAM,CAAC,CAAC;4BACjB,OAAO;wBACT,CAAC;wBACD,IAAI,KAAK,CAAC,IAAI,YAAY,IAAI,EAAE,CAAC;4BAC/B,KAAK,CAAC,IAAI,CAAC,WAAW,EAAE,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,EAAE;gCACvC,MAAM,MAAM,GAAG,IAAA,gBAAM,EAAC,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC,CAAC;gCAC9C,QAAQ,CAAC,MAAM,CAAC,CAAC;4BACnB,CAAC,CAAC,CAAC;4BACH,OAAO;wBACT,CAAC;wBAED,kDAAkD;wBAClD,MAAM,IAAI,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;wBAEpC,2DAA2D;wBAC3D,kEAAkE;wBAClE,2CAA2C;wBAC3C,IAAI,mBAAmB,CAAC,IAAI,CAAC,EAAE,CAAC;4BAC9B,IAAI,CAAC;gCACH,IAAI,EAAE,cAAc;gCACpB,KAAK,EAAE,IAAI,KAAK,CAAC,cAAc,CAAC;6BACjC,CAAC,CAAC;4BACH,OAAO;wBACT,CAAC;wBACD,IAAI,kBAAkB,CAAC,IAAI,CAAC,EAAE,CAAC;4BAC7B,QAAQ,CAAC,IAAI,CAAC,CAAC;4BACf,OAAO;wBACT,CAAC;wBACD,IAAI,gBAAgB,CAAC,IAAI,CAAC,EAAE,CAAC;4BAC3B,IAAI,IAAI,CAAC,KAAK,KAAK,SAAS,EAAE,CAAC;gCAC7B,kEAAkE;gCAClE,6DAA6D;gCAC7D,yBAAyB;gCACzB,OAAO;4BACT,CAAC;4BACD,MAAM,EAAE,OAAO,GAAG,IAAI,EAAE,GAAG,YAAY,EAAE,CAAC;4BAC1C,OAAO,CACL,IAAI,mBAAQ,CAAC;gCACX,OAAO,EAAE,GAAG,IAAI,CAAC,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE;gCACxC,gCAAgC;gCAChC,MAAM,EAAE,GAAG;gCACX,IAAI,EAAE,IAAI;6BACX,CAAC,CACH,CAAC;4BACF,OAAO;wBACT,CAAC;oBACH,CAAC,CAAC;gBACJ,CAAC;gBACD,aAAa,GAAG,OAAO,CAAC,OAAO,CAAC;YAClC,CAAC,CACF,CAAC;YAEF,MAAM,IAAI,GAAG,CAAC,KAAqC,EAAE,EAAE;gBACrD,wDAAwD;gBACxD,YAAY,CAAC,aAAa,CAAC;oBACzB,IAAI,EAAE,MAAM;oBACZ,OAAO,EAAE,KAAK;iBACf,CAAC,CAAC;YACL,CAAC,CAAC;YAEF,MAAM,KAAK,GAAG,GAAG,EAAE;gBACjB,YAAY,CAAC,IAAI,CAAC,EAAE,IAAI,EAAE,OAAO,EAAE,CAAC,CAAC;YACvC,CAAC,CAAC;YAEF,OAAO;gBACL,IAAI;gBACJ,KAAK;aACN,CAAC;QACJ,CAAC;KACF,CAAC;AACJ,CAAC", "sourcesContent": ["/* eslint-disable @typescript-eslint/no-explicit-any */\nimport { decode, encode } from \"@msgpack/msgpack\";\nimport {\n  ContextFunction,\n  InterpretOnChangeFunction,\n  Service,\n  createMachine,\n  guard,\n  immediate,\n  interpret,\n  reduce,\n  state,\n  transition,\n} from \"robot3\";\nimport { TOKEN_EXPIRATION_SECONDS, getTemporaryAuthToken } from \"./auth\";\nimport { RequiredConfig } from \"./config\";\nimport { ApiError } from \"./response\";\nimport { isBrowser } from \"./runtime\";\nimport { ensureEndpointIdFormat, isReact, throttle } from \"./utils\";\n\n// Define the context\ninterface Context {\n  token?: string;\n  enqueuedMessage?: any;\n  websocket?: WebSocket;\n  error?: Error;\n}\n\nconst initialState: ContextFunction<Context> = () => ({\n  enqueuedMessage: undefined,\n});\n\ntype SendEvent = { type: \"send\"; message: any };\ntype AuthenticatedEvent = { type: \"authenticated\"; token: string };\ntype InitiateAuthEvent = { type: \"initiateAuth\" };\ntype UnauthorizedEvent = { type: \"unauthorized\"; error: Error };\ntype ConnectedEvent = { type: \"connected\"; websocket: WebSocket };\ntype ConnectionClosedEvent = {\n  type: \"connectionClosed\";\n  code: number;\n  reason: string;\n};\n\ntype Event =\n  | SendEvent\n  | AuthenticatedEvent\n  | InitiateAuthEvent\n  | UnauthorizedEvent\n  | ConnectedEvent\n  | ConnectionClosedEvent;\n\nfunction hasToken(context: Context): boolean {\n  return context.token !== undefined;\n}\n\nfunction noToken(context: Context): boolean {\n  return !hasToken(context);\n}\n\nfunction enqueueMessage(context: Context, event: SendEvent): Context {\n  return {\n    ...context,\n    enqueuedMessage: event.message,\n  };\n}\n\nfunction closeConnection(context: Context): Context {\n  if (context.websocket && context.websocket.readyState === WebSocket.OPEN) {\n    context.websocket.close();\n  }\n  return {\n    ...context,\n    websocket: undefined,\n  };\n}\n\nfunction sendMessage(context: Context, event: SendEvent): Context {\n  if (context.websocket && context.websocket.readyState === WebSocket.OPEN) {\n    if (event.message instanceof Uint8Array) {\n      context.websocket.send(event.message);\n    } else {\n      context.websocket.send(encode(event.message));\n    }\n\n    return {\n      ...context,\n      enqueuedMessage: undefined,\n    };\n  }\n  return {\n    ...context,\n    enqueuedMessage: event.message,\n  };\n}\n\nfunction expireToken(context: Context): Context {\n  return {\n    ...context,\n    token: undefined,\n  };\n}\n\nfunction setToken(context: Context, event: AuthenticatedEvent): Context {\n  return {\n    ...context,\n    token: event.token,\n  };\n}\n\nfunction connectionEstablished(\n  context: Context,\n  event: ConnectedEvent,\n): Context {\n  return {\n    ...context,\n    websocket: event.websocket,\n  };\n}\n\n// State machine\nconst connectionStateMachine = createMachine(\n  \"idle\",\n  {\n    idle: state(\n      transition(\"send\", \"connecting\", reduce(enqueueMessage)),\n      transition(\"expireToken\", \"idle\", reduce(expireToken)),\n      transition(\"close\", \"idle\", reduce(closeConnection)),\n    ),\n    connecting: state(\n      transition(\"connecting\", \"connecting\"),\n      transition(\"connected\", \"active\", reduce(connectionEstablished)),\n      transition(\"connectionClosed\", \"idle\", reduce(closeConnection)),\n      transition(\"send\", \"connecting\", reduce(enqueueMessage)),\n      transition(\"close\", \"idle\", reduce(closeConnection)),\n      immediate(\"authRequired\", guard(noToken)),\n    ),\n    authRequired: state(\n      transition(\"initiateAuth\", \"authInProgress\"),\n      transition(\"send\", \"authRequired\", reduce(enqueueMessage)),\n      transition(\"close\", \"idle\", reduce(closeConnection)),\n    ),\n    authInProgress: state(\n      transition(\"authenticated\", \"connecting\", reduce(setToken)),\n      transition(\n        \"unauthorized\",\n        \"idle\",\n        reduce(expireToken),\n        reduce(closeConnection),\n      ),\n      transition(\"send\", \"authInProgress\", reduce(enqueueMessage)),\n      transition(\"close\", \"idle\", reduce(closeConnection)),\n    ),\n    active: state(\n      transition(\"send\", \"active\", reduce(sendMessage)),\n      transition(\"unauthorized\", \"idle\", reduce(expireToken)),\n      transition(\"connectionClosed\", \"idle\", reduce(closeConnection)),\n      transition(\"close\", \"idle\", reduce(closeConnection)),\n    ),\n    failed: state(\n      transition(\"send\", \"failed\"),\n      transition(\"close\", \"idle\", reduce(closeConnection)),\n    ),\n  },\n  initialState,\n);\n\ntype WithRequestId = {\n  request_id: string;\n};\n\n/**\n * A connection object that allows you to `send` request payloads to a\n * realtime endpoint.\n */\nexport interface RealtimeConnection<Input> {\n  send(input: Input & Partial<WithRequestId>): void;\n\n  close(): void;\n}\n\n/**\n * Options for connecting to the realtime endpoint.\n */\nexport interface RealtimeConnectionHandler<Output> {\n  /**\n   * The connection key. This is used to reuse the same connection\n   * across multiple calls to `connect`. This is particularly useful in\n   * contexts where the connection is established as part of a component\n   * lifecycle (e.g. React) and the component is re-rendered multiple times.\n   */\n  connectionKey?: string;\n\n  /**\n   * If `true`, the connection will only be established on the client side.\n   * This is useful for frameworks that reuse code for both server-side\n   * rendering and client-side rendering (e.g. Next.js).\n   *\n   * This is set to `true` by default when running on React in the server.\n   * Otherwise, it is set to `false`.\n   *\n   * Note that more SSR frameworks might be automatically detected\n   * in the future. In the meantime, you can set this to `true` when needed.\n   */\n  clientOnly?: boolean;\n\n  /**\n   * The throtle duration in milliseconds. This is used to throtle the\n   * calls to the `send` function. Realtime apps usually react to user\n   * input, which can be very frequent (e.g. fast typing or mouse/drag movements).\n   *\n   * The default value is `128` milliseconds.\n   */\n  throttleInterval?: number;\n\n  /**\n   * Configures the maximum amount of frames to store in memory before starting to drop\n   * old ones for in favor of the newer ones. It must be between `1` and `60`.\n   *\n   * The recommended is `2`. The default is `undefined` so it can be determined\n   * by the app (normally is set to the recommended setting).\n   */\n  maxBuffering?: number;\n\n  /**\n   * Callback function that is called when a result is received.\n   * @param result - The result of the request.\n   */\n  onResult(result: Output & WithRequestId): void;\n\n  /**\n   * Callback function that is called when an error occurs.\n   * @param error - The error that occurred.\n   */\n  onError?(error: ApiError<any>): void;\n}\n\nexport interface RealtimeClient {\n  /**\n   * Connect to the realtime endpoint. The default implementation uses\n   * WebSockets to connect to fal function endpoints that support WSS.\n   *\n   * @param app the app alias or identifier.\n   * @param handler the connection handler.\n   */\n  connect<Input = any, Output = any>(\n    app: string,\n    handler: RealtimeConnectionHandler<Output>,\n  ): RealtimeConnection<Input>;\n}\n\ntype RealtimeUrlParams = {\n  token: string;\n  maxBuffering?: number;\n};\n\nfunction buildRealtimeUrl(\n  app: string,\n  { token, maxBuffering }: RealtimeUrlParams,\n): string {\n  if (maxBuffering !== undefined && (maxBuffering < 1 || maxBuffering > 60)) {\n    throw new Error(\"The `maxBuffering` must be between 1 and 60 (inclusive)\");\n  }\n  const queryParams = new URLSearchParams({\n    fal_jwt_token: token,\n  });\n  if (maxBuffering !== undefined) {\n    queryParams.set(\"max_buffering\", maxBuffering.toFixed(0));\n  }\n  const appId = ensureEndpointIdFormat(app);\n  return `wss://fal.run/${appId}/realtime?${queryParams.toString()}`;\n}\n\nconst DEFAULT_THROTTLE_INTERVAL = 128;\n\nfunction isUnauthorizedError(message: any): boolean {\n  // TODO we need better protocol definition with error codes\n  return message[\"status\"] === \"error\" && message[\"error\"] === \"Unauthorized\";\n}\n\n/**\n * See https://www.rfc-editor.org/rfc/rfc6455.html#section-7.4.1\n */\nconst WebSocketErrorCodes = {\n  NORMAL_CLOSURE: 1000,\n  GOING_AWAY: 1001,\n};\n\ntype ConnectionStateMachine = Service<typeof connectionStateMachine> & {\n  throttledSend: (\n    event: Event,\n    payload?: any,\n  ) => void | Promise<void> | undefined;\n};\n\ntype ConnectionOnChange = InterpretOnChangeFunction<\n  typeof connectionStateMachine\n>;\n\ntype RealtimeConnectionCallback = Pick<\n  RealtimeConnectionHandler<any>,\n  \"onResult\" | \"onError\"\n>;\n\nconst connectionCache = new Map<string, ConnectionStateMachine>();\nconst connectionCallbacks = new Map<string, RealtimeConnectionCallback>();\nfunction reuseInterpreter(\n  key: string,\n  throttleInterval: number,\n  onChange: ConnectionOnChange,\n) {\n  if (!connectionCache.has(key)) {\n    const machine = interpret(connectionStateMachine, onChange);\n    connectionCache.set(key, {\n      ...machine,\n      throttledSend:\n        throttleInterval > 0\n          ? throttle(machine.send, throttleInterval, true)\n          : machine.send,\n    });\n  }\n  return connectionCache.get(key) as ConnectionStateMachine;\n}\n\nconst noop = () => {\n  /* No-op */\n};\n\n/**\n * A no-op connection that does not send any message.\n * Useful on the frameworks that reuse code for both ssr and csr (e.g. Next)\n * so the call when doing ssr has no side-effects.\n */\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nconst NoOpConnection: RealtimeConnection<any> = {\n  send: noop,\n  close: noop,\n};\n\nfunction isSuccessfulResult(data: any): boolean {\n  return (\n    data.status !== \"error\" &&\n    data.type !== \"x-fal-message\" &&\n    !isFalErrorResult(data)\n  );\n}\n\ntype FalErrorResult = {\n  type: \"x-fal-error\";\n  error: string;\n  reason: string;\n};\n\nfunction isFalErrorResult(data: any): data is FalErrorResult {\n  return data.type === \"x-fal-error\";\n}\n\ntype RealtimeClientDependencies = {\n  config: RequiredConfig;\n};\n\nexport function createRealtimeClient({\n  config,\n}: RealtimeClientDependencies): RealtimeClient {\n  return {\n    connect<Input, Output>(\n      app: string,\n      handler: RealtimeConnectionHandler<Output>,\n    ): RealtimeConnection<Input> {\n      const {\n        // if running on React in the server, set clientOnly to true by default\n        clientOnly = isReact() && !isBrowser(),\n        connectionKey = crypto.randomUUID(),\n        maxBuffering,\n        throttleInterval = DEFAULT_THROTTLE_INTERVAL,\n      } = handler;\n      if (clientOnly && !isBrowser()) {\n        return NoOpConnection;\n      }\n\n      let previousState: string | undefined;\n\n      // Although the state machine is cached so we don't open multiple connections,\n      // we still need to update the callbacks so we can call the correct references\n      // when the state machine is reused. This is needed because the callbacks\n      // are passed as part of the handler object, which can be different across\n      // different calls to `connect`.\n      connectionCallbacks.set(connectionKey, {\n        onError: handler.onError,\n        onResult: handler.onResult,\n      });\n      const getCallbacks = () =>\n        connectionCallbacks.get(connectionKey) as RealtimeConnectionCallback;\n      const stateMachine = reuseInterpreter(\n        connectionKey,\n        throttleInterval,\n        ({ context, machine, send }) => {\n          const { enqueuedMessage, token } = context;\n          if (machine.current === \"active\" && enqueuedMessage) {\n            send({ type: \"send\", message: enqueuedMessage });\n          }\n          if (\n            machine.current === \"authRequired\" &&\n            token === undefined &&\n            previousState !== machine.current\n          ) {\n            send({ type: \"initiateAuth\" });\n            getTemporaryAuthToken(app, config)\n              .then((token) => {\n                send({ type: \"authenticated\", token });\n                const tokenExpirationTimeout = Math.round(\n                  TOKEN_EXPIRATION_SECONDS * 0.9 * 1000,\n                );\n                setTimeout(() => {\n                  send({ type: \"expireToken\" });\n                }, tokenExpirationTimeout);\n              })\n              .catch((error) => {\n                send({ type: \"unauthorized\", error });\n              });\n          }\n          if (\n            machine.current === \"connecting\" &&\n            previousState !== machine.current &&\n            token !== undefined\n          ) {\n            const ws = new WebSocket(\n              buildRealtimeUrl(app, { token, maxBuffering }),\n            );\n            ws.onopen = () => {\n              send({ type: \"connected\", websocket: ws });\n            };\n            ws.onclose = (event) => {\n              if (event.code !== WebSocketErrorCodes.NORMAL_CLOSURE) {\n                const { onError = noop } = getCallbacks();\n                onError(\n                  new ApiError({\n                    message: `Error closing the connection: ${event.reason}`,\n                    status: event.code,\n                  }),\n                );\n              }\n              send({ type: \"connectionClosed\", code: event.code });\n            };\n            ws.onerror = (event) => {\n              // TODO specify error protocol for identified errors\n              const { onError = noop } = getCallbacks();\n              onError(new ApiError({ message: \"Unknown error\", status: 500 }));\n            };\n            ws.onmessage = (event) => {\n              const { onResult } = getCallbacks();\n\n              // Handle binary messages as msgpack messages\n              if (event.data instanceof ArrayBuffer) {\n                const result = decode(new Uint8Array(event.data));\n                onResult(result);\n                return;\n              }\n              if (event.data instanceof Uint8Array) {\n                const result = decode(event.data);\n                onResult(result);\n                return;\n              }\n              if (event.data instanceof Blob) {\n                event.data.arrayBuffer().then((buffer) => {\n                  const result = decode(new Uint8Array(buffer));\n                  onResult(result);\n                });\n                return;\n              }\n\n              // Otherwise handle strings as plain JSON messages\n              const data = JSON.parse(event.data);\n\n              // Drop messages that are not related to the actual result.\n              // In the future, we might want to handle other types of messages.\n              // TODO: specify the fal ws protocol format\n              if (isUnauthorizedError(data)) {\n                send({\n                  type: \"unauthorized\",\n                  error: new Error(\"Unauthorized\"),\n                });\n                return;\n              }\n              if (isSuccessfulResult(data)) {\n                onResult(data);\n                return;\n              }\n              if (isFalErrorResult(data)) {\n                if (data.error === \"TIMEOUT\") {\n                  // Timeout error messages just indicate that the connection hasn't\n                  // received an incoming message for a while. We don't need to\n                  // handle them as errors.\n                  return;\n                }\n                const { onError = noop } = getCallbacks();\n                onError(\n                  new ApiError({\n                    message: `${data.error}: ${data.reason}`,\n                    // TODO better error status code\n                    status: 400,\n                    body: data,\n                  }),\n                );\n                return;\n              }\n            };\n          }\n          previousState = machine.current;\n        },\n      );\n\n      const send = (input: Input & Partial<WithRequestId>) => {\n        // Use throttled send to avoid sending too many messages\n        stateMachine.throttledSend({\n          type: \"send\",\n          message: input,\n        });\n      };\n\n      const close = () => {\n        stateMachine.send({ type: \"close\" });\n      };\n\n      return {\n        send,\n        close,\n      };\n    },\n  };\n}\n"]}