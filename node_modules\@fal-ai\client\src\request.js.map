{"version": 3, "file": "request.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/request.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;AAuBA,0CA+CC;AAWD,4BA2BC;AA1GD,uCAAoD;AAEpD,mCAA6D;AAE7D,MAAM,mBAAmB,GACvB,OAAO,SAAS,KAAK,WAAW;IAChC,CAAA,SAAS,aAAT,SAAS,uBAAT,SAAS,CAAE,SAAS,MAAK,oBAAoB,CAAC;AAehD,SAAsB,eAAe,CACnC,MAA4B;;;QAE5B,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,OAAO,GAAG,EAAE,EAAE,GAAG,MAAM,CAAC;QAC1D,MAAM,EACJ,WAAW,EAAE,gBAAgB,EAC7B,iBAAiB,EACjB,eAAe,EACf,KAAK,GACN,GAAG,MAAM,CAAC;QACX,MAAM,SAAS,GAAG,IAAA,mBAAS,GAAE,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,YAAY,EAAE,IAAA,sBAAY,GAAE,EAAE,CAAC;QACtE,MAAM,WAAW,GACf,OAAO,gBAAgB,KAAK,UAAU;YACpC,CAAC,CAAC,gBAAgB,EAAE;YACpB,CAAC,CAAC,gBAAgB,CAAC;QAEvB,MAAM,EAAE,MAAM,EAAE,GAAG,EAAE,OAAO,EAAE,GAAG,MAAM,iBAAiB,CAAC;YACvD,MAAM,EAAE,CAAC,MAAA,MAAA,MAAM,CAAC,MAAM,mCAAI,OAAO,CAAC,MAAM,mCAAI,MAAM,CAAC,CAAC,WAAW,EAAE;YACjE,GAAG,EAAE,SAAS;YACd,OAAO,EAAE,MAAM,CAAC,OAAO;SACxB,CAAC,CAAC;QACH,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAC,EAAE,aAAa,EAAE,OAAO,WAAW,EAAE,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;QAC9E,MAAM,cAAc,GAAG,4DAClB,UAAU,KACb,MAAM,EAAE,kBAAkB,EAC1B,cAAc,EAAE,kBAAkB,KAC/B,SAAS,GACT,CAAC,OAAO,aAAP,OAAO,cAAP,OAAO,GAAI,EAAE,CAAC,CACJ,CAAC;QAEjB,MAAM,EAAE,eAAe,EAAE,qBAAqB,KAAqB,OAAO,EAAvB,WAAW,UAAK,OAAO,EAApE,mBAA0D,CAAU,CAAC;QAC3E,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,GAAG,8DAC3B,WAAW,KACd,MAAM,EACN,OAAO,kCACF,cAAc,GACd,CAAC,MAAA,WAAW,CAAC,OAAO,mCAAI,EAAE,CAAC,MAE7B,CAAC,CAAC,mBAAmB,IAAI,EAAE,IAAI,EAAE,MAAM,EAAE,CAAC,KAC7C,MAAM,EAAE,OAAO,CAAC,MAAM,EACtB,IAAI,EACF,MAAM,CAAC,WAAW,EAAE,KAAK,KAAK,IAAI,KAAK;gBACrC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC;gBACvB,CAAC,CAAC,SAAS,IACf,CAAC;QACH,MAAM,cAAc,GAAG,qBAAqB,aAArB,qBAAqB,cAArB,qBAAqB,GAAI,eAAe,CAAC;QAChE,OAAO,MAAM,cAAc,CAAC,QAAQ,CAAC,CAAC;IACxC,CAAC;CAAA;AAED;;;;;;;;GAQG;AACH,SAAgB,QAAQ,CACtB,EAAU,EACV,UAA0C,EAAE;;IAE5C,MAAM,MAAM,GAAG,CAAC,MAAA,OAAO,CAAC,MAAM,mCAAI,MAAM,CAAC,CAAC,WAAW,EAAE,CAAC;IACxD,MAAM,IAAI,GAAG,CAAC,MAAA,OAAO,CAAC,IAAI,mCAAI,EAAE,CAAC,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC,OAAO,CAAC,QAAQ,EAAE,GAAG,CAAC,CAAC;IAC5E,MAAM,KAAK,GAAG,OAAO,CAAC,KAAK,CAAC;IAC5B,MAAM,MAAM,mCACP,CAAC,OAAO,CAAC,KAAK,IAAI,EAAE,CAAC,GACrB,CAAC,MAAM,KAAK,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,EAAE,CAAC,CACnC,CAAC;IAEF,MAAM,WAAW,GACf,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,MAAM,GAAG,CAAC;QAC5B,CAAC,CAAC,IAAI,IAAI,eAAe,CAAC,MAAM,CAAC,CAAC,QAAQ,EAAE,EAAE;QAC9C,CAAC,CAAC,EAAE,CAAC;IAET,sCAAsC;IACtC,IAAI,IAAA,kBAAU,EAAC,EAAE,CAAC,EAAE,CAAC;QACnB,MAAM,GAAG,GAAG,EAAE,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC;QAC7C,OAAO,GAAG,GAAG,GAAG,IAAI,GAAG,WAAW,EAAE,CAAC;IACvC,CAAC;IAED,MAAM,KAAK,GAAG,IAAA,8BAAsB,EAAC,EAAE,CAAC,CAAC;IACzC,MAAM,SAAS,GAAG,OAAO,CAAC,SAAS,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,SAAS,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC;IACnE,MAAM,GAAG,GAAG,WAAW,SAAS,WAAW,KAAK,IAAI,IAAI,EAAE,CAAC;IAC3D,OAAO,GAAG,GAAG,CAAC,OAAO,CAAC,KAAK,EAAE,EAAE,CAAC,GAAG,WAAW,EAAE,CAAC;AACnD,CAAC", "sourcesContent": ["import { RequiredConfig } from \"./config\";\nimport { ResponseHand<PERSON> } from \"./response\";\nimport { getUserAgent, isBrowser } from \"./runtime\";\nimport { RunOptions, UrlOptions } from \"./types/common\";\nimport { ensureEndpointIdFormat, isValidUrl } from \"./utils\";\n\nconst isCloudflareWorkers =\n  typeof navigator !== \"undefined\" &&\n  navigator?.userAgent === \"Cloudflare-Workers\";\n\ntype RequestOptions = {\n  responseHandler?: ResponseHandler<any>;\n};\n\ntype RequestParams<Input = any> = {\n  method?: string;\n  targetUrl: string;\n  input?: Input;\n  config: RequiredConfig;\n  options?: RequestOptions & RequestInit;\n  headers?: Record<string, string>;\n};\n\nexport async function dispatchRequest<Input, Output>(\n  params: RequestParams<Input>,\n): Promise<Output> {\n  const { targetUrl, input, config, options = {} } = params;\n  const {\n    credentials: credentialsValue,\n    requestMiddleware,\n    responseHandler,\n    fetch,\n  } = config;\n  const userAgent = isBrowser() ? {} : { \"User-Agent\": getUserAgent() };\n  const credentials =\n    typeof credentialsValue === \"function\"\n      ? credentialsValue()\n      : credentialsValue;\n\n  const { method, url, headers } = await requestMiddleware({\n    method: (params.method ?? options.method ?? \"post\").toUpperCase(),\n    url: targetUrl,\n    headers: params.headers,\n  });\n  const authHeader = credentials ? { Authorization: `Key ${credentials}` } : {};\n  const requestHeaders = {\n    ...authHeader,\n    Accept: \"application/json\",\n    \"Content-Type\": \"application/json\",\n    ...userAgent,\n    ...(headers ?? {}),\n  } as HeadersInit;\n\n  const { responseHandler: customResponseHandler, ...requestInit } = options;\n  const response = await fetch(url, {\n    ...requestInit,\n    method,\n    headers: {\n      ...requestHeaders,\n      ...(requestInit.headers ?? {}),\n    },\n    ...(!isCloudflareWorkers && { mode: \"cors\" }),\n    signal: options.signal,\n    body:\n      method.toLowerCase() !== \"get\" && input\n        ? JSON.stringify(input)\n        : undefined,\n  });\n  const handleResponse = customResponseHandler ?? responseHandler;\n  return await handleResponse(response);\n}\n\n/**\n * Builds the final url to run the function based on its `id` or alias and\n * a the options from `RunOptions<Input>`.\n *\n * @private\n * @param id the function id or alias\n * @param options the run options\n * @returns the final url to run the function\n */\nexport function buildUrl<Input>(\n  id: string,\n  options: RunOptions<Input> & UrlOptions = {},\n): string {\n  const method = (options.method ?? \"post\").toLowerCase();\n  const path = (options.path ?? \"\").replace(/^\\//, \"\").replace(/\\/{2,}/, \"/\");\n  const input = options.input;\n  const params = {\n    ...(options.query || {}),\n    ...(method === \"get\" ? input : {}),\n  };\n\n  const queryParams =\n    Object.keys(params).length > 0\n      ? `?${new URLSearchParams(params).toString()}`\n      : \"\";\n\n  // if a fal url is passed, just use it\n  if (isValidUrl(id)) {\n    const url = id.endsWith(\"/\") ? id : `${id}/`;\n    return `${url}${path}${queryParams}`;\n  }\n\n  const appId = ensureEndpointIdFormat(id);\n  const subdomain = options.subdomain ? `${options.subdomain}.` : \"\";\n  const url = `https://${subdomain}fal.run/${appId}/${path}`;\n  return `${url.replace(/\\/$/, \"\")}${queryParams}`;\n}\n"]}