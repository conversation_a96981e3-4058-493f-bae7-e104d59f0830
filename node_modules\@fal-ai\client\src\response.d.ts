import { RequiredConfig } from "./config";
import { Result, ValidationErrorInfo } from "./types/common";
export type ResponseHandler<Output> = (response: Response) => Promise<Output>;
export type ResponseHandlerCreator<Output> = (config: RequiredConfig) => ResponseHandler<Output>;
type ApiErrorArgs = {
    message: string;
    status: number;
    body?: any;
};
export declare class ApiError<Body> extends Error {
    readonly status: number;
    readonly body: Body;
    constructor({ message, status, body }: ApiErrorArgs);
}
type ValidationErrorBody = {
    detail: ValidationErrorInfo[];
};
export declare class ValidationError extends ApiError<ValidationErrorBody> {
    constructor(args: ApiErrorArgs);
    get fieldErrors(): ValidationErrorInfo[];
    getFieldErrors(field: string): ValidationErrorInfo[];
}
export declare function defaultResponseHandler<Output>(response: Response): Promise<Output>;
export declare function resultResponseHandler<Output>(response: Response): Promise<Result<Output>>;
export {};
