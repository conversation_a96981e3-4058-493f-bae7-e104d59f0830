{"version": 3, "file": "response.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/response.ts"], "names": [], "mappings": ";;;;;;;;;;;;AA6DA,wDA4BC;AAED,sDAQC;AA9FD,MAAM,iBAAiB,GAAG,kBAAkB,CAAC;AAa7C,MAAa,QAAe,SAAQ,KAAK;IAGvC,YAAY,EAAE,OAAO,EAAE,MAAM,EAAE,IAAI,EAAgB;QACjD,KAAK,CAAC,OAAO,CAAC,CAAC;QACf,IAAI,CAAC,IAAI,GAAG,UAAU,CAAC;QACvB,IAAI,CAAC,MAAM,GAAG,MAAM,CAAC;QACrB,IAAI,CAAC,IAAI,GAAG,IAAI,CAAC;IACnB,CAAC;CACF;AATD,4BASC;AAMD,MAAa,eAAgB,SAAQ,QAA6B;IAChE,YAAY,IAAkB;QAC5B,KAAK,CAAC,IAAI,CAAC,CAAC;QACZ,IAAI,CAAC,IAAI,GAAG,iBAAiB,CAAC;IAChC,CAAC;IAED,IAAI,WAAW;QACb,+DAA+D;QAC/D,uEAAuE;QACvE,IAAI,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,KAAK,QAAQ,EAAE,CAAC;YACzC,OAAO;gBACL;oBACE,GAAG,EAAE,CAAC,MAAM,CAAC;oBACb,GAAG,EAAE,IAAI,CAAC,IAAI,CAAC,MAAM;oBACrB,IAAI,EAAE,aAAa;iBACpB;aACF,CAAC;QACJ,CAAC;QACD,OAAO,IAAI,CAAC,IAAI,CAAC,MAAM,IAAI,EAAE,CAAC;IAChC,CAAC;IAED,cAAc,CAAC,KAAa;QAC1B,OAAO,IAAI,CAAC,WAAW,CAAC,MAAM,CAC5B,CAAC,KAAK,EAAE,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,GAAG,CAAC,CAAC,KAAK,KAAK,CACrD,CAAC;IACJ,CAAC;CACF;AA1BD,0CA0BC;AAED,SAAsB,sBAAsB,CAC1C,QAAkB;;;QAElB,MAAM,EAAE,MAAM,EAAE,UAAU,EAAE,GAAG,QAAQ,CAAC;QACxC,MAAM,WAAW,GAAG,MAAA,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,cAAc,CAAC,mCAAI,EAAE,CAAC;QAC/D,IAAI,CAAC,QAAQ,CAAC,EAAE,EAAE,CAAC;YACjB,IAAI,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;gBAC7C,MAAM,IAAI,GAAG,MAAM,QAAQ,CAAC,IAAI,EAAE,CAAC;gBACnC,MAAM,SAAS,GAAG,MAAM,KAAK,GAAG,CAAC,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,CAAC;gBAC9D,MAAM,IAAI,SAAS,CAAC;oBAClB,OAAO,EAAE,IAAI,CAAC,OAAO,IAAI,UAAU;oBACnC,MAAM;oBACN,IAAI;iBACL,CAAC,CAAC;YACL,CAAC;YACD,MAAM,IAAI,QAAQ,CAAC,EAAE,OAAO,EAAE,QAAQ,MAAM,KAAK,UAAU,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAC3E,CAAC;QACD,IAAI,WAAW,CAAC,QAAQ,CAAC,kBAAkB,CAAC,EAAE,CAAC;YAC7C,OAAO,QAAQ,CAAC,IAAI,EAAqB,CAAC;QAC5C,CAAC;QACD,IAAI,WAAW,CAAC,QAAQ,CAAC,WAAW,CAAC,EAAE,CAAC;YACtC,OAAO,QAAQ,CAAC,IAAI,EAAqB,CAAC;QAC5C,CAAC;QACD,IAAI,WAAW,CAAC,QAAQ,CAAC,0BAA0B,CAAC,EAAE,CAAC;YACrD,OAAO,QAAQ,CAAC,WAAW,EAAqB,CAAC;QACnD,CAAC;QACD,sDAAsD;QACtD,OAAO,QAAQ,CAAC,IAAI,EAAqB,CAAC;IAC5C,CAAC;CAAA;AAED,SAAsB,qBAAqB,CACzC,QAAkB;;QAElB,MAAM,IAAI,GAAG,MAAM,sBAAsB,CAAS,QAAQ,CAAC,CAAC;QAC5D,OAAO;YACL,IAAI;YACJ,SAAS,EAAE,QAAQ,CAAC,OAAO,CAAC,GAAG,CAAC,iBAAiB,CAAC,IAAI,EAAE;SAChC,CAAC;IAC7B,CAAC;CAAA", "sourcesContent": ["import { RequiredConfig } from \"./config\";\nimport { Result, ValidationErrorInfo } from \"./types/common\";\n\nexport type ResponseHandler<Output> = (response: Response) => Promise<Output>;\n\nconst REQUEST_ID_HEADER = \"x-fal-request-id\";\n\nexport type ResponseHandlerCreator<Output> = (\n  config: RequiredConfig,\n) => ResponseHandler<Output>;\n\ntype ApiErrorArgs = {\n  message: string;\n  status: number;\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  body?: any;\n};\n\nexport class ApiError<Body> extends Error {\n  public readonly status: number;\n  public readonly body: Body;\n  constructor({ message, status, body }: ApiErrorArgs) {\n    super(message);\n    this.name = \"ApiError\";\n    this.status = status;\n    this.body = body;\n  }\n}\n\ntype ValidationErrorBody = {\n  detail: ValidationErrorInfo[];\n};\n\nexport class ValidationError extends ApiError<ValidationErrorBody> {\n  constructor(args: ApiErrorArgs) {\n    super(args);\n    this.name = \"ValidationError\";\n  }\n\n  get fieldErrors(): ValidationErrorInfo[] {\n    // NOTE: this is a hack to support both FastAPI/Pydantic errors\n    // and some custom 422 errors that might not be in the Pydantic format.\n    if (typeof this.body.detail === \"string\") {\n      return [\n        {\n          loc: [\"body\"],\n          msg: this.body.detail,\n          type: \"value_error\",\n        },\n      ];\n    }\n    return this.body.detail || [];\n  }\n\n  getFieldErrors(field: string): ValidationErrorInfo[] {\n    return this.fieldErrors.filter(\n      (error) => error.loc[error.loc.length - 1] === field,\n    );\n  }\n}\n\nexport async function defaultResponseHandler<Output>(\n  response: Response,\n): Promise<Output> {\n  const { status, statusText } = response;\n  const contentType = response.headers.get(\"Content-Type\") ?? \"\";\n  if (!response.ok) {\n    if (contentType.includes(\"application/json\")) {\n      const body = await response.json();\n      const ErrorType = status === 422 ? ValidationError : ApiError;\n      throw new ErrorType({\n        message: body.message || statusText,\n        status,\n        body,\n      });\n    }\n    throw new ApiError({ message: `HTTP ${status}: ${statusText}`, status });\n  }\n  if (contentType.includes(\"application/json\")) {\n    return response.json() as Promise<Output>;\n  }\n  if (contentType.includes(\"text/html\")) {\n    return response.text() as Promise<Output>;\n  }\n  if (contentType.includes(\"application/octet-stream\")) {\n    return response.arrayBuffer() as Promise<Output>;\n  }\n  // TODO convert to either number or bool automatically\n  return response.text() as Promise<Output>;\n}\n\nexport async function resultResponseHandler<Output>(\n  response: Response,\n): Promise<Result<Output>> {\n  const data = await defaultResponseHandler<Output>(response);\n  return {\n    data,\n    requestId: response.headers.get(REQUEST_ID_HEADER) || \"\",\n  } satisfies Result<Output>;\n}\n"]}