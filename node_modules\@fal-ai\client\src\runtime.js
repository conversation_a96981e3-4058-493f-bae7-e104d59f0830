"use strict";
/* eslint-disable @typescript-eslint/no-var-requires */
Object.defineProperty(exports, "__esModule", { value: true });
exports.isBrowser = isBrowser;
exports.getUserAgent = getUserAgent;
function isBrowser() {
    return (typeof window !== "undefined" && typeof window.document !== "undefined");
}
let memoizedUserAgent = null;
function getUserAgent() {
    if (memoizedUserAgent !== null) {
        return memoizedUserAgent;
    }
    const packageInfo = require("../package.json");
    memoizedUserAgent = `${packageInfo.name}/${packageInfo.version}`;
    return memoizedUserAgent;
}
//# sourceMappingURL=runtime.js.map