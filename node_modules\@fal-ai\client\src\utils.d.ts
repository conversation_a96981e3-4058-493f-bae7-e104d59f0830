export declare function ensureEndpointIdFormat(id: string): string;
declare const ENDPOINT_NAMESPACES: readonly ["workflows", "comfy"];
type EndpointNamespace = (typeof ENDPOINT_NAMESPACES)[number];
export type EndpointId = {
    readonly owner: string;
    readonly alias: string;
    readonly path?: string;
    readonly namespace?: EndpointNamespace;
};
export declare function parseEndpointId(id: string): EndpointId;
export declare function isValidUrl(url: string): boolean;
export declare function throttle<T extends (...args: any[]) => any>(func: T, limit: number, leading?: boolean): (...funcArgs: Parameters<T>) => ReturnType<T> | void;
/**
 * Not really the most optimal way to detect if we're running in React,
 * but the idea here is that we can support multiple rendering engines
 * (starting with React), with all their peculiarities, without having
 * to add a dependency or creating custom integrations (e.g. custom hooks).
 *
 * Yes, a bit of magic to make things works out-of-the-box.
 * @returns `true` if running in React, `false` otherwise.
 */
export declare function isReact(): boolean;
/**
 * Check if a value is a plain object.
 * @param value - The value to check.
 * @returns `true` if the value is a plain object, `false` otherwise.
 */
export declare function isPlainObject(value: any): boolean;
export {};
