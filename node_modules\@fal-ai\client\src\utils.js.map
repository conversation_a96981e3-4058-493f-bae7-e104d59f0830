{"version": 3, "file": "utils.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/utils.ts"], "names": [], "mappings": ";;AAAA,wDAYC;AAaD,0CAgBC;AAED,gCAOC;AAGD,4BA4BC;AAaD,0BASC;AAOD,sCAEC;AAhHD,SAAgB,sBAAsB,CAAC,EAAU;IAC/C,MAAM,KAAK,GAAG,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC5B,IAAI,KAAK,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACrB,OAAO,EAAE,CAAC;IACZ,CAAC;IACD,MAAM,CAAC,EAAE,QAAQ,EAAE,KAAK,CAAC,GAAG,4BAA4B,CAAC,IAAI,CAAC,EAAE,CAAC,IAAI,EAAE,CAAC;IACxE,IAAI,QAAQ,IAAI,KAAK,EAAE,CAAC;QACtB,OAAO,GAAG,QAAQ,IAAI,KAAK,EAAE,CAAC;IAChC,CAAC;IACD,MAAM,IAAI,KAAK,CACb,mBAAmB,EAAE,4CAA4C,CAClE,CAAC;AACJ,CAAC;AAED,MAAM,mBAAmB,GAAG,CAAC,WAAW,EAAE,OAAO,CAAU,CAAC;AAW5D,SAAgB,eAAe,CAAC,EAAU;IACxC,MAAM,YAAY,GAAG,sBAAsB,CAAC,EAAE,CAAC,CAAC;IAChD,MAAM,KAAK,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IACtC,IAAI,mBAAmB,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAQ,CAAC,EAAE,CAAC;QAClD,OAAO;YACL,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YACf,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;YACf,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,SAAS;YAC3C,SAAS,EAAE,KAAK,CAAC,CAAC,CAAsB;SACzC,CAAC;IACJ,CAAC;IACD,OAAO;QACL,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;QACf,KAAK,EAAE,KAAK,CAAC,CAAC,CAAC;QACf,IAAI,EAAE,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,GAAG,CAAC,IAAI,SAAS;KAC5C,CAAC;AACJ,CAAC;AAED,SAAgB,UAAU,CAAC,GAAW;IACpC,IAAI,CAAC;QACH,MAAM,EAAE,IAAI,EAAE,GAAG,IAAI,GAAG,CAAC,GAAG,CAAC,CAAC;QAC9B,OAAO,kBAAkB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;IACvC,CAAC;IAAC,OAAO,CAAC,EAAE,CAAC;QACX,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,8DAA8D;AAC9D,SAAgB,QAAQ,CACtB,IAAO,EACP,KAAa,EACb,OAAO,GAAG,KAAK;IAEf,IAAI,QAA+B,CAAC;IACpC,IAAI,OAAe,CAAC;IAEpB,OAAO,CAAC,GAAG,IAAmB,EAAwB,EAAE;QACtD,IAAI,CAAC,OAAO,IAAI,OAAO,EAAE,CAAC;YACxB,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;YACd,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QACvB,CAAC;aAAM,CAAC;YACN,IAAI,QAAQ,EAAE,CAAC;gBACb,YAAY,CAAC,QAAQ,CAAC,CAAC;YACzB,CAAC;YAED,QAAQ,GAAG,UAAU,CACnB,GAAG,EAAE;gBACH,IAAI,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,IAAI,KAAK,EAAE,CAAC;oBAClC,IAAI,CAAC,GAAG,IAAI,CAAC,CAAC;oBACd,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;gBACvB,CAAC;YACH,CAAC,EACD,KAAK,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC,CAC/B,CAAC;QACJ,CAAC;IACH,CAAC,CAAC;AACJ,CAAC;AAED,IAAI,gBAAqC,CAAC;AAE1C;;;;;;;;GAQG;AACH,SAAgB,OAAO;IACrB,IAAI,gBAAgB,KAAK,SAAS,EAAE,CAAC;QACnC,MAAM,KAAK,GAAG,IAAI,KAAK,EAAE,CAAC,KAAK,CAAC;QAChC,gBAAgB;YACd,CAAC,CAAC,KAAK;gBACP,CAAC,KAAK,CAAC,QAAQ,CAAC,yBAAyB,CAAC;oBACxC,KAAK,CAAC,QAAQ,CAAC,oBAAoB,CAAC,CAAC,CAAC;IAC5C,CAAC;IACD,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAED;;;;GAIG;AACH,SAAgB,aAAa,CAAC,KAAU;IACtC,OAAO,CAAC,CAAC,KAAK,IAAI,MAAM,CAAC,cAAc,CAAC,KAAK,CAAC,KAAK,MAAM,CAAC,SAAS,CAAC;AACtE,CAAC", "sourcesContent": ["export function ensureEndpointIdFormat(id: string): string {\n  const parts = id.split(\"/\");\n  if (parts.length > 1) {\n    return id;\n  }\n  const [, appOwner, appId] = /^([0-9]+)-([a-zA-Z0-9-]+)$/.exec(id) || [];\n  if (appOwner && appId) {\n    return `${appOwner}/${appId}`;\n  }\n  throw new Error(\n    `Invalid app id: ${id}. Must be in the format <appOwner>/<appId>`,\n  );\n}\n\nconst ENDPOINT_NAMESPACES = [\"workflows\", \"comfy\"] as const;\n\ntype EndpointNamespace = (typeof ENDPOINT_NAMESPACES)[number];\n\nexport type EndpointId = {\n  readonly owner: string;\n  readonly alias: string;\n  readonly path?: string;\n  readonly namespace?: EndpointNamespace;\n};\n\nexport function parseEndpointId(id: string): EndpointId {\n  const normalizedId = ensureEndpointIdFormat(id);\n  const parts = normalizedId.split(\"/\");\n  if (ENDPOINT_NAMESPACES.includes(parts[0] as any)) {\n    return {\n      owner: parts[1],\n      alias: parts[2],\n      path: parts.slice(3).join(\"/\") || undefined,\n      namespace: parts[0] as EndpointNamespace,\n    };\n  }\n  return {\n    owner: parts[0],\n    alias: parts[1],\n    path: parts.slice(2).join(\"/\") || undefined,\n  };\n}\n\nexport function isValidUrl(url: string) {\n  try {\n    const { host } = new URL(url);\n    return /(fal\\.(ai|run))$/.test(host);\n  } catch (_) {\n    return false;\n  }\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\nexport function throttle<T extends (...args: any[]) => any>(\n  func: T,\n  limit: number,\n  leading = false,\n): (...funcArgs: Parameters<T>) => ReturnType<T> | void {\n  let lastFunc: NodeJS.Timeout | null;\n  let lastRan: number;\n\n  return (...args: Parameters<T>): ReturnType<T> | void => {\n    if (!lastRan && leading) {\n      func(...args);\n      lastRan = Date.now();\n    } else {\n      if (lastFunc) {\n        clearTimeout(lastFunc);\n      }\n\n      lastFunc = setTimeout(\n        () => {\n          if (Date.now() - lastRan >= limit) {\n            func(...args);\n            lastRan = Date.now();\n          }\n        },\n        limit - (Date.now() - lastRan),\n      );\n    }\n  };\n}\n\nlet isRunningInReact: boolean | undefined;\n\n/**\n * Not really the most optimal way to detect if we're running in React,\n * but the idea here is that we can support multiple rendering engines\n * (starting with React), with all their peculiarities, without having\n * to add a dependency or creating custom integrations (e.g. custom hooks).\n *\n * Yes, a bit of magic to make things works out-of-the-box.\n * @returns `true` if running in React, `false` otherwise.\n */\nexport function isReact() {\n  if (isRunningInReact === undefined) {\n    const stack = new Error().stack;\n    isRunningInReact =\n      !!stack &&\n      (stack.includes(\"node_modules/react-dom/\") ||\n        stack.includes(\"node_modules/next/\"));\n  }\n  return isRunningInReact;\n}\n\n/**\n * Check if a value is a plain object.\n * @param value - The value to check.\n * @returns `true` if the value is a plain object, `false` otherwise.\n */\nexport function isPlainObject(value: any): boolean {\n  return !!value && Object.getPrototypeOf(value) === Object.prototype;\n}\n"]}