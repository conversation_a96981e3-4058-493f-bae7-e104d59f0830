{"version": 3, "file": "ExtensionCodecmjs", "sourceRoot": "", "sources": ["../src/ExtensionCodec.ts"], "names": [], "mappings": "AAAA,kDAAkD;AAElD,OAAO,EAAE,OAAO,EAAE,MAAM,cAAc,CAAC;AACvC,OAAO,EAAE,kBAAkB,EAAE,MAAM,gBAAgB,CAAC;AAqBpD,MAAM,OAAO,cAAc;IAgBzB;QARA,sBAAsB;QACL,oBAAe,GAAgE,EAAE,CAAC;QAClF,oBAAe,GAAgE,EAAE,CAAC;QAEnG,oBAAoB;QACH,aAAQ,GAAgE,EAAE,CAAC;QAC3E,aAAQ,GAAgE,EAAE,CAAC;QAG1F,IAAI,CAAC,QAAQ,CAAC,kBAAkB,CAAC,CAAC;IACpC,CAAC;IAEM,QAAQ,CAAC,EACd,IAAI,EACJ,MAAM,EACN,MAAM,GAKP;QACC,IAAI,IAAI,IAAI,CAAC,EAAE,CAAC;YACd,oBAAoB;YACpB,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;YAC7B,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,GAAG,MAAM,CAAC;QAC/B,CAAC;aAAM,CAAC;YACN,sBAAsB;YACtB,MAAM,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC;YACxB,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;YACrC,IAAI,CAAC,eAAe,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC;QACvC,CAAC;IACH,CAAC;IAEM,WAAW,CAAC,MAAe,EAAE,OAAoB;QACtD,sBAAsB;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YACrD,MAAM,SAAS,GAAG,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC;YAC1C,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;gBACtB,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACxC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;oBACjB,MAAM,IAAI,GAAG,CAAC,CAAC,GAAG,CAAC,CAAC;oBACpB,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;QACH,CAAC;QAED,oBAAoB;QACpB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAC9C,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACnC,IAAI,SAAS,IAAI,IAAI,EAAE,CAAC;gBACtB,MAAM,IAAI,GAAG,SAAS,CAAC,MAAM,EAAE,OAAO,CAAC,CAAC;gBACxC,IAAI,IAAI,IAAI,IAAI,EAAE,CAAC;oBACjB,MAAM,IAAI,GAAG,CAAC,CAAC;oBACf,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;gBACjC,CAAC;YACH,CAAC;QACH,CAAC;QAED,IAAI,MAAM,YAAY,OAAO,EAAE,CAAC;YAC9B,wBAAwB;YACxB,OAAO,MAAM,CAAC;QAChB,CAAC;QACD,OAAO,IAAI,CAAC;IACd,CAAC;IAEM,MAAM,CAAC,IAAgB,EAAE,IAAY,EAAE,OAAoB;QAChE,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QACnF,IAAI,SAAS,EAAE,CAAC;YACd,OAAO,SAAS,CAAC,IAAI,EAAE,IAAI,EAAE,OAAO,CAAC,CAAC;QACxC,CAAC;aAAM,CAAC;YACN,mDAAmD;YACnD,OAAO,IAAI,OAAO,CAAC,IAAI,EAAE,IAAI,CAAC,CAAC;QACjC,CAAC;IACH,CAAC;;AAhFsB,2BAAY,GAAkC,IAAI,cAAc,EAAE,AAAtD,CAAuD"}