{"version": 3, "file": "msgpack.min.js", "mappings": "CAAA,SAA2CA,EAAMC,GAC1B,iBAAZC,SAA0C,iBAAXC,OACxCA,OAAOD,QAAUD,IACQ,mBAAXG,QAAyBA,OAAOC,IAC9CD,OAAO,GAAIH,GACe,iBAAZC,QACdA,QAAqB,YAAID,IAEzBD,EAAkB,YAAIC,GACvB,CATD,CASGK,MAAM,I,mBCRT,IAAIC,EAAsB,CCA1BA,EAAwB,CAACL,EAASM,KACjC,IAAI,IAAIC,KAAOD,EACXD,EAAoBG,EAAEF,EAAYC,KAASF,EAAoBG,EAAER,EAASO,IAC5EE,OAAOC,eAAeV,EAASO,EAAK,CAAEI,YAAY,EAAMC,IAAKN,EAAWC,IAE1E,ECNDF,EAAwB,CAACQ,EAAKC,IAAUL,OAAOM,UAAUC,eAAeC,KAAKJ,EAAKC,GCClFT,EAAyBL,IACH,oBAAXkB,QAA0BA,OAAOC,aAC1CV,OAAOC,eAAeV,EAASkB,OAAOC,YAAa,CAAEC,MAAO,WAE7DX,OAAOC,eAAeV,EAAS,aAAc,CAAEoB,OAAO,GAAO,G,iYCqF9D,MAAMC,EAAoB,IAAIC,YAoBvB,SAASC,EAAaC,EAAmBC,EAAqBC,GACnE,IAAIC,EAASF,EACb,MAAMG,EAAMD,EAASD,EAEfG,EAAuB,GAC7B,IAAIC,EAAS,GACb,KAAOH,EAASC,GAAK,CACnB,MAAMG,EAAQP,EAAMG,KACpB,GAAa,IAARI,EAGE,GAAuB,MAAV,IAARA,GAAwB,CAElC,MAAMC,EAA2B,GAAnBR,EAAMG,KACpBE,EAAMI,MAAe,GAARF,IAAiB,EAAKC,EACrC,MAAO,GAAuB,MAAV,IAARD,GAAwB,CAElC,MAAMC,EAA2B,GAAnBR,EAAMG,KACdO,EAA2B,GAAnBV,EAAMG,KACpBE,EAAMI,MAAe,GAARF,IAAiB,GAAOC,GAAS,EAAKE,EACrD,MAAO,GAAuB,MAAV,IAARH,GAAwB,CAKlC,IAAII,GAAiB,EAARJ,IAAiB,IAHG,GAAnBP,EAAMG,OAG4B,IAFf,GAAnBH,EAAMG,OAE8C,EADjC,GAAnBH,EAAMG,KAEhBQ,EAAO,QACTA,GAAQ,MACRN,EAAMI,KAAOE,IAAS,GAAM,KAAS,OACrCA,EAAO,MAAiB,KAAPA,GAEnBN,EAAMI,KAAKE,EACb,MACEN,EAAMI,KAAKF,QAvBXF,EAAMI,KAAKF,GA0BTF,EAAMO,QAtCK,OAuCbN,GAAUO,OAAOC,gBAAgBT,GACjCA,EAAMO,OAAS,EAEnB,CAMA,OAJIP,EAAMO,OAAS,IACjBN,GAAUO,OAAOC,gBAAgBT,IAG5BC,CACT,CAEA,MAAMS,EAAoB,IAAIC,YC5JvB,MAAMC,EAIX,WAAAC,CAAYC,EAAcC,GACxBxC,KAAKuC,KAAOA,EACZvC,KAAKwC,KAAOA,CACd,ECVK,MAAMC,UAAoBC,MAC/B,WAAAJ,CAAYK,GACVC,MAAMD,GAGN,MAAME,EAAsCxC,OAAOyC,OAAOL,EAAY9B,WACtEN,OAAO0C,eAAe/C,KAAM6C,GAE5BxC,OAAOC,eAAeN,KAAM,OAAQ,CAClCgD,cAAc,EACdzC,YAAY,EACZS,MAAOyB,EAAYQ,MAEvB,ECXK,MAAMC,EAAa,WAYnB,SAASC,EAASC,EAAgB7B,EAAgBP,GACvD,MAAMqC,EAAOC,KAAKC,MAAMvC,EAAQ,YAC1BwC,EAAMxC,EACZoC,EAAKK,UAAUlC,EAAQ8B,GACvBD,EAAKK,UAAUlC,EAAS,EAAGiC,EAC7B,CAEO,SAASE,EAASN,EAAgB7B,GAGvC,OAAc,WAFD6B,EAAKO,SAASpC,GACf6B,EAAKQ,UAAUrC,EAAS,EAEtC,CCrBO,MAAMsC,GAAiB,EAOxBC,EAAsB,WACtBC,EAAsB,YAErB,SAASC,GAA0B,IAAEC,EAAG,KAAEC,IAC/C,GAAID,GAAO,GAAKC,GAAQ,GAAKD,GAAOF,EAAqB,CAEvD,GAAa,IAATG,GAAcD,GAAOH,EAAqB,CAE5C,MAAMK,EAAK,IAAIC,WAAW,GAG1B,OAFa,IAAIC,SAASF,EAAGG,QACxBb,UAAU,EAAGQ,GACXE,CACT,CAAO,CAEL,MAAMI,EAAUN,EAAM,WAChBO,EAAe,WAANP,EACTE,EAAK,IAAIC,WAAW,GACpBhB,EAAO,IAAIiB,SAASF,EAAGG,QAK7B,OAHAlB,EAAKK,UAAU,EAAIS,GAAQ,EAAgB,EAAVK,GAEjCnB,EAAKK,UAAU,EAAGe,GACXL,CACT,CACF,CAAO,CAEL,MAAMA,EAAK,IAAIC,WAAW,IACpBhB,EAAO,IAAIiB,SAASF,EAAGG,QAG7B,OAFAlB,EAAKK,UAAU,EAAGS,GAClBf,EAASC,EAAM,EAAGa,GACXE,CACT,CACF,CAEO,SAASM,EAAqBC,GACnC,MAAMC,EAAOD,EAAKE,UACZX,EAAMX,KAAKC,MAAMoB,EAAO,KACxBT,EAA4B,KAApBS,EAAa,IAANV,GAGfY,EAAYvB,KAAKC,MAAMW,EAAO,KACpC,MAAO,CACLD,IAAKA,EAAMY,EACXX,KAAMA,EAAmB,IAAZW,EAEjB,CAEO,SAASC,EAAyBC,GACvC,OAAIA,aAAkBC,KAEbhB,EADUS,EAAqBM,IAG/B,IAEX,CAEO,SAASE,EAA0BzC,GACxC,MAAMY,EAAO,IAAIiB,SAAS7B,EAAK8B,OAAQ9B,EAAK0C,WAAY1C,EAAKlB,YAG7D,OAAQkB,EAAKlB,YACX,KAAK,EAIH,MAAO,CAAE2C,IAFGb,EAAKQ,UAAU,GAEbM,KADD,GAGf,KAAK,EAAG,CAEN,MAAMiB,EAAoB/B,EAAKQ,UAAU,GAIzC,MAAO,CAAEK,IAF+B,YAAP,EAApBkB,GADI/B,EAAKQ,UAAU,GAGlBM,KADDiB,IAAsB,EAErC,CACA,KAAK,GAKH,MAAO,CAAElB,IAFGP,EAASN,EAAM,GAEbc,KADDd,EAAKQ,UAAU,IAG9B,QACE,MAAM,IAAInB,EAAY,gEAAgED,EAAKR,UAEjG,CAEO,SAASoD,EAAyB5C,GACvC,MAAM6C,EAAWJ,EAA0BzC,GAC3C,OAAO,IAAIwC,KAAoB,IAAfK,EAASpB,IAAYoB,EAASnB,KAAO,IACvD,CAEO,MAAMoB,EAAqB,CAChC/C,KAAMsB,EACN0B,OAAQT,EACRU,OAAQJ,GClFH,MAAMK,EAgBX,cAPiB,KAAAC,gBAA+E,GAC/E,KAAAC,gBAA+E,GAG/E,KAAAC,SAAwE,GACxE,KAAAC,SAAwE,GAGvF7F,KAAK8F,SAASR,EAChB,CAEO,QAAAQ,EAAS,KACdvD,EAAI,OACJgD,EAAM,OACNC,IAMA,GAAIjD,GAAQ,EAEVvC,KAAK4F,SAASrD,GAAQgD,EACtBvF,KAAK6F,SAAStD,GAAQiD,MACjB,CAEL,MAAMO,GAAS,EAAIxD,EACnBvC,KAAK0F,gBAAgBK,GAASR,EAC9BvF,KAAK2F,gBAAgBI,GAASP,CAChC,CACF,CAEO,WAAAQ,CAAYjB,EAAiBkB,GAElC,IAAK,IAAIC,EAAI,EAAGA,EAAIlG,KAAK0F,gBAAgB1D,OAAQkE,IAAK,CACpD,MAAMC,EAAYnG,KAAK0F,gBAAgBQ,GACvC,GAAiB,MAAbC,EAAmB,CACrB,MAAM3D,EAAO2D,EAAUpB,EAAQkB,GAC/B,GAAY,MAARzD,EAEF,OAAO,IAAIH,GADG,EAAI6D,EACO1D,EAE7B,CACF,CAGA,IAAK,IAAI0D,EAAI,EAAGA,EAAIlG,KAAK4F,SAAS5D,OAAQkE,IAAK,CAC7C,MAAMC,EAAYnG,KAAK4F,SAASM,GAChC,GAAiB,MAAbC,EAAmB,CACrB,MAAM3D,EAAO2D,EAAUpB,EAAQkB,GAC/B,GAAY,MAARzD,EAEF,OAAO,IAAIH,EADE6D,EACY1D,EAE7B,CACF,CAEA,OAAIuC,aAAkB1C,EAEb0C,EAEF,IACT,CAEO,MAAAS,CAAOhD,EAAkBD,EAAc0D,GAC5C,MAAMG,EAAY7D,EAAO,EAAIvC,KAAK2F,iBAAiB,EAAIpD,GAAQvC,KAAK6F,SAAStD,GAC7E,OAAI6D,EACKA,EAAU5D,EAAMD,EAAM0D,GAGtB,IAAI5D,EAAQE,EAAMC,EAE7B,ECnGK,SAAS6D,EACd/B,GAEA,OAAIA,aAAkBF,WACbE,EACEgC,YAAYC,OAAOjC,GACrB,IAAIF,WAAWE,EAAOA,OAAQA,EAAOY,WAAYZ,EAAOhD,YAZnE,SAA2BgD,GACzB,OACEA,aAAkBgC,aAA6C,oBAAtBE,mBAAqClC,aAAkBkC,iBAEpG,CASaC,CAAkBnC,GACpB,IAAIF,WAAWE,GAGfF,WAAWsC,KAAKpC,EAE3B,CDMyB,EAAAqC,aAA8C,IAAIlB,EEgDpE,MAAMmB,EAiBX,YAAmBC,GAFX,KAAAC,SAAU,EAGhB9G,KAAK+G,eAAiBF,GAASE,gBAAmBtB,EAAekB,aACjE3G,KAAKiG,QAAWY,GAAkDZ,QAElEjG,KAAKgH,YAAcH,GAASG,cAAe,EAC3ChH,KAAKiH,SAAWJ,GAASI,UAxFI,IAyF7BjH,KAAKkH,kBAAoBL,GAASK,mBAxFK,KAyFvClH,KAAKmH,SAAWN,GAASM,WAAY,EACrCnH,KAAKoH,aAAeP,GAASO,eAAgB,EAC7CpH,KAAKqH,gBAAkBR,GAASQ,kBAAmB,EACnDrH,KAAKsH,oBAAsBT,GAASS,sBAAuB,EAE3DtH,KAAKuH,IAAM,EACXvH,KAAKoD,KAAO,IAAIiB,SAAS,IAAIiC,YAAYtG,KAAKkH,oBAC9ClH,KAAKoB,MAAQ,IAAIgD,WAAWpE,KAAKoD,KAAKkB,OACxC,CAEQ,KAAAkD,GAIN,OAAO,IAAIZ,EAAqB,CAC9BG,eAAgB/G,KAAK+G,eACrBd,QAASjG,KAAKiG,QACde,YAAahH,KAAKgH,YAClBC,SAAUjH,KAAKiH,SACfC,kBAAmBlH,KAAKkH,kBACxBC,SAAUnH,KAAKmH,SACfC,aAAcpH,KAAKoH,aACnBC,gBAAiBrH,KAAKqH,gBACtBC,oBAAqBtH,KAAKsH,qBAE9B,CAEQ,iBAAAG,GACNzH,KAAKuH,IAAM,CACb,CAOO,eAAAG,CAAgB3C,GACrB,GAAI/E,KAAK8G,QAEP,OADiB9G,KAAKwH,QACNE,gBAAgB3C,GAGlC,IAKE,OAJA/E,KAAK8G,SAAU,EAEf9G,KAAKyH,oBACLzH,KAAK2H,SAAS5C,EAAQ,GACf/E,KAAKoB,MAAMwG,SAAS,EAAG5H,KAAKuH,IACrC,C,QACEvH,KAAK8G,SAAU,CACjB,CACF,CAKO,MAAAvB,CAAOR,GACZ,GAAI/E,KAAK8G,QAEP,OADiB9G,KAAKwH,QACNjC,OAAOR,GAGzB,IAKE,OAJA/E,KAAK8G,SAAU,EAEf9G,KAAKyH,oBACLzH,KAAK2H,SAAS5C,EAAQ,GACf/E,KAAKoB,MAAMyG,MAAM,EAAG7H,KAAKuH,IAClC,C,QACEvH,KAAK8G,SAAU,CACjB,CACF,CAEQ,QAAAa,CAAS5C,EAAiB+C,GAChC,GAAIA,EAAQ9H,KAAKiH,SACf,MAAM,IAAIvE,MAAM,6BAA6BoF,KAGjC,MAAV/C,EACF/E,KAAK+H,YACsB,kBAAXhD,EAChB/E,KAAKgI,cAAcjD,GACQ,iBAAXA,EACX/E,KAAKsH,oBAGRtH,KAAKiI,oBAAoBlD,GAFzB/E,KAAKkI,aAAanD,GAIO,iBAAXA,EAChB/E,KAAKmI,aAAapD,GACT/E,KAAKgH,aAAiC,iBAAXjC,EACpC/E,KAAKoI,eAAerD,GAEpB/E,KAAKqI,aAAatD,EAAQ+C,EAE9B,CAEQ,uBAAAQ,CAAwBC,GAC9B,MAAMC,EAAexI,KAAKuH,IAAMgB,EAE5BvI,KAAKoD,KAAK9B,WAAakH,GACzBxI,KAAKyI,aAA4B,EAAfD,EAEtB,CAEQ,YAAAC,CAAaC,GACnB,MAAMC,EAAY,IAAIrC,YAAYoC,GAC5BE,EAAW,IAAIxE,WAAWuE,GAC1BE,EAAU,IAAIxE,SAASsE,GAE7BC,EAASE,IAAI9I,KAAKoB,OAElBpB,KAAKoD,KAAOyF,EACZ7I,KAAKoB,MAAQwH,CACf,CAEQ,SAAAb,GACN/H,KAAK+I,QAAQ,IACf,CAEQ,aAAAf,CAAcjD,IACL,IAAXA,EACF/E,KAAK+I,QAAQ,KAEb/I,KAAK+I,QAAQ,IAEjB,CAEQ,YAAAb,CAAanD,IACd/E,KAAKsH,qBAAuB0B,OAAOC,cAAclE,GAChDA,GAAU,EACRA,EAAS,IAEX/E,KAAK+I,QAAQhE,GACJA,EAAS,KAElB/E,KAAK+I,QAAQ,KACb/I,KAAK+I,QAAQhE,IACJA,EAAS,OAElB/E,KAAK+I,QAAQ,KACb/I,KAAKkJ,SAASnE,IACLA,EAAS,YAElB/E,KAAK+I,QAAQ,KACb/I,KAAKmJ,SAASpE,IACJ/E,KAAKgH,YAKfhH,KAAKiI,oBAAoBlD,IAHzB/E,KAAK+I,QAAQ,KACb/I,KAAKoJ,SAASrE,IAKZA,IAAW,GAEb/E,KAAK+I,QAAQ,IAAQhE,EAAS,IACrBA,IAAW,KAEpB/E,KAAK+I,QAAQ,KACb/I,KAAKqJ,QAAQtE,IACJA,IAAW,OAEpB/E,KAAK+I,QAAQ,KACb/I,KAAKsJ,SAASvE,IACLA,IAAW,YAEpB/E,KAAK+I,QAAQ,KACb/I,KAAKuJ,SAASxE,IACJ/E,KAAKgH,YAKfhH,KAAKiI,oBAAoBlD,IAHzB/E,KAAK+I,QAAQ,KACb/I,KAAKwJ,SAASzE,IAMlB/E,KAAKiI,oBAAoBlD,EAE7B,CAEQ,mBAAAkD,CAAoBlD,GACtB/E,KAAKoH,cAEPpH,KAAK+I,QAAQ,KACb/I,KAAKyJ,SAAS1E,KAGd/E,KAAK+I,QAAQ,KACb/I,KAAK0J,SAAS3E,GAElB,CAEQ,cAAAqD,CAAerD,GACjBA,GAAU4E,OAAO,IAEnB3J,KAAK+I,QAAQ,KACb/I,KAAK4J,eAAe7E,KAGpB/E,KAAK+I,QAAQ,KACb/I,KAAK6J,cAAc9E,GAEvB,CAEQ,iBAAA+E,CAAkBxI,GACxB,GAAIA,EAAa,GAEftB,KAAK+I,QAAQ,IAAOzH,QACf,GAAIA,EAAa,IAEtBtB,KAAK+I,QAAQ,KACb/I,KAAK+I,QAAQzH,QACR,GAAIA,EAAa,MAEtBtB,KAAK+I,QAAQ,KACb/I,KAAKkJ,SAAS5H,OACT,MAAIA,EAAa,YAKtB,MAAM,IAAIoB,MAAM,oBAAoBpB,oBAHpCtB,KAAK+I,QAAQ,KACb/I,KAAKmJ,SAAS7H,EAGhB,CACF,CAEQ,YAAA6G,CAAapD,GACnB,MAEMzD,EPvUH,SAAmByI,GACxB,MAAMC,EAAYD,EAAI/H,OAEtB,IAAIV,EAAa,EACbiG,EAAM,EACV,KAAOA,EAAMyC,GAAW,CACtB,IAAIhJ,EAAQ+I,EAAIE,WAAW1C,KAE3B,GAAa,WAARvG,EAIE,GAAa,WAARA,EAGL,CAEL,GAAIA,GAAS,OAAUA,GAAS,OAE1BuG,EAAMyC,EAAW,CACnB,MAAME,EAAQH,EAAIE,WAAW1C,GACJ,QAAZ,MAAR2C,OACD3C,EACFvG,IAAkB,KAARA,IAAkB,KAAe,KAARkJ,GAAiB,MAExD,CAQA5I,GALW,WAARN,EAKW,EAHA,CAKlB,MArBEM,GAAc,OAJdA,GA0BJ,CACA,OAAOA,CACT,COiSuB6I,CAAUpF,GPnO1B,IAAoBgF,EAAaK,EAAoBC,EOoOxDrK,KAAKsI,wBAHiB,EAGuBhH,GAC7CtB,KAAK8J,kBAAkBxI,GPrOAyI,EOsOZhF,EPtOyBqF,EOsOjBpK,KAAKoB,MPtOgCiJ,EOsOzBrK,KAAKuH,IPrOlCwC,EAAI/H,OAPqB,GAExB,SAAsB+H,EAAaK,EAAoBC,GAC5DpJ,EAAkBqJ,WAAWP,EAAKK,EAAOxC,SAASyC,GACpD,CAIIE,CAAaR,EAAKK,EAAQC,GA9DvB,SAAsBN,EAAaK,EAAoBC,GAC5D,MAAML,EAAYD,EAAI/H,OACtB,IAAIT,EAAS8I,EACT9C,EAAM,EACV,KAAOA,EAAMyC,GAAW,CACtB,IAAIhJ,EAAQ+I,EAAIE,WAAW1C,KAE3B,GAAa,WAARvG,EAAL,CAIO,GAAa,WAARA,EAGL,CAEL,GAAIA,GAAS,OAAUA,GAAS,OAE1BuG,EAAMyC,EAAW,CACnB,MAAME,EAAQH,EAAIE,WAAW1C,GACJ,QAAZ,MAAR2C,OACD3C,EACFvG,IAAkB,KAARA,IAAkB,KAAe,KAARkJ,GAAiB,MAExD,CAGW,WAARlJ,GAMHoJ,EAAO7I,KAAcP,GAAS,GAAM,EAAQ,IAC5CoJ,EAAO7I,KAAcP,GAAS,GAAM,GAAQ,IAC5CoJ,EAAO7I,KAAcP,GAAS,EAAK,GAAQ,MAN3CoJ,EAAO7I,KAAcP,GAAS,GAAM,GAAQ,IAC5CoJ,EAAO7I,KAAcP,GAAS,EAAK,GAAQ,IAO/C,MAxBEoJ,EAAO7I,KAAcP,GAAS,EAAK,GAAQ,IA0B7CoJ,EAAO7I,KAAqB,GAARP,EAAgB,GAFpC,MA5BEoJ,EAAO7I,KAAYP,CA+BvB,CACF,CAuBIwJ,CAAaT,EAAKK,EAAQC,GOmO1BrK,KAAKuH,KAAOjG,CACd,CAEQ,YAAA+G,CAAatD,EAAiB+C,GAEpC,MAAM2C,EAAMzK,KAAK+G,eAAef,YAAYjB,EAAQ/E,KAAKiG,SACzD,GAAW,MAAPwE,EACFzK,KAAK0K,gBAAgBD,QAChB,GAAIE,MAAMC,QAAQ7F,GACvB/E,KAAK6K,YAAY9F,EAAQ+C,QACpB,GAAIxB,YAAYC,OAAOxB,GAC5B/E,KAAK8K,aAAa/F,OACb,IAAsB,iBAAXA,EAIhB,MAAM,IAAIrC,MAAM,wBAAwBrC,OAAOM,UAAUoK,SAASC,MAAMjG,MAHxE/E,KAAKiL,UAAUlG,EAAmC+C,EAIpD,CACF,CAEQ,YAAAgD,CAAa/F,GACnB,MAAMmG,EAAOnG,EAAOzD,WACpB,GAAI4J,EAAO,IAETlL,KAAK+I,QAAQ,KACb/I,KAAK+I,QAAQmC,QACR,GAAIA,EAAO,MAEhBlL,KAAK+I,QAAQ,KACb/I,KAAKkJ,SAASgC,OACT,MAAIA,EAAO,YAKhB,MAAM,IAAIxI,MAAM,qBAAqBwI,KAHrClL,KAAK+I,QAAQ,KACb/I,KAAKmJ,SAAS+B,EAGhB,CACA,MAAM9J,EAAQiF,EAAiBtB,GAC/B/E,KAAKmL,SAAS/J,EAChB,CAEQ,WAAAyJ,CAAY9F,EAAwB+C,GAC1C,MAAMoD,EAAOnG,EAAO/C,OACpB,GAAIkJ,EAAO,GAETlL,KAAK+I,QAAQ,IAAOmC,QACf,GAAIA,EAAO,MAEhBlL,KAAK+I,QAAQ,KACb/I,KAAKkJ,SAASgC,OACT,MAAIA,EAAO,YAKhB,MAAM,IAAIxI,MAAM,oBAAoBwI,KAHpClL,KAAK+I,QAAQ,KACb/I,KAAKmJ,SAAS+B,EAGhB,CACA,IAAK,MAAME,KAAQrG,EACjB/E,KAAK2H,SAASyD,EAAMtD,EAAQ,EAEhC,CAEQ,qBAAAuD,CAAsBtG,EAAiCuG,GAC7D,IAAIC,EAAQ,EAEZ,IAAK,MAAMpL,KAAOmL,OACIE,IAAhBzG,EAAO5E,IACToL,IAIJ,OAAOA,CACT,CAEQ,SAAAN,CAAUlG,EAAiC+C,GACjD,MAAMwD,EAAOjL,OAAOiL,KAAKvG,GACrB/E,KAAKmH,UACPmE,EAAKG,OAGP,MAAMP,EAAOlL,KAAKqH,gBAAkBrH,KAAKqL,sBAAsBtG,EAAQuG,GAAQA,EAAKtJ,OAEpF,GAAIkJ,EAAO,GAETlL,KAAK+I,QAAQ,IAAOmC,QACf,GAAIA,EAAO,MAEhBlL,KAAK+I,QAAQ,KACb/I,KAAKkJ,SAASgC,OACT,MAAIA,EAAO,YAKhB,MAAM,IAAIxI,MAAM,yBAAyBwI,KAHzClL,KAAK+I,QAAQ,KACb/I,KAAKmJ,SAAS+B,EAGhB,CAEA,IAAK,MAAM/K,KAAOmL,EAAM,CACtB,MAAMtK,EAAQ+D,EAAO5E,GAEfH,KAAKqH,sBAA6BmE,IAAVxK,IAC5BhB,KAAKmI,aAAahI,GAClBH,KAAK2H,SAAS3G,EAAO8G,EAAQ,GAEjC,CACF,CAEQ,eAAA4C,CAAgBD,GACtB,GAAwB,mBAAbA,EAAIjI,KAAqB,CAClC,MAAMA,EAAOiI,EAAIjI,KAAKxC,KAAKuH,IAAM,GAC3B2D,EAAO1I,EAAKR,OAElB,GAAIkJ,GAAQ,WACV,MAAM,IAAIxI,MAAM,+BAA+BwI,KAOjD,OAJAlL,KAAK+I,QAAQ,KACb/I,KAAKmJ,SAAS+B,GACdlL,KAAKqJ,QAAQoB,EAAIlI,WACjBvC,KAAKmL,SAAS3I,EAEhB,CAEA,MAAM0I,EAAOT,EAAIjI,KAAKR,OACtB,GAAa,IAATkJ,EAEFlL,KAAK+I,QAAQ,UACR,GAAa,IAATmC,EAETlL,KAAK+I,QAAQ,UACR,GAAa,IAATmC,EAETlL,KAAK+I,QAAQ,UACR,GAAa,IAATmC,EAETlL,KAAK+I,QAAQ,UACR,GAAa,KAATmC,EAETlL,KAAK+I,QAAQ,UACR,GAAImC,EAAO,IAEhBlL,KAAK+I,QAAQ,KACb/I,KAAK+I,QAAQmC,QACR,GAAIA,EAAO,MAEhBlL,KAAK+I,QAAQ,KACb/I,KAAKkJ,SAASgC,OACT,MAAIA,EAAO,YAKhB,MAAM,IAAIxI,MAAM,+BAA+BwI,KAH/ClL,KAAK+I,QAAQ,KACb/I,KAAKmJ,SAAS+B,EAGhB,CACAlL,KAAKqJ,QAAQoB,EAAIlI,MACjBvC,KAAKmL,SAASV,EAAIjI,KACpB,CAEQ,OAAAuG,CAAQ/H,GACdhB,KAAKsI,wBAAwB,GAE7BtI,KAAKoD,KAAKsI,SAAS1L,KAAKuH,IAAKvG,GAC7BhB,KAAKuH,KACP,CAEQ,QAAA4D,CAASQ,GACf,MAAMT,EAAOS,EAAO3J,OACpBhC,KAAKsI,wBAAwB4C,GAE7BlL,KAAKoB,MAAM0H,IAAI6C,EAAQ3L,KAAKuH,KAC5BvH,KAAKuH,KAAO2D,CACd,CAEQ,OAAA7B,CAAQrI,GACdhB,KAAKsI,wBAAwB,GAE7BtI,KAAKoD,KAAKwI,QAAQ5L,KAAKuH,IAAKvG,GAC5BhB,KAAKuH,KACP,CAEQ,QAAA2B,CAASlI,GACfhB,KAAKsI,wBAAwB,GAE7BtI,KAAKoD,KAAKyI,UAAU7L,KAAKuH,IAAKvG,GAC9BhB,KAAKuH,KAAO,CACd,CAEQ,QAAA+B,CAAStI,GACfhB,KAAKsI,wBAAwB,GAE7BtI,KAAKoD,KAAK0I,SAAS9L,KAAKuH,IAAKvG,GAC7BhB,KAAKuH,KAAO,CACd,CAEQ,QAAA4B,CAASnI,GACfhB,KAAKsI,wBAAwB,GAE7BtI,KAAKoD,KAAKK,UAAUzD,KAAKuH,IAAKvG,GAC9BhB,KAAKuH,KAAO,CACd,CAEQ,QAAAgC,CAASvI,GACfhB,KAAKsI,wBAAwB,GAE7BtI,KAAKoD,KAAK2I,SAAS/L,KAAKuH,IAAKvG,GAC7BhB,KAAKuH,KAAO,CACd,CAEQ,QAAAkC,CAASzI,GACfhB,KAAKsI,wBAAwB,GAE7BtI,KAAKoD,KAAK4I,WAAWhM,KAAKuH,IAAKvG,GAC/BhB,KAAKuH,KAAO,CACd,CAEQ,QAAAmC,CAAS1I,GACfhB,KAAKsI,wBAAwB,GAE7BtI,KAAKoD,KAAK6I,WAAWjM,KAAKuH,IAAKvG,GAC/BhB,KAAKuH,KAAO,CACd,CAEQ,QAAA6B,CAASpI,GACfhB,KAAKsI,wBAAwB,GJniB1B,SAAmBlF,EAAgB7B,EAAgBP,GACxD,MAAMqC,EAAOrC,EAAQ,WACfwC,EAAMxC,EACZoC,EAAKK,UAAUlC,EAAQ8B,GACvBD,EAAKK,UAAUlC,EAAS,EAAGiC,EAC7B,CIgiBI0I,CAAUlM,KAAKoD,KAAMpD,KAAKuH,IAAKvG,GAC/BhB,KAAKuH,KAAO,CACd,CAEQ,QAAAiC,CAASxI,GACfhB,KAAKsI,wBAAwB,GAE7BnF,EAASnD,KAAKoD,KAAMpD,KAAKuH,IAAKvG,GAC9BhB,KAAKuH,KAAO,CACd,CAEQ,cAAAqC,CAAe5I,GACrBhB,KAAKsI,wBAAwB,GAE7BtI,KAAKoD,KAAK+I,aAAanM,KAAKuH,IAAKvG,GACjChB,KAAKuH,KAAO,CACd,CAEQ,aAAAsC,CAAc7I,GACpBhB,KAAKsI,wBAAwB,GAE7BtI,KAAKoD,KAAKgJ,YAAYpM,KAAKuH,IAAKvG,GAChChB,KAAKuH,KAAO,CACd,ECzjBK,SAAShC,EACdvE,EACA6F,GAGA,OADgB,IAAID,EAAQC,GACba,gBAAgB1G,EACjC,CChBO,SAASqL,EAAWC,GACzB,MAAO,GAAGA,EAAO,EAAI,IAAM,OAAOhJ,KAAKiJ,IAAID,GAAMvB,SAAS,IAAIyB,SAAS,EAAG,MAC5E,CC8EA,MAAMC,EAAc,QACdC,EAAgB,UAChBC,EAAkB,YAIlBC,EAAmBzM,IACvB,GAAmB,iBAARA,GAAmC,iBAARA,EACpC,OAAOA,EAET,MAAM,IAAIsC,EAAY,uDAAyDtC,EAAI,EAkBrF,MAAM0M,EAAN,cACmB,KAAAC,MAA2B,GACpC,KAAAC,mBAAqB,CA8E/B,CA5EE,UAAW/K,GACT,OAAOhC,KAAK+M,kBAAoB,CAClC,CAEO,GAAAC,GACL,OAAOhN,KAAK8M,MAAM9M,KAAK+M,kBACzB,CAEO,cAAAE,CAAe/B,GACpB,MAAMgC,EAAQlN,KAAKmN,gCAEnBD,EAAM3K,KAAOkK,EACbS,EAAME,SAAW,EACjBF,EAAMhC,KAAOA,EACbgC,EAAMG,MAAQ,IAAI1C,MAAMO,EAC1B,CAEO,YAAAoC,CAAapC,GAClB,MAAMgC,EAAQlN,KAAKmN,gCAEnBD,EAAM3K,KAAOmK,EACbQ,EAAMK,UAAY,EAClBL,EAAMhC,KAAOA,EACbgC,EAAMM,IAAM,CAAC,CACf,CAEQ,6BAAAL,GAGN,GAFAnN,KAAK+M,oBAED/M,KAAK+M,oBAAsB/M,KAAK8M,MAAM9K,OAAQ,CAChD,MAAMyL,EAAoC,CACxClL,UAAMiJ,EACNN,KAAM,EACNmC,WAAO7B,EACP4B,SAAU,EACVG,UAAW,EACXC,SAAKhC,EACLrL,IAAK,MAGPH,KAAK8M,MAAMjL,KAAK4L,EAClB,CAEA,OAAOzN,KAAK8M,MAAM9M,KAAK+M,kBACzB,CAEO,OAAAW,CAAQR,GAGb,GAFsBlN,KAAK8M,MAAM9M,KAAK+M,qBAEhBG,EACpB,MAAM,IAAIxK,MAAM,mEAGlB,GAAIwK,EAAM3K,OAASkK,EAAa,CAC9B,MAAMgB,EAAeP,EACrBO,EAAavC,KAAO,EACpBuC,EAAaJ,WAAQ7B,EACrBiC,EAAaL,SAAW,EACxBK,EAAalL,UAAOiJ,CACtB,CAEA,GAAI0B,EAAM3K,OAASmK,GAAiBQ,EAAM3K,OAASoK,EAAiB,CAClE,MAAMc,EAAeP,EACrBO,EAAavC,KAAO,EACpBuC,EAAaD,SAAMhC,EACnBiC,EAAaF,UAAY,EACzBE,EAAalL,UAAOiJ,CACtB,CAEAxL,KAAK+M,mBACP,CAEO,KAAAY,GACL3N,KAAK8M,MAAM9K,OAAS,EACpBhC,KAAK+M,mBAAqB,CAC5B,EAKF,MAEMa,EAAa,IAAIvJ,SAA0B,IAAIiC,YAAY,IAC3DuH,EAAc,IAAIzJ,WAA4BwJ,EAAWtJ,QAE/D,IAGEsJ,EAAWE,QAAQ,EACrB,CAAE,MAAOC,GACP,KAAMA,aAAaC,YACjB,MAAM,IAAItL,MACR,mIAGN,CAEA,MAAMuL,EAAY,IAAID,WAAW,qBAE3BE,EAAyB,ICrMxB,MAOL,WAAA5L,CAAY6L,EAnBiB,GAmBsBC,EAlBlB,IAYjC,KAAAC,IAAM,EACN,KAAAC,KAAO,EAMLtO,KAAKmO,aAAeA,EACpBnO,KAAKoO,gBAAkBA,EAIvBpO,KAAKuO,OAAS,GACd,IAAK,IAAIrI,EAAI,EAAGA,EAAIlG,KAAKmO,aAAcjI,IACrClG,KAAKuO,OAAO1M,KAAK,GAErB,CAEO,WAAA2M,CAAYlN,GACjB,OAAOA,EAAa,GAAKA,GAActB,KAAKmO,YAC9C,CAEQ,IAAAM,CAAKrN,EAAmBC,EAAqBC,GACnD,MAAMoN,EAAU1O,KAAKuO,OAAOjN,EAAa,GAEzCqN,EAAY,IAAK,MAAMC,KAAUF,EAAS,CACxC,MAAMG,EAAcD,EAAOxN,MAE3B,IAAK,IAAI0N,EAAI,EAAGA,EAAIxN,EAAYwN,IAC9B,GAAID,EAAYC,KAAO1N,EAAMC,EAAcyN,GACzC,SAASH,EAGb,OAAOC,EAAO7E,GAChB,CACA,OAAO,IACT,CAEQ,KAAAgF,CAAM3N,EAAmBJ,GAC/B,MAAM0N,EAAU1O,KAAKuO,OAAOnN,EAAMY,OAAS,GACrC4M,EAAyB,CAAExN,QAAO2I,IAAK/I,GAEzC0N,EAAQ1M,QAAUhC,KAAKoO,gBAGzBM,EAASpL,KAAK0L,SAAWN,EAAQ1M,OAAU,GAAK4M,EAEhDF,EAAQ7M,KAAK+M,EAEjB,CAEO,MAAApJ,CAAOpE,EAAmBC,EAAqBC,GACpD,MAAM2N,EAAcjP,KAAKyO,KAAKrN,EAAOC,EAAaC,GAClD,GAAmB,MAAf2N,EAEF,OADAjP,KAAKqO,MACEY,EAETjP,KAAKsO,OAEL,MAAMvE,EAAM5I,EAAaC,EAAOC,EAAaC,GAEvC4N,EAAoB9K,WAAWzD,UAAUkH,MAAMhH,KAAKO,EAAOC,EAAaA,EAAcC,GAE5F,OADAtB,KAAK+O,MAAMG,EAAmBnF,GACvBA,CACT,GDsIK,MAAMoF,EAuBX,YAAmBtI,GAVX,KAAAuI,SAAW,EACX,KAAA7H,IAAM,EAEN,KAAAnE,KAAOwK,EACP,KAAAxM,MAAQyM,EACR,KAAAwB,UAvCiB,EAwCR,KAAAvC,MAAQ,IAAID,EAErB,KAAA/F,SAAU,EAGhB9G,KAAK+G,eAAiBF,GAASE,gBAAmBtB,EAAekB,aACjE3G,KAAKiG,QAAWY,GAAkDZ,QAElEjG,KAAKgH,YAAcH,GAASG,cAAe,EAC3ChH,KAAKsP,WAAazI,GAASyI,aAAc,EACzCtP,KAAKuP,aAAe1I,GAAS0I,cAAgBrM,EAC7ClD,KAAKwP,aAAe3I,GAAS2I,cAAgBtM,EAC7ClD,KAAKyP,eAAiB5I,GAAS4I,gBAAkBvM,EACjDlD,KAAK0P,aAAe7I,GAAS6I,cAAgBxM,EAC7ClD,KAAK2P,aAAe9I,GAAS8I,cAAgBzM,EAC7ClD,KAAK4P,gBAAqCpE,IAAxB3E,GAAS+I,WAA2B/I,EAAQ+I,WAAa1B,EAC3ElO,KAAK4M,gBAAkB/F,GAAS+F,iBAAmBA,CACrD,CAEQ,KAAApF,GAEN,OAAO,IAAI2H,EAAQ,CACjBpI,eAAgB/G,KAAK+G,eACrBd,QAASjG,KAAKiG,QACde,YAAahH,KAAKgH,YAClBsI,WAAYtP,KAAKsP,WACjBC,aAAcvP,KAAKuP,aACnBC,aAAcxP,KAAKwP,aACnBC,eAAgBzP,KAAKyP,eACrBC,aAAc1P,KAAK0P,aACnBC,aAAc3P,KAAK2P,aACnBC,WAAY5P,KAAK4P,YAErB,CAEQ,iBAAAnI,GACNzH,KAAKoP,SAAW,EAChBpP,KAAKqP,UA7EkB,EA8EvBrP,KAAK8M,MAAMa,OAGb,CAEQ,SAAAkC,CAAUvL,GAChB,MAAMlD,EAAQiF,EAAiB/B,GAC/BtE,KAAKoB,MAAQA,EACbpB,KAAKoD,KAAO,IAAIiB,SAASjD,EAAMkD,OAAQlD,EAAM8D,WAAY9D,EAAME,YAC/DtB,KAAKuH,IAAM,CACb,CAEQ,YAAAuI,CAAaxL,GACnB,IA3FuB,IA2FnBtE,KAAKqP,UAAoCrP,KAAK+P,aAAa,GAExD,CACL,MAAMC,EAAgBhQ,KAAKoB,MAAMwG,SAAS5H,KAAKuH,KACzC0I,EAAU5J,EAAiB/B,GAG3BqE,EAAY,IAAIvE,WAAW4L,EAAchO,OAASiO,EAAQjO,QAChE2G,EAAUG,IAAIkH,GACdrH,EAAUG,IAAImH,EAASD,EAAchO,QACrChC,KAAK6P,UAAUlH,EACjB,MAVE3I,KAAK6P,UAAUvL,EAWnB,CAEQ,YAAAyL,CAAa7E,GACnB,OAAOlL,KAAKoD,KAAK9B,WAAatB,KAAKuH,KAAO2D,CAC5C,CAEQ,oBAAAgF,CAAqBC,GAC3B,MAAM,KAAE/M,EAAI,IAAEmE,GAAQvH,KACtB,OAAO,IAAIgO,WAAW,SAAS5K,EAAK9B,WAAaiG,QAAUnE,EAAK9B,sCAAsC6O,KACxG,CAMO,MAAA3K,CAAOlB,GACZ,GAAItE,KAAK8G,QAEP,OADiB9G,KAAKwH,QACNhC,OAAOlB,GAGzB,IACEtE,KAAK8G,SAAU,EAEf9G,KAAKyH,oBACLzH,KAAK6P,UAAUvL,GAEf,MAAMS,EAAS/E,KAAKoQ,eACpB,GAAIpQ,KAAK+P,aAAa,GACpB,MAAM/P,KAAKkQ,qBAAqBlQ,KAAKuH,KAEvC,OAAOxC,CACT,C,QACE/E,KAAK8G,SAAU,CACjB,CACF,CAEO,YAACuJ,CAAY/L,GAClB,GAAItE,KAAK8G,QAAT,CACE,MAAMwJ,EAAWtQ,KAAKwH,cACf8I,EAASD,YAAY/L,EAE9B,MAEA,IAME,IALAtE,KAAK8G,SAAU,EAEf9G,KAAKyH,oBACLzH,KAAK6P,UAAUvL,GAERtE,KAAK+P,aAAa,UACjB/P,KAAKoQ,cAEf,C,QACEpQ,KAAK8G,SAAU,CACjB,CACF,CAEO,iBAAMyJ,CAAYC,GACvB,GAAIxQ,KAAK8G,QAEP,OADiB9G,KAAKwH,QACN+I,YAAYC,GAG9B,IACExQ,KAAK8G,SAAU,EAEf,IACI/B,EADA0L,GAAU,EAEd,UAAW,MAAMnM,KAAUkM,EAAQ,CACjC,GAAIC,EAEF,MADAzQ,KAAK8G,SAAU,EACT9G,KAAKkQ,qBAAqBlQ,KAAKoP,UAGvCpP,KAAK8P,aAAaxL,GAElB,IACES,EAAS/E,KAAKoQ,eACdK,GAAU,CACZ,CAAE,MAAO1C,GACP,KAAMA,aAAaC,YACjB,MAAMD,CAGV,CACA/N,KAAKoP,UAAYpP,KAAKuH,GACxB,CAEA,GAAIkJ,EAAS,CACX,GAAIzQ,KAAK+P,aAAa,GACpB,MAAM/P,KAAKkQ,qBAAqBlQ,KAAKoP,UAEvC,OAAOrK,CACT,CAEA,MAAM,SAAEsK,EAAQ,IAAE9H,EAAG,SAAE6H,GAAapP,KACpC,MAAM,IAAIgO,WACR,gCAAgC3B,EAAWgD,SAAgBD,MAAa7H,2BAE5E,C,QACEvH,KAAK8G,SAAU,CACjB,CACF,CAEO,iBAAA4J,CACLF,GAEA,OAAOxQ,KAAK2Q,iBAAiBH,GAAQ,EACvC,CAEO,YAAAI,CAAaJ,GAClB,OAAOxQ,KAAK2Q,iBAAiBH,GAAQ,EACvC,CAEQ,sBAAOG,CAAiBH,EAA8E5F,GAC5G,GAAI5K,KAAK8G,QAAT,CACE,MAAMwJ,EAAWtQ,KAAKwH,cACf8I,EAASK,iBAAiBH,EAAQ5F,EAE3C,MAEA,IACE5K,KAAK8G,SAAU,EAEf,IAAI+J,EAAwBjG,EACxBkG,GAAkB,EAEtB,UAAW,MAAMxM,KAAUkM,EAAQ,CACjC,GAAI5F,GAA8B,IAAnBkG,EACb,MAAM9Q,KAAKkQ,qBAAqBlQ,KAAKoP,UAGvCpP,KAAK8P,aAAaxL,GAEduM,IACFC,EAAiB9Q,KAAK+Q,gBACtBF,GAAwB,EACxB7Q,KAAKgR,YAGP,IACE,WACQhR,KAAKoQ,eACc,KAAnBU,IAIV,CAAE,MAAO/C,GACP,KAAMA,aAAaC,YACjB,MAAMD,CAGV,CACA/N,KAAKoP,UAAYpP,KAAKuH,GACxB,CACF,C,QACEvH,KAAK8G,SAAU,CACjB,CACF,CAEQ,YAAAsJ,GACNa,EAAQ,OAAa,CACnB,MAAM5B,EAAWrP,KAAKkR,eACtB,IAAInM,EAEJ,GAAIsK,GAAY,IAEdtK,EAASsK,EAAW,SACf,GAAIA,EAAW,IACpB,GAAIA,EAAW,IAEbtK,EAASsK,OACJ,GAAIA,EAAW,IAAM,CAE1B,MAAMnE,EAAOmE,EAAW,IACxB,GAAa,IAATnE,EAAY,CACdlL,KAAKsN,aAAapC,GAClBlL,KAAKgR,WACL,SAASC,CACX,CACElM,EAAS,CAAC,CAEd,MAAO,GAAIsK,EAAW,IAAM,CAE1B,MAAMnE,EAAOmE,EAAW,IACxB,GAAa,IAATnE,EAAY,CACdlL,KAAKiN,eAAe/B,GACpBlL,KAAKgR,WACL,SAASC,CACX,CACElM,EAAS,EAEb,KAAO,CAEL,MAAMzD,EAAa+N,EAAW,IAC9BtK,EAAS/E,KAAKmR,aAAa7P,EAAY,EACzC,MACK,GAAiB,MAAb+N,EAETtK,EAAS,UACJ,GAAiB,MAAbsK,EAETtK,GAAS,OACJ,GAAiB,MAAbsK,EAETtK,GAAS,OACJ,GAAiB,MAAbsK,EAETtK,EAAS/E,KAAKoR,eACT,GAAiB,MAAb/B,EAETtK,EAAS/E,KAAKqR,eACT,GAAiB,MAAbhC,EAETtK,EAAS/E,KAAKsR,cACT,GAAiB,MAAbjC,EAETtK,EAAS/E,KAAKuR,eACT,GAAiB,MAAblC,EAETtK,EAAS/E,KAAKwR,eACT,GAAiB,MAAbnC,EAGPtK,EADE/E,KAAKgH,YACEhH,KAAKyR,kBAELzR,KAAK0R,eAEX,GAAiB,MAAbrC,EAETtK,EAAS/E,KAAK2R,cACT,GAAiB,MAAbtC,EAETtK,EAAS/E,KAAK4R,eACT,GAAiB,MAAbvC,EAETtK,EAAS/E,KAAK6R,eACT,GAAiB,MAAbxC,EAGPtK,EADE/E,KAAKgH,YACEhH,KAAK8R,kBAEL9R,KAAK+R,eAEX,GAAiB,MAAb1C,EAAmB,CAE5B,MAAM/N,EAAatB,KAAKgS,SACxBjN,EAAS/E,KAAKmR,aAAa7P,EAAY,EACzC,MAAO,GAAiB,MAAb+N,EAAmB,CAE5B,MAAM/N,EAAatB,KAAKiS,UACxBlN,EAAS/E,KAAKmR,aAAa7P,EAAY,EACzC,MAAO,GAAiB,MAAb+N,EAAmB,CAE5B,MAAM/N,EAAatB,KAAKkS,UACxBnN,EAAS/E,KAAKmR,aAAa7P,EAAY,EACzC,MAAO,GAAiB,MAAb+N,EAAmB,CAE5B,MAAMnE,EAAOlL,KAAKuR,UAClB,GAAa,IAATrG,EAAY,CACdlL,KAAKiN,eAAe/B,GACpBlL,KAAKgR,WACL,SAASC,CACX,CACElM,EAAS,EAEb,MAAO,GAAiB,MAAbsK,EAAmB,CAE5B,MAAMnE,EAAOlL,KAAKwR,UAClB,GAAa,IAATtG,EAAY,CACdlL,KAAKiN,eAAe/B,GACpBlL,KAAKgR,WACL,SAASC,CACX,CACElM,EAAS,EAEb,MAAO,GAAiB,MAAbsK,EAAmB,CAE5B,MAAMnE,EAAOlL,KAAKuR,UAClB,GAAa,IAATrG,EAAY,CACdlL,KAAKsN,aAAapC,GAClBlL,KAAKgR,WACL,SAASC,CACX,CACElM,EAAS,CAAC,CAEd,MAAO,GAAiB,MAAbsK,EAAmB,CAE5B,MAAMnE,EAAOlL,KAAKwR,UAClB,GAAa,IAATtG,EAAY,CACdlL,KAAKsN,aAAapC,GAClBlL,KAAKgR,WACL,SAASC,CACX,CACElM,EAAS,CAAC,CAEd,MAAO,GAAiB,MAAbsK,EAAmB,CAE5B,MAAMnE,EAAOlL,KAAKgS,SAClBjN,EAAS/E,KAAKmS,aAAajH,EAAM,EACnC,MAAO,GAAiB,MAAbmE,EAAmB,CAE5B,MAAMnE,EAAOlL,KAAKiS,UAClBlN,EAAS/E,KAAKmS,aAAajH,EAAM,EACnC,MAAO,GAAiB,MAAbmE,EAAmB,CAE5B,MAAMnE,EAAOlL,KAAKkS,UAClBnN,EAAS/E,KAAKmS,aAAajH,EAAM,EACnC,MAAO,GAAiB,MAAbmE,EAETtK,EAAS/E,KAAKoS,gBAAgB,EAAG,QAC5B,GAAiB,MAAb/C,EAETtK,EAAS/E,KAAKoS,gBAAgB,EAAG,QAC5B,GAAiB,MAAb/C,EAETtK,EAAS/E,KAAKoS,gBAAgB,EAAG,QAC5B,GAAiB,MAAb/C,EAETtK,EAAS/E,KAAKoS,gBAAgB,EAAG,QAC5B,GAAiB,MAAb/C,EAETtK,EAAS/E,KAAKoS,gBAAgB,GAAI,QAC7B,GAAiB,MAAb/C,EAAmB,CAE5B,MAAMnE,EAAOlL,KAAKgS,SAClBjN,EAAS/E,KAAKoS,gBAAgBlH,EAAM,EACtC,MAAO,GAAiB,MAAbmE,EAAmB,CAE5B,MAAMnE,EAAOlL,KAAKiS,UAClBlN,EAAS/E,KAAKoS,gBAAgBlH,EAAM,EACtC,KAAO,IAAiB,MAAbmE,EAKT,MAAM,IAAI5M,EAAY,2BAA2B4J,EAAWgD,MALhC,CAE5B,MAAMnE,EAAOlL,KAAKkS,UAClBnN,EAAS/E,KAAKoS,gBAAgBlH,EAAM,EACtC,CAEA,CAEAlL,KAAKgR,WAEL,MAAMlE,EAAQ9M,KAAK8M,MACnB,KAAOA,EAAM9K,OAAS,GAAG,CAEvB,MAAMkL,EAAQJ,EAAME,MACpB,GAAIE,EAAM3K,OAASkK,EAAa,CAG9B,GAFAS,EAAMG,MAAMH,EAAME,UAAYrI,EAC9BmI,EAAME,WACFF,EAAME,WAAaF,EAAMhC,KAI3B,SAAS+F,EAHTlM,EAASmI,EAAMG,MACfP,EAAMY,QAAQR,EAIlB,KAAO,IAAIA,EAAM3K,OAASmK,EAAe,CACvC,GAAe,cAAX3H,EACF,MAAM,IAAItC,EAAY,oCAGxByK,EAAM/M,IAAMH,KAAK4M,gBAAgB7H,GACjCmI,EAAM3K,KAAOoK,EACb,SAASsE,CACX,CAME,GAHA/D,EAAMM,IAAIN,EAAM/M,KAAQ4E,EACxBmI,EAAMK,YAEFL,EAAMK,YAAcL,EAAMhC,KAGvB,CACLgC,EAAM/M,IAAM,KACZ+M,EAAM3K,KAAOmK,EACb,SAASuE,CACX,CANElM,EAASmI,EAAMM,IACfV,EAAMY,QAAQR,EAMlB,CACF,CAEA,OAAOnI,CACT,CACF,CAEQ,YAAAmM,GAMN,OA7euB,IAwenBlR,KAAKqP,WACPrP,KAAKqP,SAAWrP,KAAKsR,UAIhBtR,KAAKqP,QACd,CAEQ,QAAA2B,GACNhR,KAAKqP,UAjfkB,CAkfzB,CAEQ,aAAA0B,GACN,MAAM1B,EAAWrP,KAAKkR,eAEtB,OAAQ7B,GACN,KAAK,IACH,OAAOrP,KAAKuR,UACd,KAAK,IACH,OAAOvR,KAAKwR,UACd,QACE,GAAInC,EAAW,IACb,OAAOA,EAAW,IAElB,MAAM,IAAI5M,EAAY,iCAAiC4J,EAAWgD,MAI1E,CAEQ,YAAA/B,CAAapC,GACnB,GAAIA,EAAOlL,KAAK0P,aACd,MAAM,IAAIjN,EAAY,oCAAoCyI,4BAA+BlL,KAAK0P,iBAGhG1P,KAAK8M,MAAMQ,aAAapC,EAC1B,CAEQ,cAAA+B,CAAe/B,GACrB,GAAIA,EAAOlL,KAAKyP,eACd,MAAM,IAAIhN,EAAY,sCAAsCyI,wBAA2BlL,KAAKyP,mBAG9FzP,KAAK8M,MAAMG,eAAe/B,EAC5B,CAEQ,YAAAiG,CAAa7P,EAAoB+Q,GACvC,OAAKrS,KAAKsP,YAActP,KAAKsS,gBACpBtS,KAAKuS,iBAAiBjR,EAAY+Q,GAEpCrS,KAAKmS,aAAa7Q,EAAY+Q,EACvC,CAKQ,gBAAAE,CAAiBjR,EAAoB+Q,GAC3C,GAAI/Q,EAAatB,KAAKuP,aACpB,MAAM,IAAI9M,EACR,2CAA2CnB,sBAA+BtB,KAAKuP,iBAInF,GAAIvP,KAAKoB,MAAME,WAAatB,KAAKuH,IAAM8K,EAAe/Q,EACpD,MAAM2M,EAGR,MAAM1M,EAASvB,KAAKuH,IAAM8K,EAC1B,IAAItN,EAOJ,OALEA,EADE/E,KAAKsS,iBAAmBtS,KAAK4P,YAAYpB,YAAYlN,GAC9CtB,KAAK4P,WAAWpK,OAAOxF,KAAKoB,MAAOG,EAAQD,GVpkBnD,SAAoBF,EAAmBC,EAAqBC,GACjE,OAAIA,EARyB,IAExB,SAAsBF,EAAmBC,EAAqBC,GACnE,MAAMkR,EAAcpR,EAAMwG,SAASvG,EAAaA,EAAcC,GAC9D,OAAOa,EAAkBqD,OAAOgN,EAClC,CAIWC,CAAarR,EAAOC,EAAaC,GAEjCH,EAAaC,EAAOC,EAAaC,EAE5C,CUgkBeoR,CAAW1S,KAAKoB,MAAOG,EAAQD,GAE1CtB,KAAKuH,KAAO8K,EAAe/Q,EACpByD,CACT,CAEQ,aAAAuN,GACN,OAAItS,KAAK8M,MAAM9K,OAAS,GACRhC,KAAK8M,MAAME,MACZzK,OAASmK,CAG1B,CAKQ,YAAAyF,CAAa7Q,EAAoBqR,GACvC,GAAIrR,EAAatB,KAAKwP,aACpB,MAAM,IAAI/M,EAAY,oCAAoCnB,sBAA+BtB,KAAKwP,iBAGhG,IAAKxP,KAAK+P,aAAazO,EAAaqR,GAClC,MAAM1E,EAGR,MAAM1M,EAASvB,KAAKuH,IAAMoL,EACpB5N,EAAS/E,KAAKoB,MAAMwG,SAASrG,EAAQA,EAASD,GAEpD,OADAtB,KAAKuH,KAAOoL,EAAarR,EAClByD,CACT,CAEQ,eAAAqN,CAAgBlH,EAAcyH,GACpC,GAAIzH,EAAOlL,KAAK2P,aACd,MAAM,IAAIlN,EAAY,oCAAoCyI,sBAAyBlL,KAAK2P,iBAG1F,MAAMiD,EAAU5S,KAAKoD,KAAK0K,QAAQ9N,KAAKuH,IAAMoL,GACvCnQ,EAAOxC,KAAKmS,aAAajH,EAAMyH,EAAa,GAClD,OAAO3S,KAAK+G,eAAevB,OAAOhD,EAAMoQ,EAAS5S,KAAKiG,QACxD,CAEQ,MAAA+L,GACN,OAAOhS,KAAKoD,KAAKyP,SAAS7S,KAAKuH,IACjC,CAEQ,OAAA0K,GACN,OAAOjS,KAAKoD,KAAK0P,UAAU9S,KAAKuH,IAClC,CAEQ,OAAA2K,GACN,OAAOlS,KAAKoD,KAAKQ,UAAU5D,KAAKuH,IAClC,CAEQ,MAAA+J,GACN,MAAMtQ,EAAQhB,KAAKoD,KAAKyP,SAAS7S,KAAKuH,KAEtC,OADAvH,KAAKuH,MACEvG,CACT,CAEQ,MAAA2Q,GACN,MAAM3Q,EAAQhB,KAAKoD,KAAK0K,QAAQ9N,KAAKuH,KAErC,OADAvH,KAAKuH,MACEvG,CACT,CAEQ,OAAAuQ,GACN,MAAMvQ,EAAQhB,KAAKoD,KAAK0P,UAAU9S,KAAKuH,KAEvC,OADAvH,KAAKuH,KAAO,EACLvG,CACT,CAEQ,OAAA4Q,GACN,MAAM5Q,EAAQhB,KAAKoD,KAAK2P,SAAS/S,KAAKuH,KAEtC,OADAvH,KAAKuH,KAAO,EACLvG,CACT,CAEQ,OAAAwQ,GACN,MAAMxQ,EAAQhB,KAAKoD,KAAKQ,UAAU5D,KAAKuH,KAEvC,OADAvH,KAAKuH,KAAO,EACLvG,CACT,CAEQ,OAAA6Q,GACN,MAAM7Q,EAAQhB,KAAKoD,KAAKO,SAAS3D,KAAKuH,KAEtC,OADAvH,KAAKuH,KAAO,EACLvG,CACT,CAEQ,OAAA0Q,GACN,MAAM1Q,GPhzBgBoC,EOgzBEpD,KAAKoD,KPhzBS7B,EOgzBHvB,KAAKuH,IP7yB5B,WAFDnE,EAAKQ,UAAUrC,GAChB6B,EAAKQ,UAAUrC,EAAS,IAF/B,IAAmB6B,EAAgB7B,EOkzBtC,OADAvB,KAAKuH,KAAO,EACLvG,CACT,CAEQ,OAAA+Q,GACN,MAAM/Q,EAAQ0C,EAAS1D,KAAKoD,KAAMpD,KAAKuH,KAEvC,OADAvH,KAAKuH,KAAO,EACLvG,CACT,CAEQ,eAAAyQ,GACN,MAAMzQ,EAAQhB,KAAKoD,KAAK4P,aAAahT,KAAKuH,KAE1C,OADAvH,KAAKuH,KAAO,EACLvG,CACT,CAEQ,eAAA8Q,GACN,MAAM9Q,EAAQhB,KAAKoD,KAAK6P,YAAYjT,KAAKuH,KAEzC,OADAvH,KAAKuH,KAAO,EACLvG,CACT,CAEQ,OAAAoQ,GACN,MAAMpQ,EAAQhB,KAAKoD,KAAK8P,WAAWlT,KAAKuH,KAExC,OADAvH,KAAKuH,KAAO,EACLvG,CACT,CAEQ,OAAAqQ,GACN,MAAMrQ,EAAQhB,KAAKoD,KAAK+P,WAAWnT,KAAKuH,KAExC,OADAvH,KAAKuH,KAAO,EACLvG,CACT,EE/1BK,SAASwE,EACdlB,EACAuC,GAGA,OADgB,IAAIsI,EAAQtI,GACbrB,OAAOlB,EACxB,CASO,SAAS+L,EACd/L,EACAuC,GAGA,OADgB,IAAIsI,EAAQtI,GACbwJ,YAAY/L,EAC7B,CCNO,SAAS8O,EAAuBC,GACrC,OApBgD,MAoB5BA,EApBGvS,OAAOwS,eAqBrBD,EAlBJE,gBAA2C/C,GAChD,MAAMgD,EAAShD,EAAOiD,YAEtB,IACE,OAAa,CACX,MAAM,KAAEC,EAAI,MAAE1S,SAAgBwS,EAAOG,OACrC,GAAID,EACF,aAEI1S,CACR,CACF,C,QACEwS,EAAOI,aACT,CACF,CAMWC,CAAwBR,EAEnC,CCxBOE,eAAehD,EACpB8C,EACAxM,GAEA,MAAM2J,EAAS4C,EAAoBC,GAEnC,OADgB,IAAIlE,EAAQtI,GACb0J,YAAYC,EAC7B,CAMO,SAASE,EACd2C,EACAxM,GAEA,MAAM2J,EAAS4C,EAAoBC,GAEnC,OADgB,IAAIlE,EAAQtI,GACb6J,kBAAkBF,EACnC,CAMO,SAASsD,EACdT,EACAxM,GAEA,MAAM2J,EAAS4C,EAAoBC,GAEnC,OADgB,IAAIlE,EAAQtI,GACb+J,aAAaJ,EAC9B,C", "sources": ["webpack://MessagePack/webpack/universalModuleDefinition", "webpack://MessagePack/webpack/bootstrap", "webpack://MessagePack/webpack/runtime/define property getters", "webpack://MessagePack/webpack/runtime/hasOwnProperty shorthand", "webpack://MessagePack/webpack/runtime/make namespace object", "webpack://MessagePack/./src/utils/utf8.ts", "webpack://MessagePack/./src/ExtData.ts", "webpack://MessagePack/./src/DecodeError.ts", "webpack://MessagePack/./src/utils/int.ts", "webpack://MessagePack/./src/timestamp.ts", "webpack://MessagePack/./src/ExtensionCodec.ts", "webpack://MessagePack/./src/utils/typedArrays.ts", "webpack://MessagePack/./src/Encoder.ts", "webpack://MessagePack/./src/encode.ts", "webpack://MessagePack/./src/utils/prettyByte.ts", "webpack://MessagePack/./src/Decoder.ts", "webpack://MessagePack/./src/CachedKeyDecoder.ts", "webpack://MessagePack/./src/decode.ts", "webpack://MessagePack/./src/utils/stream.ts", "webpack://MessagePack/./src/decodeAsync.ts"], "sourcesContent": ["(function webpackUniversalModuleDefinition(root, factory) {\n\tif(typeof exports === 'object' && typeof module === 'object')\n\t\tmodule.exports = factory();\n\telse if(typeof define === 'function' && define.amd)\n\t\tdefine([], factory);\n\telse if(typeof exports === 'object')\n\t\texports[\"MessagePack\"] = factory();\n\telse\n\t\troot[\"MessagePack\"] = factory();\n})(this, () => {\nreturn ", "// The require scope\nvar __webpack_require__ = {};\n\n", "// define getter functions for harmony exports\n__webpack_require__.d = (exports, definition) => {\n\tfor(var key in definition) {\n\t\tif(__webpack_require__.o(definition, key) && !__webpack_require__.o(exports, key)) {\n\t\t\tObject.defineProperty(exports, key, { enumerable: true, get: definition[key] });\n\t\t}\n\t}\n};", "__webpack_require__.o = (obj, prop) => (Object.prototype.hasOwnProperty.call(obj, prop))", "// define __esModule on exports\n__webpack_require__.r = (exports) => {\n\tif(typeof Symbol !== 'undefined' && Symbol.toStringTag) {\n\t\tObject.defineProperty(exports, Symbol.toStringTag, { value: 'Module' });\n\t}\n\tObject.defineProperty(exports, '__esModule', { value: true });\n};", "export function utf8Count(str: string): number {\n  const strLength = str.length;\n\n  let byteLength = 0;\n  let pos = 0;\n  while (pos < strLength) {\n    let value = str.charCodeAt(pos++);\n\n    if ((value & 0xffffff80) === 0) {\n      // 1-byte\n      byteLength++;\n      continue;\n    } else if ((value & 0xfffff800) === 0) {\n      // 2-bytes\n      byteLength += 2;\n    } else {\n      // handle surrogate pair\n      if (value >= 0xd800 && value <= 0xdbff) {\n        // high surrogate\n        if (pos < strLength) {\n          const extra = str.charCodeAt(pos);\n          if ((extra & 0xfc00) === 0xdc00) {\n            ++pos;\n            value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n          }\n        }\n      }\n\n      if ((value & 0xffff0000) === 0) {\n        // 3-byte\n        byteLength += 3;\n      } else {\n        // 4-byte\n        byteLength += 4;\n      }\n    }\n  }\n  return byteLength;\n}\n\nexport function utf8EncodeJs(str: string, output: Uint8Array, outputOffset: number): void {\n  const strLength = str.length;\n  let offset = outputOffset;\n  let pos = 0;\n  while (pos < strLength) {\n    let value = str.charCodeAt(pos++);\n\n    if ((value & 0xffffff80) === 0) {\n      // 1-byte\n      output[offset++] = value;\n      continue;\n    } else if ((value & 0xfffff800) === 0) {\n      // 2-bytes\n      output[offset++] = ((value >> 6) & 0x1f) | 0xc0;\n    } else {\n      // handle surrogate pair\n      if (value >= 0xd800 && value <= 0xdbff) {\n        // high surrogate\n        if (pos < strLength) {\n          const extra = str.charCodeAt(pos);\n          if ((extra & 0xfc00) === 0xdc00) {\n            ++pos;\n            value = ((value & 0x3ff) << 10) + (extra & 0x3ff) + 0x10000;\n          }\n        }\n      }\n\n      if ((value & 0xffff0000) === 0) {\n        // 3-byte\n        output[offset++] = ((value >> 12) & 0x0f) | 0xe0;\n        output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n      } else {\n        // 4-byte\n        output[offset++] = ((value >> 18) & 0x07) | 0xf0;\n        output[offset++] = ((value >> 12) & 0x3f) | 0x80;\n        output[offset++] = ((value >> 6) & 0x3f) | 0x80;\n      }\n    }\n\n    output[offset++] = (value & 0x3f) | 0x80;\n  }\n}\n\n// TextEncoder and TextDecoder are standardized in whatwg encoding:\n// https://encoding.spec.whatwg.org/\n// and available in all the modern browsers:\n// https://caniuse.com/textencoder\n// They are available in Node.js since v12 LTS as well:\n// https://nodejs.org/api/globals.html#textencoder\n\nconst sharedTextEncoder = new TextEncoder();\n\n// This threshold should be determined by benchmarking, which might vary in engines and input data.\n// Run `npx ts-node benchmark/encode-string.ts` for details.\nconst TEXT_ENCODER_THRESHOLD = 50;\n\nexport function utf8EncodeTE(str: string, output: Uint8Array, outputOffset: number): void {\n  sharedTextEncoder.encodeInto(str, output.subarray(outputOffset));\n}\n\nexport function utf8Encode(str: string, output: Uint8Array, outputOffset: number): void {\n  if (str.length > TEXT_ENCODER_THRESHOLD) {\n    utf8EncodeTE(str, output, outputOffset);\n  } else {\n    utf8EncodeJs(str, output, outputOffset);\n  }\n}\n\nconst CHUNK_SIZE = 0x1_000;\n\nexport function utf8DecodeJs(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n  let offset = inputOffset;\n  const end = offset + byteLength;\n\n  const units: Array<number> = [];\n  let result = \"\";\n  while (offset < end) {\n    const byte1 = bytes[offset++]!;\n    if ((byte1 & 0x80) === 0) {\n      // 1 byte\n      units.push(byte1);\n    } else if ((byte1 & 0xe0) === 0xc0) {\n      // 2 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      units.push(((byte1 & 0x1f) << 6) | byte2);\n    } else if ((byte1 & 0xf0) === 0xe0) {\n      // 3 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      const byte3 = bytes[offset++]! & 0x3f;\n      units.push(((byte1 & 0x1f) << 12) | (byte2 << 6) | byte3);\n    } else if ((byte1 & 0xf8) === 0xf0) {\n      // 4 bytes\n      const byte2 = bytes[offset++]! & 0x3f;\n      const byte3 = bytes[offset++]! & 0x3f;\n      const byte4 = bytes[offset++]! & 0x3f;\n      let unit = ((byte1 & 0x07) << 0x12) | (byte2 << 0x0c) | (byte3 << 0x06) | byte4;\n      if (unit > 0xffff) {\n        unit -= 0x10000;\n        units.push(((unit >>> 10) & 0x3ff) | 0xd800);\n        unit = 0xdc00 | (unit & 0x3ff);\n      }\n      units.push(unit);\n    } else {\n      units.push(byte1);\n    }\n\n    if (units.length >= CHUNK_SIZE) {\n      result += String.fromCharCode(...units);\n      units.length = 0;\n    }\n  }\n\n  if (units.length > 0) {\n    result += String.fromCharCode(...units);\n  }\n\n  return result;\n}\n\nconst sharedTextDecoder = new TextDecoder();\n\n// This threshold should be determined by benchmarking, which might vary in engines and input data.\n// Run `npx ts-node benchmark/decode-string.ts` for details.\nconst TEXT_DECODER_THRESHOLD = 200;\n\nexport function utf8DecodeTD(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n  const stringBytes = bytes.subarray(inputOffset, inputOffset + byteLength);\n  return sharedTextDecoder.decode(stringBytes);\n}\n\nexport function utf8Decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n  if (byteLength > TEXT_DECODER_THRESHOLD) {\n    return utf8DecodeTD(bytes, inputOffset, byteLength);\n  } else {\n    return utf8DecodeJs(bytes, inputOffset, byteLength);\n  }\n}\n", "/**\n * ExtData is used to handle Extension Types that are not registered to ExtensionCodec.\n */\nexport class ExtData {\n  readonly type: number;\n  readonly data: Uint8Array | ((pos: number) => Uint8Array);\n\n  constructor(type: number, data: Uint8Array | ((pos: number) => Uint8Array)) {\n    this.type = type;\n    this.data = data;\n  }\n}\n", "export class DecodeError extends Error {\n  constructor(message: string) {\n    super(message);\n\n    // fix the prototype chain in a cross-platform way\n    const proto: typeof DecodeError.prototype = Object.create(DecodeError.prototype);\n    Object.setPrototypeOf(this, proto);\n\n    Object.defineProperty(this, \"name\", {\n      configurable: true,\n      enumerable: false,\n      value: DecodeError.name,\n    });\n  }\n}\n", "// Integer Utility\n\nexport const UINT32_MAX = 0xffff_ffff;\n\n// DataView extension to handle int64 / uint64,\n// where the actual range is 53-bits integer (a.k.a. safe integer)\n\nexport function setUint64(view: DataView, offset: number, value: number): void {\n  const high = value / 0x1_0000_0000;\n  const low = value; // high bits are truncated by DataView\n  view.setUint32(offset, high);\n  view.setUint32(offset + 4, low);\n}\n\nexport function setInt64(view: DataView, offset: number, value: number): void {\n  const high = Math.floor(value / 0x1_0000_0000);\n  const low = value; // high bits are truncated by DataView\n  view.setUint32(offset, high);\n  view.setUint32(offset + 4, low);\n}\n\nexport function getInt64(view: DataView, offset: number): number {\n  const high = view.getInt32(offset);\n  const low = view.getUint32(offset + 4);\n  return high * 0x1_0000_0000 + low;\n}\n\nexport function getUint64(view: <PERSON>View, offset: number): number {\n  const high = view.getUint32(offset);\n  const low = view.getUint32(offset + 4);\n  return high * 0x1_0000_0000 + low;\n}\n", "// https://github.com/msgpack/msgpack/blob/master/spec.md#timestamp-extension-type\nimport { DecodeError } from \"./DecodeError.ts\";\nimport { getInt64, setInt64 } from \"./utils/int.ts\";\n\nexport const EXT_TIMESTAMP = -1;\n\nexport type TimeSpec = {\n  sec: number;\n  nsec: number;\n};\n\nconst TIMESTAMP32_MAX_SEC = 0x100000000 - 1; // 32-bit unsigned int\nconst TIMESTAMP64_MAX_SEC = 0x400000000 - 1; // 34-bit unsigned int\n\nexport function encodeTimeSpecToTimestamp({ sec, nsec }: TimeSpec): Uint8Array {\n  if (sec >= 0 && nsec >= 0 && sec <= TIMESTAMP64_MAX_SEC) {\n    // Here sec >= 0 && nsec >= 0\n    if (nsec === 0 && sec <= TIMESTAMP32_MAX_SEC) {\n      // timestamp 32 = { sec32 (unsigned) }\n      const rv = new Uint8Array(4);\n      const view = new DataView(rv.buffer);\n      view.setUint32(0, sec);\n      return rv;\n    } else {\n      // timestamp 64 = { nsec30 (unsigned), sec34 (unsigned) }\n      const secHigh = sec / 0x100000000;\n      const secLow = sec & 0xffffffff;\n      const rv = new Uint8Array(8);\n      const view = new DataView(rv.buffer);\n      // nsec30 | secHigh2\n      view.setUint32(0, (nsec << 2) | (secHigh & 0x3));\n      // secLow32\n      view.setUint32(4, secLow);\n      return rv;\n    }\n  } else {\n    // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n    const rv = new Uint8Array(12);\n    const view = new DataView(rv.buffer);\n    view.setUint32(0, nsec);\n    setInt64(view, 4, sec);\n    return rv;\n  }\n}\n\nexport function encodeDateToTimeSpec(date: Date): TimeSpec {\n  const msec = date.getTime();\n  const sec = Math.floor(msec / 1e3);\n  const nsec = (msec - sec * 1e3) * 1e6;\n\n  // Normalizes { sec, nsec } to ensure nsec is unsigned.\n  const nsecInSec = Math.floor(nsec / 1e9);\n  return {\n    sec: sec + nsecInSec,\n    nsec: nsec - nsecInSec * 1e9,\n  };\n}\n\nexport function encodeTimestampExtension(object: unknown): Uint8Array | null {\n  if (object instanceof Date) {\n    const timeSpec = encodeDateToTimeSpec(object);\n    return encodeTimeSpecToTimestamp(timeSpec);\n  } else {\n    return null;\n  }\n}\n\nexport function decodeTimestampToTimeSpec(data: Uint8Array): TimeSpec {\n  const view = new DataView(data.buffer, data.byteOffset, data.byteLength);\n\n  // data may be 32, 64, or 96 bits\n  switch (data.byteLength) {\n    case 4: {\n      // timestamp 32 = { sec32 }\n      const sec = view.getUint32(0);\n      const nsec = 0;\n      return { sec, nsec };\n    }\n    case 8: {\n      // timestamp 64 = { nsec30, sec34 }\n      const nsec30AndSecHigh2 = view.getUint32(0);\n      const secLow32 = view.getUint32(4);\n      const sec = (nsec30AndSecHigh2 & 0x3) * 0x100000000 + secLow32;\n      const nsec = nsec30AndSecHigh2 >>> 2;\n      return { sec, nsec };\n    }\n    case 12: {\n      // timestamp 96 = { nsec32 (unsigned), sec64 (signed) }\n\n      const sec = getInt64(view, 4);\n      const nsec = view.getUint32(0);\n      return { sec, nsec };\n    }\n    default:\n      throw new DecodeError(`Unrecognized data size for timestamp (expected 4, 8, or 12): ${data.length}`);\n  }\n}\n\nexport function decodeTimestampExtension(data: Uint8Array): Date {\n  const timeSpec = decodeTimestampToTimeSpec(data);\n  return new Date(timeSpec.sec * 1e3 + timeSpec.nsec / 1e6);\n}\n\nexport const timestampExtension = {\n  type: EXT_TIMESTAMP,\n  encode: encodeTimestampExtension,\n  decode: decodeTimestampExtension,\n};\n", "// ExtensionCodec to handle MessagePack extensions\n\nimport { ExtData } from \"./ExtData.ts\";\nimport { timestampExtension } from \"./timestamp.ts\";\n\nexport type ExtensionDecoderType<ContextType> = (\n  data: Uint8Array,\n  extensionType: number,\n  context: ContextType,\n) => unknown;\n\nexport type ExtensionEncoderType<ContextType> = (\n  input: unknown,\n  context: ContextType,\n) => Uint8Array | ((dataPos: number) => Uint8Array) | null;\n\n// immutable interface to ExtensionCodec\nexport type ExtensionCodecType<ContextType> = {\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  __brand?: ContextType;\n  tryToEncode(object: unknown, context: ContextType): ExtData | null;\n  decode(data: Uint8Array, extType: number, context: ContextType): unknown;\n};\n\nexport class ExtensionCodec<ContextType = undefined> implements ExtensionCodecType<ContextType> {\n  public static readonly defaultCodec: ExtensionCodecType<undefined> = new ExtensionCodec();\n\n  // ensures ExtensionCodecType<X> matches ExtensionCodec<X>\n  // this will make type errors a lot more clear\n  // eslint-disable-next-line @typescript-eslint/naming-convention\n  __brand?: ContextType;\n\n  // built-in extensions\n  private readonly builtInEncoders: Array<ExtensionEncoderType<ContextType> | undefined | null> = [];\n  private readonly builtInDecoders: Array<ExtensionDecoderType<ContextType> | undefined | null> = [];\n\n  // custom extensions\n  private readonly encoders: Array<ExtensionEncoderType<ContextType> | undefined | null> = [];\n  private readonly decoders: Array<ExtensionDecoderType<ContextType> | undefined | null> = [];\n\n  public constructor() {\n    this.register(timestampExtension);\n  }\n\n  public register({\n    type,\n    encode,\n    decode,\n  }: {\n    type: number;\n    encode: ExtensionEncoderType<ContextType>;\n    decode: ExtensionDecoderType<ContextType>;\n  }): void {\n    if (type >= 0) {\n      // custom extensions\n      this.encoders[type] = encode;\n      this.decoders[type] = decode;\n    } else {\n      // built-in extensions\n      const index = -1 - type;\n      this.builtInEncoders[index] = encode;\n      this.builtInDecoders[index] = decode;\n    }\n  }\n\n  public tryToEncode(object: unknown, context: ContextType): ExtData | null {\n    // built-in extensions\n    for (let i = 0; i < this.builtInEncoders.length; i++) {\n      const encodeExt = this.builtInEncoders[i];\n      if (encodeExt != null) {\n        const data = encodeExt(object, context);\n        if (data != null) {\n          const type = -1 - i;\n          return new ExtData(type, data);\n        }\n      }\n    }\n\n    // custom extensions\n    for (let i = 0; i < this.encoders.length; i++) {\n      const encodeExt = this.encoders[i];\n      if (encodeExt != null) {\n        const data = encodeExt(object, context);\n        if (data != null) {\n          const type = i;\n          return new ExtData(type, data);\n        }\n      }\n    }\n\n    if (object instanceof ExtData) {\n      // to keep ExtData as is\n      return object;\n    }\n    return null;\n  }\n\n  public decode(data: Uint8Array, type: number, context: ContextType): unknown {\n    const decodeExt = type < 0 ? this.builtInDecoders[-1 - type] : this.decoders[type];\n    if (decodeExt) {\n      return decodeExt(data, type, context);\n    } else {\n      // decode() does not fail, returns ExtData instead.\n      return new ExtData(type, data);\n    }\n  }\n}\n", "function isArrayBufferLike(buffer: unknown): buffer is ArrayBuffer<PERSON>ike {\n  return (\n    buffer instanceof ArrayBuffer || (typeof SharedArrayBuffer !== \"undefined\" && buffer instanceof SharedArrayBuffer)\n  );\n}\n\nexport function ensureUint8Array(\n  buffer: ArrayLike<number> | Uint8Array<ArrayBufferLike> | ArrayBufferView | ArrayBufferLike,\n): Uint8Array<ArrayBufferLike> {\n  if (buffer instanceof Uint8Array) {\n    return buffer;\n  } else if (ArrayBuffer.isView(buffer)) {\n    return new Uint8Array(buffer.buffer, buffer.byteOffset, buffer.byteLength);\n  } else if (isArrayBuffer<PERSON>ike(buffer)) {\n    return new Uint8Array(buffer);\n  } else {\n    // ArrayLike<number>\n    return Uint8Array.from(buffer);\n  }\n}\n", "import { utf8Count, utf8Encode } from \"./utils/utf8.ts\";\nimport { ExtensionCodec, ExtensionCodecType } from \"./ExtensionCodec.ts\";\nimport { setInt64, setUint64 } from \"./utils/int.ts\";\nimport { ensureUint8Array } from \"./utils/typedArrays.ts\";\nimport type { ExtData } from \"./ExtData.ts\";\nimport type { ContextOf } from \"./context.ts\";\n\nexport const DEFAULT_MAX_DEPTH = 100;\nexport const DEFAULT_INITIAL_BUFFER_SIZE = 2048;\n\nexport type EncoderOptions<ContextType = undefined> = Partial<\n  Readonly<{\n    extensionCodec: ExtensionCodecType<ContextType>;\n\n    /**\n     * Encodes bigint as Int64 or Uint64 if it's set to true.\n     * {@link forceIntegerToFloat} does not affect bigint.\n     * Depends on ES2020's {@link DataView#setBigInt64} and\n     * {@link DataView#setBigUint64}.\n     *\n     * Defaults to false.\n     */\n    useBigInt64: boolean;\n\n    /**\n     * The maximum depth in nested objects and arrays.\n     *\n     * Defaults to 100.\n     */\n    maxDepth: number;\n\n    /**\n     * The initial size of the internal buffer.\n     *\n     * Defaults to 2048.\n     */\n    initialBufferSize: number;\n\n    /**\n     * If `true`, the keys of an object is sorted. In other words, the encoded\n     * binary is canonical and thus comparable to another encoded binary.\n     *\n     * Defaults to `false`. If enabled, it spends more time in encoding objects.\n     */\n    sortKeys: boolean;\n    /**\n     * If `true`, non-integer numbers are encoded in float32, not in float64 (the default).\n     *\n     * Only use it if precisions don't matter.\n     *\n     * Defaults to `false`.\n     */\n    forceFloat32: boolean;\n\n    /**\n     * If `true`, an object property with `undefined` value are ignored.\n     * e.g. `{ foo: undefined }` will be encoded as `{}`, as `JSON.stringify()` does.\n     *\n     * Defaults to `false`. If enabled, it spends more time in encoding objects.\n     */\n    ignoreUndefined: boolean;\n\n    /**\n     * If `true`, integer numbers are encoded as floating point numbers,\n     * with the `forceFloat32` option taken into account.\n     *\n     * Defaults to `false`.\n     */\n    forceIntegerToFloat: boolean;\n  }>\n> &\n  ContextOf<ContextType>;\n\nexport class Encoder<ContextType = undefined> {\n  private readonly extensionCodec: ExtensionCodecType<ContextType>;\n  private readonly context: ContextType;\n  private readonly useBigInt64: boolean;\n  private readonly maxDepth: number;\n  private readonly initialBufferSize: number;\n  private readonly sortKeys: boolean;\n  private readonly forceFloat32: boolean;\n  private readonly ignoreUndefined: boolean;\n  private readonly forceIntegerToFloat: boolean;\n\n  private pos: number;\n  private view: DataView;\n  private bytes: Uint8Array;\n\n  private entered = false;\n\n  public constructor(options?: EncoderOptions<ContextType>) {\n    this.extensionCodec = options?.extensionCodec ?? (ExtensionCodec.defaultCodec as ExtensionCodecType<ContextType>);\n    this.context = (options as { context: ContextType } | undefined)?.context as ContextType; // needs a type assertion because EncoderOptions has no context property when ContextType is undefined\n\n    this.useBigInt64 = options?.useBigInt64 ?? false;\n    this.maxDepth = options?.maxDepth ?? DEFAULT_MAX_DEPTH;\n    this.initialBufferSize = options?.initialBufferSize ?? DEFAULT_INITIAL_BUFFER_SIZE;\n    this.sortKeys = options?.sortKeys ?? false;\n    this.forceFloat32 = options?.forceFloat32 ?? false;\n    this.ignoreUndefined = options?.ignoreUndefined ?? false;\n    this.forceIntegerToFloat = options?.forceIntegerToFloat ?? false;\n\n    this.pos = 0;\n    this.view = new DataView(new ArrayBuffer(this.initialBufferSize));\n    this.bytes = new Uint8Array(this.view.buffer);\n  }\n\n  private clone() {\n    // Because of slightly special argument `context`,\n    // type assertion is needed.\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n    return new Encoder<ContextType>({\n      extensionCodec: this.extensionCodec,\n      context: this.context,\n      useBigInt64: this.useBigInt64,\n      maxDepth: this.maxDepth,\n      initialBufferSize: this.initialBufferSize,\n      sortKeys: this.sortKeys,\n      forceFloat32: this.forceFloat32,\n      ignoreUndefined: this.ignoreUndefined,\n      forceIntegerToFloat: this.forceIntegerToFloat,\n    } as any);\n  }\n\n  private reinitializeState() {\n    this.pos = 0;\n  }\n\n  /**\n   * This is almost equivalent to {@link Encoder#encode}, but it returns an reference of the encoder's internal buffer and thus much faster than {@link Encoder#encode}.\n   *\n   * @returns Encodes the object and returns a shared reference the encoder's internal buffer.\n   */\n  public encodeSharedRef(object: unknown): Uint8Array {\n    if (this.entered) {\n      const instance = this.clone();\n      return instance.encodeSharedRef(object);\n    }\n\n    try {\n      this.entered = true;\n\n      this.reinitializeState();\n      this.doEncode(object, 1);\n      return this.bytes.subarray(0, this.pos);\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  /**\n   * @returns Encodes the object and returns a copy of the encoder's internal buffer.\n   */\n  public encode(object: unknown): Uint8Array {\n    if (this.entered) {\n      const instance = this.clone();\n      return instance.encode(object);\n    }\n\n    try {\n      this.entered = true;\n\n      this.reinitializeState();\n      this.doEncode(object, 1);\n      return this.bytes.slice(0, this.pos);\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  private doEncode(object: unknown, depth: number): void {\n    if (depth > this.maxDepth) {\n      throw new Error(`Too deep objects in depth ${depth}`);\n    }\n\n    if (object == null) {\n      this.encodeNil();\n    } else if (typeof object === \"boolean\") {\n      this.encodeBoolean(object);\n    } else if (typeof object === \"number\") {\n      if (!this.forceIntegerToFloat) {\n        this.encodeNumber(object);\n      } else {\n        this.encodeNumberAsFloat(object);\n      }\n    } else if (typeof object === \"string\") {\n      this.encodeString(object);\n    } else if (this.useBigInt64 && typeof object === \"bigint\") {\n      this.encodeBigInt64(object);\n    } else {\n      this.encodeObject(object, depth);\n    }\n  }\n\n  private ensureBufferSizeToWrite(sizeToWrite: number) {\n    const requiredSize = this.pos + sizeToWrite;\n\n    if (this.view.byteLength < requiredSize) {\n      this.resizeBuffer(requiredSize * 2);\n    }\n  }\n\n  private resizeBuffer(newSize: number) {\n    const newBuffer = new ArrayBuffer(newSize);\n    const newBytes = new Uint8Array(newBuffer);\n    const newView = new DataView(newBuffer);\n\n    newBytes.set(this.bytes);\n\n    this.view = newView;\n    this.bytes = newBytes;\n  }\n\n  private encodeNil() {\n    this.writeU8(0xc0);\n  }\n\n  private encodeBoolean(object: boolean) {\n    if (object === false) {\n      this.writeU8(0xc2);\n    } else {\n      this.writeU8(0xc3);\n    }\n  }\n\n  private encodeNumber(object: number): void {\n    if (!this.forceIntegerToFloat && Number.isSafeInteger(object)) {\n      if (object >= 0) {\n        if (object < 0x80) {\n          // positive fixint\n          this.writeU8(object);\n        } else if (object < 0x100) {\n          // uint 8\n          this.writeU8(0xcc);\n          this.writeU8(object);\n        } else if (object < 0x10000) {\n          // uint 16\n          this.writeU8(0xcd);\n          this.writeU16(object);\n        } else if (object < 0x100000000) {\n          // uint 32\n          this.writeU8(0xce);\n          this.writeU32(object);\n        } else if (!this.useBigInt64) {\n          // uint 64\n          this.writeU8(0xcf);\n          this.writeU64(object);\n        } else {\n          this.encodeNumberAsFloat(object);\n        }\n      } else {\n        if (object >= -0x20) {\n          // negative fixint\n          this.writeU8(0xe0 | (object + 0x20));\n        } else if (object >= -0x80) {\n          // int 8\n          this.writeU8(0xd0);\n          this.writeI8(object);\n        } else if (object >= -0x8000) {\n          // int 16\n          this.writeU8(0xd1);\n          this.writeI16(object);\n        } else if (object >= -0x80000000) {\n          // int 32\n          this.writeU8(0xd2);\n          this.writeI32(object);\n        } else if (!this.useBigInt64) {\n          // int 64\n          this.writeU8(0xd3);\n          this.writeI64(object);\n        } else {\n          this.encodeNumberAsFloat(object);\n        }\n      }\n    } else {\n      this.encodeNumberAsFloat(object);\n    }\n  }\n\n  private encodeNumberAsFloat(object: number): void {\n    if (this.forceFloat32) {\n      // float 32\n      this.writeU8(0xca);\n      this.writeF32(object);\n    } else {\n      // float 64\n      this.writeU8(0xcb);\n      this.writeF64(object);\n    }\n  }\n\n  private encodeBigInt64(object: bigint): void {\n    if (object >= BigInt(0)) {\n      // uint 64\n      this.writeU8(0xcf);\n      this.writeBigUint64(object);\n    } else {\n      // int 64\n      this.writeU8(0xd3);\n      this.writeBigInt64(object);\n    }\n  }\n\n  private writeStringHeader(byteLength: number) {\n    if (byteLength < 32) {\n      // fixstr\n      this.writeU8(0xa0 + byteLength);\n    } else if (byteLength < 0x100) {\n      // str 8\n      this.writeU8(0xd9);\n      this.writeU8(byteLength);\n    } else if (byteLength < 0x10000) {\n      // str 16\n      this.writeU8(0xda);\n      this.writeU16(byteLength);\n    } else if (byteLength < 0x100000000) {\n      // str 32\n      this.writeU8(0xdb);\n      this.writeU32(byteLength);\n    } else {\n      throw new Error(`Too long string: ${byteLength} bytes in UTF-8`);\n    }\n  }\n\n  private encodeString(object: string) {\n    const maxHeaderSize = 1 + 4;\n\n    const byteLength = utf8Count(object);\n    this.ensureBufferSizeToWrite(maxHeaderSize + byteLength);\n    this.writeStringHeader(byteLength);\n    utf8Encode(object, this.bytes, this.pos);\n    this.pos += byteLength;\n  }\n\n  private encodeObject(object: unknown, depth: number) {\n    // try to encode objects with custom codec first of non-primitives\n    const ext = this.extensionCodec.tryToEncode(object, this.context);\n    if (ext != null) {\n      this.encodeExtension(ext);\n    } else if (Array.isArray(object)) {\n      this.encodeArray(object, depth);\n    } else if (ArrayBuffer.isView(object)) {\n      this.encodeBinary(object);\n    } else if (typeof object === \"object\") {\n      this.encodeMap(object as Record<string, unknown>, depth);\n    } else {\n      // symbol, function and other special object come here unless extensionCodec handles them.\n      throw new Error(`Unrecognized object: ${Object.prototype.toString.apply(object)}`);\n    }\n  }\n\n  private encodeBinary(object: ArrayBufferView) {\n    const size = object.byteLength;\n    if (size < 0x100) {\n      // bin 8\n      this.writeU8(0xc4);\n      this.writeU8(size);\n    } else if (size < 0x10000) {\n      // bin 16\n      this.writeU8(0xc5);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // bin 32\n      this.writeU8(0xc6);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large binary: ${size}`);\n    }\n    const bytes = ensureUint8Array(object);\n    this.writeU8a(bytes);\n  }\n\n  private encodeArray(object: Array<unknown>, depth: number) {\n    const size = object.length;\n    if (size < 16) {\n      // fixarray\n      this.writeU8(0x90 + size);\n    } else if (size < 0x10000) {\n      // array 16\n      this.writeU8(0xdc);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // array 32\n      this.writeU8(0xdd);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large array: ${size}`);\n    }\n    for (const item of object) {\n      this.doEncode(item, depth + 1);\n    }\n  }\n\n  private countWithoutUndefined(object: Record<string, unknown>, keys: ReadonlyArray<string>): number {\n    let count = 0;\n\n    for (const key of keys) {\n      if (object[key] !== undefined) {\n        count++;\n      }\n    }\n\n    return count;\n  }\n\n  private encodeMap(object: Record<string, unknown>, depth: number) {\n    const keys = Object.keys(object);\n    if (this.sortKeys) {\n      keys.sort();\n    }\n\n    const size = this.ignoreUndefined ? this.countWithoutUndefined(object, keys) : keys.length;\n\n    if (size < 16) {\n      // fixmap\n      this.writeU8(0x80 + size);\n    } else if (size < 0x10000) {\n      // map 16\n      this.writeU8(0xde);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // map 32\n      this.writeU8(0xdf);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large map object: ${size}`);\n    }\n\n    for (const key of keys) {\n      const value = object[key];\n\n      if (!(this.ignoreUndefined && value === undefined)) {\n        this.encodeString(key);\n        this.doEncode(value, depth + 1);\n      }\n    }\n  }\n\n  private encodeExtension(ext: ExtData) {\n    if (typeof ext.data === \"function\") {\n      const data = ext.data(this.pos + 6);\n      const size = data.length;\n\n      if (size >= 0x100000000) {\n        throw new Error(`Too large extension object: ${size}`);\n      }\n\n      this.writeU8(0xc9);\n      this.writeU32(size);\n      this.writeI8(ext.type);\n      this.writeU8a(data);\n      return;\n    }\n\n    const size = ext.data.length;\n    if (size === 1) {\n      // fixext 1\n      this.writeU8(0xd4);\n    } else if (size === 2) {\n      // fixext 2\n      this.writeU8(0xd5);\n    } else if (size === 4) {\n      // fixext 4\n      this.writeU8(0xd6);\n    } else if (size === 8) {\n      // fixext 8\n      this.writeU8(0xd7);\n    } else if (size === 16) {\n      // fixext 16\n      this.writeU8(0xd8);\n    } else if (size < 0x100) {\n      // ext 8\n      this.writeU8(0xc7);\n      this.writeU8(size);\n    } else if (size < 0x10000) {\n      // ext 16\n      this.writeU8(0xc8);\n      this.writeU16(size);\n    } else if (size < 0x100000000) {\n      // ext 32\n      this.writeU8(0xc9);\n      this.writeU32(size);\n    } else {\n      throw new Error(`Too large extension object: ${size}`);\n    }\n    this.writeI8(ext.type);\n    this.writeU8a(ext.data);\n  }\n\n  private writeU8(value: number) {\n    this.ensureBufferSizeToWrite(1);\n\n    this.view.setUint8(this.pos, value);\n    this.pos++;\n  }\n\n  private writeU8a(values: ArrayLike<number>) {\n    const size = values.length;\n    this.ensureBufferSizeToWrite(size);\n\n    this.bytes.set(values, this.pos);\n    this.pos += size;\n  }\n\n  private writeI8(value: number) {\n    this.ensureBufferSizeToWrite(1);\n\n    this.view.setInt8(this.pos, value);\n    this.pos++;\n  }\n\n  private writeU16(value: number) {\n    this.ensureBufferSizeToWrite(2);\n\n    this.view.setUint16(this.pos, value);\n    this.pos += 2;\n  }\n\n  private writeI16(value: number) {\n    this.ensureBufferSizeToWrite(2);\n\n    this.view.setInt16(this.pos, value);\n    this.pos += 2;\n  }\n\n  private writeU32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n\n    this.view.setUint32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeI32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n\n    this.view.setInt32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeF32(value: number) {\n    this.ensureBufferSizeToWrite(4);\n\n    this.view.setFloat32(this.pos, value);\n    this.pos += 4;\n  }\n\n  private writeF64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n\n    this.view.setFloat64(this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeU64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n\n    setUint64(this.view, this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeI64(value: number) {\n    this.ensureBufferSizeToWrite(8);\n\n    setInt64(this.view, this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeBigUint64(value: bigint) {\n    this.ensureBufferSizeToWrite(8);\n\n    this.view.setBigUint64(this.pos, value);\n    this.pos += 8;\n  }\n\n  private writeBigInt64(value: bigint) {\n    this.ensureBufferSizeToWrite(8);\n\n    this.view.setBigInt64(this.pos, value);\n    this.pos += 8;\n  }\n}\n", "import { Encoder } from \"./Encoder.ts\";\nimport type { EncoderOptions } from \"./Encoder.ts\";\nimport type { SplitUndefined } from \"./context.ts\";\n\n/**\n * It encodes `value` in the MessagePack format and\n * returns a byte buffer.\n *\n * The returned buffer is a slice of a larger `ArrayBuffer`, so you have to use its `#byteOffset` and `#byteLength` in order to convert it to another typed arrays including NodeJS `Buffer`.\n */\nexport function encode<ContextType = undefined>(\n  value: unknown,\n  options?: EncoderOptions<SplitUndefined<ContextType>>,\n): Uint8Array {\n  const encoder = new Encoder(options);\n  return encoder.encodeSharedRef(value);\n}\n", "export function prettyByte(byte: number): string {\n  return `${byte < 0 ? \"-\" : \"\"}0x${Math.abs(byte).toString(16).padStart(2, \"0\")}`;\n}\n", "import { prettyByte } from \"./utils/prettyByte.ts\";\nimport { ExtensionCodec, ExtensionCodecType } from \"./ExtensionCodec.ts\";\nimport { getInt64, getUint64, UINT32_MAX } from \"./utils/int.ts\";\nimport { utf8Decode } from \"./utils/utf8.ts\";\nimport { ensureUint8Array } from \"./utils/typedArrays.ts\";\nimport { CachedKeyDecoder, KeyDecoder } from \"./CachedKeyDecoder.ts\";\nimport { DecodeError } from \"./DecodeError.ts\";\nimport type { ContextOf } from \"./context.ts\";\n\nexport type DecoderOptions<ContextType = undefined> = Readonly<\n  Partial<{\n    extensionCodec: ExtensionCodecType<ContextType>;\n\n    /**\n     * Decodes Int64 and Uint64 as bigint if it's set to true.\n     * Depends on ES2020's {@link DataView#getBigInt64} and\n     * {@link DataView#getBigUint64}.\n     *\n     * Defaults to false.\n     */\n    useBigInt64: boolean;\n\n    /**\n     * By default, string values will be decoded as UTF-8 strings. However, if this option is true,\n     * string values will be returned as Uint8Arrays without additional decoding.\n     *\n     * This is useful if the strings may contain invalid UTF-8 sequences.\n     *\n     * Note that this option only applies to string values, not map keys. Additionally, when\n     * enabled, raw string length is limited by the maxBinLength option.\n     */\n    rawStrings: boolean;\n\n    /**\n     * Maximum string length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxStrLength: number;\n    /**\n     * Maximum binary length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxBinLength: number;\n    /**\n     * Maximum array length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxArrayLength: number;\n    /**\n     * Maximum map length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxMapLength: number;\n    /**\n     * Maximum extension length.\n     *\n     * Defaults to 4_294_967_295 (UINT32_MAX).\n     */\n    maxExtLength: number;\n\n    /**\n     * An object key decoder. Defaults to the shared instance of {@link CachedKeyDecoder}.\n     * `null` is a special value to disable the use of the key decoder at all.\n     */\n    keyDecoder: KeyDecoder | null;\n\n    /**\n     * A function to convert decoded map key to a valid JS key type.\n     * \n     * Defaults to a function that throws an error if the key is not a string or a number.\n     */\n    mapKeyConverter: (key: unknown) => MapKeyType;\n  }>\n> &\n  ContextOf<ContextType>;\n\nconst STATE_ARRAY = \"array\";\nconst STATE_MAP_KEY = \"map_key\";\nconst STATE_MAP_VALUE = \"map_value\";\n\ntype MapKeyType = string | number;\n\nconst mapKeyConverter = (key: unknown): MapKeyType => {\n  if (typeof key === \"string\" || typeof key === \"number\") {\n    return key;\n  }\n  throw new DecodeError(\"The type of key must be string or number but \" + typeof key);\n};\n\ntype StackMapState = {\n  type: typeof STATE_MAP_KEY | typeof STATE_MAP_VALUE;\n  size: number;\n  key: MapKeyType | null;\n  readCount: number;\n  map: Record<string, unknown>;\n};\n\ntype StackArrayState = {\n  type: typeof STATE_ARRAY;\n  size: number;\n  array: Array<unknown>;\n  position: number;\n};\n\nclass StackPool {\n  private readonly stack: Array<StackState> = [];\n  private stackHeadPosition = -1;\n\n  public get length(): number {\n    return this.stackHeadPosition + 1;\n  }\n\n  public top(): StackState | undefined {\n    return this.stack[this.stackHeadPosition];\n  }\n\n  public pushArrayState(size: number) {\n    const state = this.getUninitializedStateFromPool() as StackArrayState;\n\n    state.type = STATE_ARRAY;\n    state.position = 0;\n    state.size = size;\n    state.array = new Array(size);\n  }\n\n  public pushMapState(size: number) {\n    const state = this.getUninitializedStateFromPool() as StackMapState;\n\n    state.type = STATE_MAP_KEY;\n    state.readCount = 0;\n    state.size = size;\n    state.map = {};\n  }\n\n  private getUninitializedStateFromPool() {\n    this.stackHeadPosition++;\n\n    if (this.stackHeadPosition === this.stack.length) {\n      const partialState: Partial<StackState> = {\n        type: undefined,\n        size: 0,\n        array: undefined,\n        position: 0,\n        readCount: 0,\n        map: undefined,\n        key: null,\n      };\n\n      this.stack.push(partialState as StackState);\n    }\n\n    return this.stack[this.stackHeadPosition];\n  }\n\n  public release(state: StackState): void {\n    const topStackState = this.stack[this.stackHeadPosition];\n\n    if (topStackState !== state) {\n      throw new Error(\"Invalid stack state. Released state is not on top of the stack.\");\n    }\n\n    if (state.type === STATE_ARRAY) {\n      const partialState = state as Partial<StackArrayState>;\n      partialState.size = 0;\n      partialState.array = undefined;\n      partialState.position = 0;\n      partialState.type = undefined;\n    }\n\n    if (state.type === STATE_MAP_KEY || state.type === STATE_MAP_VALUE) {\n      const partialState = state as Partial<StackMapState>;\n      partialState.size = 0;\n      partialState.map = undefined;\n      partialState.readCount = 0;\n      partialState.type = undefined;\n    }\n\n    this.stackHeadPosition--;\n  }\n\n  public reset(): void {\n    this.stack.length = 0;\n    this.stackHeadPosition = -1;\n  }\n}\n\ntype StackState = StackArrayState | StackMapState;\n\nconst HEAD_BYTE_REQUIRED = -1;\n\nconst EMPTY_VIEW = new DataView<ArrayBufferLike>(new ArrayBuffer(0));\nconst EMPTY_BYTES = new Uint8Array<ArrayBufferLike>(EMPTY_VIEW.buffer);\n\ntry {\n  // IE11: The spec says it should throw RangeError,\n  // IE11: but in IE11 it throws TypeError.\n  EMPTY_VIEW.getInt8(0);\n} catch (e) {\n  if (!(e instanceof RangeError)) {\n    throw new Error(\n      \"This module is not supported in the current JavaScript engine because DataView does not throw RangeError on out-of-bounds access\",\n    );\n  }\n}\n\nconst MORE_DATA = new RangeError(\"Insufficient data\");\n\nconst sharedCachedKeyDecoder = new CachedKeyDecoder();\n\nexport class Decoder<ContextType = undefined> {\n  private readonly extensionCodec: ExtensionCodecType<ContextType>;\n  private readonly context: ContextType;\n  private readonly useBigInt64: boolean;\n  private readonly rawStrings: boolean;\n  private readonly maxStrLength: number;\n  private readonly maxBinLength: number;\n  private readonly maxArrayLength: number;\n  private readonly maxMapLength: number;\n  private readonly maxExtLength: number;\n  private readonly keyDecoder: KeyDecoder | null;\n  private readonly mapKeyConverter: (key: unknown) => MapKeyType;\n\n  private totalPos = 0;\n  private pos = 0;\n\n  private view = EMPTY_VIEW;\n  private bytes = EMPTY_BYTES;\n  private headByte = HEAD_BYTE_REQUIRED;\n  private readonly stack = new StackPool();\n\n  private entered = false;\n\n  public constructor(options?: DecoderOptions<ContextType>) {\n    this.extensionCodec = options?.extensionCodec ?? (ExtensionCodec.defaultCodec as ExtensionCodecType<ContextType>);\n    this.context = (options as { context: ContextType } | undefined)?.context as ContextType; // needs a type assertion because EncoderOptions has no context property when ContextType is undefined\n\n    this.useBigInt64 = options?.useBigInt64 ?? false;\n    this.rawStrings = options?.rawStrings ?? false;\n    this.maxStrLength = options?.maxStrLength ?? UINT32_MAX;\n    this.maxBinLength = options?.maxBinLength ?? UINT32_MAX;\n    this.maxArrayLength = options?.maxArrayLength ?? UINT32_MAX;\n    this.maxMapLength = options?.maxMapLength ?? UINT32_MAX;\n    this.maxExtLength = options?.maxExtLength ?? UINT32_MAX;\n    this.keyDecoder = options?.keyDecoder !== undefined ? options.keyDecoder : sharedCachedKeyDecoder;\n    this.mapKeyConverter = options?.mapKeyConverter ?? mapKeyConverter;\n  }\n\n  private clone(): Decoder<ContextType> {\n    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n    return new Decoder({\n      extensionCodec: this.extensionCodec,\n      context: this.context,\n      useBigInt64: this.useBigInt64,\n      rawStrings: this.rawStrings,\n      maxStrLength: this.maxStrLength,\n      maxBinLength: this.maxBinLength,\n      maxArrayLength: this.maxArrayLength,\n      maxMapLength: this.maxMapLength,\n      maxExtLength: this.maxExtLength,\n      keyDecoder: this.keyDecoder,\n    } as any);\n  }\n\n  private reinitializeState() {\n    this.totalPos = 0;\n    this.headByte = HEAD_BYTE_REQUIRED;\n    this.stack.reset();\n\n    // view, bytes, and pos will be re-initialized in setBuffer()\n  }\n\n  private setBuffer(buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike): void {\n    const bytes = ensureUint8Array(buffer);\n    this.bytes = bytes;\n    this.view = new DataView(bytes.buffer, bytes.byteOffset, bytes.byteLength);\n    this.pos = 0;\n  }\n\n  private appendBuffer(buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike): void {\n    if (this.headByte === HEAD_BYTE_REQUIRED && !this.hasRemaining(1)) {\n      this.setBuffer(buffer);\n    } else {\n      const remainingData = this.bytes.subarray(this.pos);\n      const newData = ensureUint8Array(buffer);\n\n      // concat remainingData + newData\n      const newBuffer = new Uint8Array(remainingData.length + newData.length);\n      newBuffer.set(remainingData);\n      newBuffer.set(newData, remainingData.length);\n      this.setBuffer(newBuffer);\n    }\n  }\n\n  private hasRemaining(size: number) {\n    return this.view.byteLength - this.pos >= size;\n  }\n\n  private createExtraByteError(posToShow: number): Error {\n    const { view, pos } = this;\n    return new RangeError(`Extra ${view.byteLength - pos} of ${view.byteLength} byte(s) found at buffer[${posToShow}]`);\n  }\n\n  /**\n   * @throws {@link DecodeError}\n   * @throws {@link RangeError}\n   */\n  public decode(buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike): unknown {\n    if (this.entered) {\n      const instance = this.clone();\n      return instance.decode(buffer);\n    }\n\n    try {\n      this.entered = true;\n\n      this.reinitializeState();\n      this.setBuffer(buffer);\n\n      const object = this.doDecodeSync();\n      if (this.hasRemaining(1)) {\n        throw this.createExtraByteError(this.pos);\n      }\n      return object;\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  public *decodeMulti(buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike): Generator<unknown, void, unknown> {\n    if (this.entered) {\n      const instance = this.clone();\n      yield* instance.decodeMulti(buffer);\n      return;\n    }\n\n    try {\n      this.entered = true;\n\n      this.reinitializeState();\n      this.setBuffer(buffer);\n\n      while (this.hasRemaining(1)) {\n        yield this.doDecodeSync();\n      }\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  public async decodeAsync(stream: AsyncIterable<ArrayLike<number> | ArrayBufferView | ArrayBufferLike>): Promise<unknown> {\n    if (this.entered) {\n      const instance = this.clone();\n      return instance.decodeAsync(stream);\n    }\n\n    try {\n      this.entered = true;\n\n      let decoded = false;\n      let object: unknown;\n      for await (const buffer of stream) {\n        if (decoded) {\n          this.entered = false;\n          throw this.createExtraByteError(this.totalPos);\n        }\n\n        this.appendBuffer(buffer);\n\n        try {\n          object = this.doDecodeSync();\n          decoded = true;\n        } catch (e) {\n          if (!(e instanceof RangeError)) {\n            throw e; // rethrow\n          }\n          // fallthrough\n        }\n        this.totalPos += this.pos;\n      }\n\n      if (decoded) {\n        if (this.hasRemaining(1)) {\n          throw this.createExtraByteError(this.totalPos);\n        }\n        return object;\n      }\n\n      const { headByte, pos, totalPos } = this;\n      throw new RangeError(\n        `Insufficient data in parsing ${prettyByte(headByte)} at ${totalPos} (${pos} in the current buffer)`,\n      );\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  public decodeArrayStream(\n    stream: AsyncIterable<ArrayLike<number> | ArrayBufferView | ArrayBufferLike>,\n  ): AsyncGenerator<unknown, void, unknown> {\n    return this.decodeMultiAsync(stream, true);\n  }\n\n  public decodeStream(stream: AsyncIterable<ArrayLike<number> | ArrayBufferView | ArrayBufferLike>): AsyncGenerator<unknown, void, unknown> {\n    return this.decodeMultiAsync(stream, false);\n  }\n\n  private async *decodeMultiAsync(stream: AsyncIterable<ArrayLike<number> | ArrayBufferView | ArrayBufferLike>, isArray: boolean): AsyncGenerator<unknown, void, unknown> {\n    if (this.entered) {\n      const instance = this.clone();\n      yield* instance.decodeMultiAsync(stream, isArray);\n      return;\n    }\n\n    try {\n      this.entered = true;\n\n      let isArrayHeaderRequired = isArray;\n      let arrayItemsLeft = -1;\n\n      for await (const buffer of stream) {\n        if (isArray && arrayItemsLeft === 0) {\n          throw this.createExtraByteError(this.totalPos);\n        }\n\n        this.appendBuffer(buffer);\n\n        if (isArrayHeaderRequired) {\n          arrayItemsLeft = this.readArraySize();\n          isArrayHeaderRequired = false;\n          this.complete();\n        }\n\n        try {\n          while (true) {\n            yield this.doDecodeSync();\n            if (--arrayItemsLeft === 0) {\n              break;\n            }\n          }\n        } catch (e) {\n          if (!(e instanceof RangeError)) {\n            throw e; // rethrow\n          }\n          // fallthrough\n        }\n        this.totalPos += this.pos;\n      }\n    } finally {\n      this.entered = false;\n    }\n  }\n\n  private doDecodeSync(): unknown {\n    DECODE: while (true) {\n      const headByte = this.readHeadByte();\n      let object: unknown;\n\n      if (headByte >= 0xe0) {\n        // negative fixint (111x xxxx) 0xe0 - 0xff\n        object = headByte - 0x100;\n      } else if (headByte < 0xc0) {\n        if (headByte < 0x80) {\n          // positive fixint (0xxx xxxx) 0x00 - 0x7f\n          object = headByte;\n        } else if (headByte < 0x90) {\n          // fixmap (1000 xxxx) 0x80 - 0x8f\n          const size = headByte - 0x80;\n          if (size !== 0) {\n            this.pushMapState(size);\n            this.complete();\n            continue DECODE;\n          } else {\n            object = {};\n          }\n        } else if (headByte < 0xa0) {\n          // fixarray (1001 xxxx) 0x90 - 0x9f\n          const size = headByte - 0x90;\n          if (size !== 0) {\n            this.pushArrayState(size);\n            this.complete();\n            continue DECODE;\n          } else {\n            object = [];\n          }\n        } else {\n          // fixstr (101x xxxx) 0xa0 - 0xbf\n          const byteLength = headByte - 0xa0;\n          object = this.decodeString(byteLength, 0);\n        }\n      } else if (headByte === 0xc0) {\n        // nil\n        object = null;\n      } else if (headByte === 0xc2) {\n        // false\n        object = false;\n      } else if (headByte === 0xc3) {\n        // true\n        object = true;\n      } else if (headByte === 0xca) {\n        // float 32\n        object = this.readF32();\n      } else if (headByte === 0xcb) {\n        // float 64\n        object = this.readF64();\n      } else if (headByte === 0xcc) {\n        // uint 8\n        object = this.readU8();\n      } else if (headByte === 0xcd) {\n        // uint 16\n        object = this.readU16();\n      } else if (headByte === 0xce) {\n        // uint 32\n        object = this.readU32();\n      } else if (headByte === 0xcf) {\n        // uint 64\n        if (this.useBigInt64) {\n          object = this.readU64AsBigInt();\n        } else {\n          object = this.readU64();\n        }\n      } else if (headByte === 0xd0) {\n        // int 8\n        object = this.readI8();\n      } else if (headByte === 0xd1) {\n        // int 16\n        object = this.readI16();\n      } else if (headByte === 0xd2) {\n        // int 32\n        object = this.readI32();\n      } else if (headByte === 0xd3) {\n        // int 64\n        if (this.useBigInt64) {\n          object = this.readI64AsBigInt();\n        } else {\n          object = this.readI64();\n        }\n      } else if (headByte === 0xd9) {\n        // str 8\n        const byteLength = this.lookU8();\n        object = this.decodeString(byteLength, 1);\n      } else if (headByte === 0xda) {\n        // str 16\n        const byteLength = this.lookU16();\n        object = this.decodeString(byteLength, 2);\n      } else if (headByte === 0xdb) {\n        // str 32\n        const byteLength = this.lookU32();\n        object = this.decodeString(byteLength, 4);\n      } else if (headByte === 0xdc) {\n        // array 16\n        const size = this.readU16();\n        if (size !== 0) {\n          this.pushArrayState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = [];\n        }\n      } else if (headByte === 0xdd) {\n        // array 32\n        const size = this.readU32();\n        if (size !== 0) {\n          this.pushArrayState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = [];\n        }\n      } else if (headByte === 0xde) {\n        // map 16\n        const size = this.readU16();\n        if (size !== 0) {\n          this.pushMapState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = {};\n        }\n      } else if (headByte === 0xdf) {\n        // map 32\n        const size = this.readU32();\n        if (size !== 0) {\n          this.pushMapState(size);\n          this.complete();\n          continue DECODE;\n        } else {\n          object = {};\n        }\n      } else if (headByte === 0xc4) {\n        // bin 8\n        const size = this.lookU8();\n        object = this.decodeBinary(size, 1);\n      } else if (headByte === 0xc5) {\n        // bin 16\n        const size = this.lookU16();\n        object = this.decodeBinary(size, 2);\n      } else if (headByte === 0xc6) {\n        // bin 32\n        const size = this.lookU32();\n        object = this.decodeBinary(size, 4);\n      } else if (headByte === 0xd4) {\n        // fixext 1\n        object = this.decodeExtension(1, 0);\n      } else if (headByte === 0xd5) {\n        // fixext 2\n        object = this.decodeExtension(2, 0);\n      } else if (headByte === 0xd6) {\n        // fixext 4\n        object = this.decodeExtension(4, 0);\n      } else if (headByte === 0xd7) {\n        // fixext 8\n        object = this.decodeExtension(8, 0);\n      } else if (headByte === 0xd8) {\n        // fixext 16\n        object = this.decodeExtension(16, 0);\n      } else if (headByte === 0xc7) {\n        // ext 8\n        const size = this.lookU8();\n        object = this.decodeExtension(size, 1);\n      } else if (headByte === 0xc8) {\n        // ext 16\n        const size = this.lookU16();\n        object = this.decodeExtension(size, 2);\n      } else if (headByte === 0xc9) {\n        // ext 32\n        const size = this.lookU32();\n        object = this.decodeExtension(size, 4);\n      } else {\n        throw new DecodeError(`Unrecognized type byte: ${prettyByte(headByte)}`);\n      }\n\n      this.complete();\n\n      const stack = this.stack;\n      while (stack.length > 0) {\n        // arrays and maps\n        const state = stack.top()!;\n        if (state.type === STATE_ARRAY) {\n          state.array[state.position] = object;\n          state.position++;\n          if (state.position === state.size) {\n            object = state.array;\n            stack.release(state);\n          } else {\n            continue DECODE;\n          }\n        } else if (state.type === STATE_MAP_KEY) {\n          if (object === \"__proto__\") {\n            throw new DecodeError(\"The key __proto__ is not allowed\");\n          }\n\n          state.key = this.mapKeyConverter(object);\n          state.type = STATE_MAP_VALUE;\n          continue DECODE;\n        } else {\n          // it must be `state.type === State.MAP_VALUE` here\n\n          state.map[state.key!] = object;\n          state.readCount++;\n\n          if (state.readCount === state.size) {\n            object = state.map;\n            stack.release(state);\n          } else {\n            state.key = null;\n            state.type = STATE_MAP_KEY;\n            continue DECODE;\n          }\n        }\n      }\n\n      return object;\n    }\n  }\n\n  private readHeadByte(): number {\n    if (this.headByte === HEAD_BYTE_REQUIRED) {\n      this.headByte = this.readU8();\n      // console.log(\"headByte\", prettyByte(this.headByte));\n    }\n\n    return this.headByte;\n  }\n\n  private complete(): void {\n    this.headByte = HEAD_BYTE_REQUIRED;\n  }\n\n  private readArraySize(): number {\n    const headByte = this.readHeadByte();\n\n    switch (headByte) {\n      case 0xdc:\n        return this.readU16();\n      case 0xdd:\n        return this.readU32();\n      default: {\n        if (headByte < 0xa0) {\n          return headByte - 0x90;\n        } else {\n          throw new DecodeError(`Unrecognized array type byte: ${prettyByte(headByte)}`);\n        }\n      }\n    }\n  }\n\n  private pushMapState(size: number) {\n    if (size > this.maxMapLength) {\n      throw new DecodeError(`Max length exceeded: map length (${size}) > maxMapLengthLength (${this.maxMapLength})`);\n    }\n\n    this.stack.pushMapState(size);\n  }\n\n  private pushArrayState(size: number) {\n    if (size > this.maxArrayLength) {\n      throw new DecodeError(`Max length exceeded: array length (${size}) > maxArrayLength (${this.maxArrayLength})`);\n    }\n\n    this.stack.pushArrayState(size);\n  }\n\n  private decodeString(byteLength: number, headerOffset: number): string | Uint8Array {\n    if (!this.rawStrings || this.stateIsMapKey()) {\n      return this.decodeUtf8String(byteLength, headerOffset);\n    }\n    return this.decodeBinary(byteLength, headerOffset);\n  }\n\n  /**\n   * @throws {@link RangeError}\n   */\n  private decodeUtf8String(byteLength: number, headerOffset: number): string {\n    if (byteLength > this.maxStrLength) {\n      throw new DecodeError(\n        `Max length exceeded: UTF-8 byte length (${byteLength}) > maxStrLength (${this.maxStrLength})`,\n      );\n    }\n\n    if (this.bytes.byteLength < this.pos + headerOffset + byteLength) {\n      throw MORE_DATA;\n    }\n\n    const offset = this.pos + headerOffset;\n    let object: string;\n    if (this.stateIsMapKey() && this.keyDecoder?.canBeCached(byteLength)) {\n      object = this.keyDecoder.decode(this.bytes, offset, byteLength);\n    } else {\n      object = utf8Decode(this.bytes, offset, byteLength);\n    }\n    this.pos += headerOffset + byteLength;\n    return object;\n  }\n\n  private stateIsMapKey(): boolean {\n    if (this.stack.length > 0) {\n      const state = this.stack.top()!;\n      return state.type === STATE_MAP_KEY;\n    }\n    return false;\n  }\n\n  /**\n   * @throws {@link RangeError}\n   */\n  private decodeBinary(byteLength: number, headOffset: number): Uint8Array {\n    if (byteLength > this.maxBinLength) {\n      throw new DecodeError(`Max length exceeded: bin length (${byteLength}) > maxBinLength (${this.maxBinLength})`);\n    }\n\n    if (!this.hasRemaining(byteLength + headOffset)) {\n      throw MORE_DATA;\n    }\n\n    const offset = this.pos + headOffset;\n    const object = this.bytes.subarray(offset, offset + byteLength);\n    this.pos += headOffset + byteLength;\n    return object;\n  }\n\n  private decodeExtension(size: number, headOffset: number): unknown {\n    if (size > this.maxExtLength) {\n      throw new DecodeError(`Max length exceeded: ext length (${size}) > maxExtLength (${this.maxExtLength})`);\n    }\n\n    const extType = this.view.getInt8(this.pos + headOffset);\n    const data = this.decodeBinary(size, headOffset + 1 /* extType */);\n    return this.extensionCodec.decode(data, extType, this.context);\n  }\n\n  private lookU8() {\n    return this.view.getUint8(this.pos);\n  }\n\n  private lookU16() {\n    return this.view.getUint16(this.pos);\n  }\n\n  private lookU32() {\n    return this.view.getUint32(this.pos);\n  }\n\n  private readU8(): number {\n    const value = this.view.getUint8(this.pos);\n    this.pos++;\n    return value;\n  }\n\n  private readI8(): number {\n    const value = this.view.getInt8(this.pos);\n    this.pos++;\n    return value;\n  }\n\n  private readU16(): number {\n    const value = this.view.getUint16(this.pos);\n    this.pos += 2;\n    return value;\n  }\n\n  private readI16(): number {\n    const value = this.view.getInt16(this.pos);\n    this.pos += 2;\n    return value;\n  }\n\n  private readU32(): number {\n    const value = this.view.getUint32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readI32(): number {\n    const value = this.view.getInt32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readU64(): number {\n    const value = getUint64(this.view, this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readI64(): number {\n    const value = getInt64(this.view, this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readU64AsBigInt(): bigint {\n    const value = this.view.getBigUint64(this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readI64AsBigInt(): bigint {\n    const value = this.view.getBigInt64(this.pos);\n    this.pos += 8;\n    return value;\n  }\n\n  private readF32() {\n    const value = this.view.getFloat32(this.pos);\n    this.pos += 4;\n    return value;\n  }\n\n  private readF64() {\n    const value = this.view.getFloat64(this.pos);\n    this.pos += 8;\n    return value;\n  }\n}\n", "import { utf8DecodeJs } from \"./utils/utf8.ts\";\n\nconst DEFAULT_MAX_KEY_LENGTH = 16;\nconst DEFAULT_MAX_LENGTH_PER_KEY = 16;\n\nexport interface KeyDecoder {\n  canBeCached(byteLength: number): boolean;\n  decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string;\n}\ninterface KeyCacheRecord {\n  readonly bytes: Uint8Array;\n  readonly str: string;\n}\n\nexport class CachedKeyDecoder implements KeyDecoder {\n  hit = 0;\n  miss = 0;\n  private readonly caches: Array<Array<KeyCacheRecord>>;\n  private readonly maxKeyLength: number;\n  private readonly maxLengthPerKey: number;\n\n  constructor(maxKeyLength = DEFAULT_MAX_KEY_LENGTH, maxLengthPerKey = DEFAULT_MAX_LENGTH_PER_KEY) {\n    this.maxKeyLength = maxKeyLength;\n    this.maxLengthPerKey = maxLengthPerKey;\n\n    // avoid `new Array(N)`, which makes a sparse array,\n    // because a sparse array is typically slower than a non-sparse array.\n    this.caches = [];\n    for (let i = 0; i < this.maxKeyLength; i++) {\n      this.caches.push([]);\n    }\n  }\n\n  public canBeCached(byteLength: number): boolean {\n    return byteLength > 0 && byteLength <= this.maxKeyLength;\n  }\n\n  private find(bytes: Uint8Array, inputOffset: number, byteLength: number): string | null {\n    const records = this.caches[byteLength - 1]!;\n\n    FIND_CHUNK: for (const record of records) {\n      const recordBytes = record.bytes;\n\n      for (let j = 0; j < byteLength; j++) {\n        if (recordBytes[j] !== bytes[inputOffset + j]) {\n          continue FIND_CHUNK;\n        }\n      }\n      return record.str;\n    }\n    return null;\n  }\n\n  private store(bytes: Uint8Array, value: string) {\n    const records = this.caches[bytes.length - 1]!;\n    const record: KeyCacheRecord = { bytes, str: value };\n\n    if (records.length >= this.maxLengthPerKey) {\n      // `records` are full!\n      // Set `record` to an arbitrary position.\n      records[(Math.random() * records.length) | 0] = record;\n    } else {\n      records.push(record);\n    }\n  }\n\n  public decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string {\n    const cachedValue = this.find(bytes, inputOffset, byteLength);\n    if (cachedValue != null) {\n      this.hit++;\n      return cachedValue;\n    }\n    this.miss++;\n\n    const str = utf8DecodeJs(bytes, inputOffset, byteLength);\n    // Ensure to copy a slice of bytes because the bytes may be a NodeJS Buffer and Buffer#slice() returns a reference to its internal ArrayBuffer.\n    const slicedCopyOfBytes = Uint8Array.prototype.slice.call(bytes, inputOffset, inputOffset + byteLength);\n    this.store(slicedCopyOfBytes, str);\n    return str;\n  }\n}\n", "import { Decoder } from \"./Decoder.ts\";\nimport type { DecoderOptions } from \"./Decoder.ts\";\nimport type { SplitUndefined } from \"./context.ts\";\n\n/**\n * It decodes a single MessagePack object in a buffer.\n *\n * This is a synchronous decoding function.\n * See other variants for asynchronous decoding: {@link decodeAsync}, {@link decodeStream}, or {@link decodeArrayStream}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decode<ContextType = undefined>(\n  buffer: ArrayLike<number> | ArrayBufferView | ArrayBufferLike,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): unknown {\n  const decoder = new Decoder(options);\n  return decoder.decode(buffer);\n}\n\n/**\n * It decodes multiple MessagePack objects in a buffer.\n * This is corresponding to {@link decodeMultiStream}.\n *\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decodeMulti<ContextType = undefined>(\n  buffer: ArrayLike<number> | BufferSource,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): Generator<unknown, void, unknown> {\n  const decoder = new Decoder(options);\n  return decoder.decodeMulti(buffer);\n}\n", "// utility for whatwg streams\n\n// The living standard of whatwg streams says\n// ReadableStream is also AsyncIterable, but\n// as of June 2019, no browser implements it.\n// See https://streams.spec.whatwg.org/ for details\nexport type ReadableStreamLike<T> = AsyncIterable<T> | ReadableStream<T>;\n\nexport function isAsyncIterable<T>(object: ReadableStreamLike<T>): object is AsyncIterable<T> {\n  return (object as any)[Symbol.asyncIterator] != null;\n}\n\nexport async function* asyncIterableFromStream<T>(stream: ReadableStream<T>): AsyncIterable<T> {\n  const reader = stream.getReader();\n\n  try {\n    while (true) {\n      const { done, value } = await reader.read();\n      if (done) {\n        return;\n      }\n      yield value;\n    }\n  } finally {\n    reader.releaseLock();\n  }\n}\n\nexport function ensureAsyncIterable<T>(streamLike: ReadableStreamLike<T>): AsyncIterable<T> {\n  if (isAsyncIterable(streamLike)) {\n    return streamLike;\n  } else {\n    return asyncIterableFromStream(streamLike);\n  }\n}\n", "import { Decoder } from \"./Decoder.ts\";\nimport { ensureAsyncIterable } from \"./utils/stream.ts\";\nimport type { DecoderOptions } from \"./Decoder.ts\";\nimport type { ReadableStreamLike } from \"./utils/stream.ts\";\nimport type { SplitUndefined } from \"./context.ts\";\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport async function decodeAsync<ContextType = undefined>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): Promise<unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n  const decoder = new Decoder(options);\n  return decoder.decodeAsync(stream);\n}\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decodeArrayStream<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): AsyncGenerator<unknown, void, unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n  const decoder = new Decoder(options);\n  return decoder.decodeArrayStream(stream);\n}\n\n/**\n * @throws {@link RangeError} if the buffer is incomplete, including the case where the buffer is empty.\n * @throws {@link DecodeError} if the buffer contains invalid data.\n */\nexport function decodeMultiStream<ContextType>(\n  streamLike: ReadableStreamLike<ArrayLike<number> | BufferSource>,\n  options?: DecoderOptions<SplitUndefined<ContextType>>,\n): AsyncGenerator<unknown, void, unknown> {\n  const stream = ensureAsyncIterable(streamLike);\n  const decoder = new Decoder(options);\n  return decoder.decodeStream(stream);\n}\n"], "names": ["root", "factory", "exports", "module", "define", "amd", "this", "__webpack_require__", "definition", "key", "o", "Object", "defineProperty", "enumerable", "get", "obj", "prop", "prototype", "hasOwnProperty", "call", "Symbol", "toStringTag", "value", "sharedTextEncoder", "TextEncoder", "utf8DecodeJs", "bytes", "inputOffset", "byteLength", "offset", "end", "units", "result", "byte1", "byte2", "push", "byte3", "unit", "length", "String", "fromCharCode", "sharedTextDecoder", "TextDecoder", "ExtData", "constructor", "type", "data", "DecodeError", "Error", "message", "super", "proto", "create", "setPrototypeOf", "configurable", "name", "UINT32_MAX", "setInt64", "view", "high", "Math", "floor", "low", "setUint32", "getInt64", "getInt32", "getUint32", "EXT_TIMESTAMP", "TIMESTAMP32_MAX_SEC", "TIMESTAMP64_MAX_SEC", "encodeTimeSpecToTimestamp", "sec", "nsec", "rv", "Uint8Array", "DataView", "buffer", "secHigh", "secLow", "encodeDateToTimeSpec", "date", "msec", "getTime", "nsecInSec", "encodeTimestampExtension", "object", "Date", "decodeTimestampToTimeSpec", "byteOffset", "nsec30AndSecHigh2", "decodeTimestampExtension", "timeSpec", "timestampExtension", "encode", "decode", "ExtensionCodec", "builtInEncoders", "builtInDecoders", "encoders", "decoders", "register", "index", "tryToEncode", "context", "i", "encodeExt", "decodeExt", "ensureUint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "SharedArrayBuffer", "isArrayBufferLike", "from", "defaultCodec", "Encoder", "options", "entered", "extensionCodec", "useBigInt64", "max<PERSON><PERSON><PERSON>", "initialBufferSize", "sortKeys", "forceFloat32", "ignoreUndefined", "forceIntegerToFloat", "pos", "clone", "reinitializeState", "encodeSharedRef", "doEncode", "subarray", "slice", "depth", "encodeNil", "encodeBoolean", "encodeNumberAsFloat", "encodeNumber", "encodeString", "encodeBigInt64", "encodeObject", "ensureBufferSizeToWrite", "sizeToWrite", "requiredSize", "resize<PERSON>uffer", "newSize", "new<PERSON>uffer", "newBytes", "newView", "set", "writeU8", "Number", "isSafeInteger", "writeU16", "writeU32", "writeU64", "writeI8", "writeI16", "writeI32", "writeI64", "writeF32", "writeF64", "BigInt", "writeBigUint64", "writeBigInt64", "writeStringHeader", "str", "str<PERSON><PERSON><PERSON>", "charCodeAt", "extra", "utf8Count", "output", "outputOffset", "encodeInto", "utf8EncodeTE", "utf8EncodeJs", "ext", "encodeExtension", "Array", "isArray", "encodeArray", "encodeBinary", "toString", "apply", "encodeMap", "size", "writeU8a", "item", "countWithoutUndefined", "keys", "count", "undefined", "sort", "setUint8", "values", "setInt8", "setUint16", "setInt16", "setInt32", "setFloat32", "setFloat64", "setUint64", "setBigUint64", "setBigInt64", "prettyByte", "byte", "abs", "padStart", "STATE_ARRAY", "STATE_MAP_KEY", "STATE_MAP_VALUE", "mapKeyConverter", "StackPool", "stack", "stackHeadPosition", "top", "pushArrayState", "state", "getUninitializedStateFromPool", "position", "array", "pushMapState", "readCount", "map", "partialState", "release", "reset", "EMPTY_VIEW", "EMPTY_BYTES", "getInt8", "e", "RangeError", "MORE_DATA", "sharedCachedKeyDecoder", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hit", "miss", "caches", "canBeCached", "find", "records", "FIND_CHUNK", "record", "recordBytes", "j", "store", "random", "cachedValue", "slicedCopyOfBytes", "Decoder", "totalPos", "headByte", "rawStrings", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "max<PERSON><PERSON><PERSON><PERSON><PERSON>", "maxExt<PERSON><PERSON><PERSON>", "keyDecoder", "<PERSON><PERSON><PERSON><PERSON>", "append<PERSON><PERSON>er", "hasRemaining", "remainingData", "newData", "createExtraByteError", "posToShow", "doDecodeSync", "decodeMulti", "instance", "decodeAsync", "stream", "decoded", "decodeArrayStream", "decodeMultiAsync", "decodeStream", "isArrayHeaderRequired", "arrayItemsLeft", "readArraySize", "complete", "DECODE", "readHeadByte", "decodeString", "readF32", "readF64", "readU8", "readU16", "readU32", "readU64AsBigInt", "readU64", "readI8", "readI16", "readI32", "readI64AsBigInt", "readI64", "lookU8", "lookU16", "lookU32", "decodeBinary", "decodeExtension", "headerOffset", "stateIsMapKey", "decodeUtf8String", "stringBytes", "utf8DecodeTD", "utf8Decode", "headOffset", "extType", "getUint8", "getUint16", "getInt16", "getBigUint64", "getBigInt64", "getFloat32", "getFloat64", "ensureAsyncIterable", "streamLike", "asyncIterator", "async", "reader", "<PERSON><PERSON><PERSON><PERSON>", "done", "read", "releaseLock", "asyncIterableFromStream", "decodeMultiStream"], "sourceRoot": ""}